namespace UnifiedHub.App.Extensions;

public static class ConfigureWebHostExtension
{
    public static ConfigureWebHostBuilder AddSentry(this ConfigureWebHostBuilder webHost,
        IWebHostEnvironment environment)
    {
        if (environment.IsStaging() || environment.IsProduction())
        {
            webHost.UseSentry(o =>
            {
                o.Dsn = "https://<EMAIL>/2";
                // When configuring for the first time, to see what the SDK is doing:
                o.Debug = true;
                o.Environment = environment.EnvironmentName.ToLower();
                // Set TracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring.
                // We recommend adjusting this value in production.
                o.TracesSampleRate = 1.0;

                o.AddEntityFramework();
            });
        }

        return webHost;
    }
}
