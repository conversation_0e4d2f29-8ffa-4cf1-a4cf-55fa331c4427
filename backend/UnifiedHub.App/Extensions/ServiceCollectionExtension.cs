using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using Mapster;
using MapsterMapper;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Translation.Extensions;
using UnifiedHub.App.CultureProviders;
using UnifiedHub.App.ExceptionFilters;
using UnifiedHub.App.JsonConverters;
using UnifiedHub.App.ModelBinders;
using UnifiedHub.App.Services;
using UnifiedHub.Core.Constants;
using UnifiedHub.Core.Misc;
using UnifiedHub.Core.Services;
using UnifiedHub.Core.Services.Caching;
using UnifiedHub.Core.Services.PdfGenerator;
using UnifiedHub.Persistence.Extensions;
using UnifiedHub.Servicing.Extensions;

namespace UnifiedHub.App.Extensions;

public static class ServiceCollectionExtension
{
    public static IServiceCollection AddCore(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddPersistence(configuration);

        services.AddHttpContextAccessor();
        services.AddControllers(opts =>
            {
                // Serialize enums into strings rather than digits (in query strings).
                opts.ModelBinderProviders.Insert(0, new SnakeCaseEnumModelBinderProvider());

                opts.Filters.Add<AppExceptionFilter>();
            })
            .AddJsonOptions(opts =>
            {
                // Serialize enums into strings rather than digits.
                var enumConverter = new JsonStringEnumConverter(JsonNamingPolicy.SnakeCaseLower, false);
                opts.JsonSerializerOptions.Converters.Add(enumConverter);


                opts.JsonSerializerOptions.Converters.Add(new NullableIntJsonConverter());
                opts.JsonSerializerOptions.Converters.Add(new NullableDecimalJsonConverter());
            });

        // Disable automatic required validation for non-nullable reference types in model binding
        // This prevents properties like "string Type = null!" from triggering validation errors
        // when not explicitly provided in request payloads, while still maintaining nullable reference
        // type safety at compile time
        services.Configure<MvcOptions>(options =>
        {
            options.SuppressImplicitRequiredAttributeForNonNullableReferenceTypes = true;
        });

        services.AddSignalR();

        services.AddScoped<ITimezoneProvider, TimezoneProvider>();

        services.AddSingleton(TypeAdapterConfig.GlobalSettings);

        // TODO: Why is this scoped?
        services.AddScoped<IMapper, ServiceMapper>();

        services.AddSingleton<IDateTimeProvider, DateTimeProvider>();
        services.AddSingleton<IPasswordHasher, PasswordHasher>();
        services.AddSingleton<IPdfFileGenerator, PdfFileGenerator>();

        var dbConnectionString = configuration.GetConnectionString("SqlDatabase")!;
        return services.AddSwaggerGen(opts =>
            {
                opts.CustomSchemaIds(x => x.FullName);
            })
            .AddCors()
            .AddAuth(configuration)
            .AddCaching()
            .AddLocalization(dbConnectionString)
            .AddServicingCategories(typeof(ServiceCollectionExtension).Assembly);
    }


    private static IServiceCollection AddAuth(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var jwtSettings = new JwtSettings();
        configuration.Bind(JwtSettings.SectionName, jwtSettings);

        services.AddAuthentication(defaultScheme: JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = jwtSettings.Issuer,
                    ValidAudience = jwtSettings.Audience,
                    IssuerSigningKey = new SymmetricSecurityKey(
                        Encoding.UTF8.GetBytes(jwtSettings.Secret)),
                };
            });

        return services;
    }

    private static IServiceCollection AddLocalization(this IServiceCollection services, string connectionString)
    {
        services.AddLocalization();

        services.Configure<RequestLocalizationOptions>(options =>
        {
            options.DefaultRequestCulture =
                new RequestCulture(culture: SupportedCultures.Default, SupportedCultures.Default);

            options.SupportedCultures = SupportedCultures.All;
            options.SupportedUICultures = SupportedCultures.All;

            options.RequestCultureProviders = new List<IRequestCultureProvider>(
                new[] { new HeaderRequestCultureProvider() }
            );
        });

        services.AddScoped<ILanguageService, LanguageService>();
        services.AddTranslation<LanguageService>(connectionString);

        return services;
    }

    private static IServiceCollection AddCaching(this IServiceCollection services)
    {
        services.AddMemoryCache();
        services.AddSingleton<ICache, Cache>();
        return services;
    }
}
