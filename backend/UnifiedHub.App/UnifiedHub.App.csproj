<Project Sdk="Microsoft.NET.Sdk.Web">

  <ItemGroup>
    <PackageReference Include="Elastic.Serilog.Sinks" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design"/>
    <PackageReference Include="Sentry.AspNetCore"/>
    <PackageReference Include="Sentry.EntityFramework"/>
    <PackageReference Include="Serilog.AspNetCore" />
    <PackageReference Include="Serilog.Enrichers.Environment" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Features\UnifiedHub.Fcm\UnifiedHub.Fcm.csproj" />
    <ProjectReference Include="..\Features\UnifiedHub.Flows\UnifiedHub.Flows.csproj" />
    <ProjectReference Include="..\Features\UnifiedHub.Logging\UnifiedHub.Logging.csproj" />
    <ProjectReference Include="..\Modules\Auth\UnifiedHub.Auth\UnifiedHub.Auth.csproj" />
    <ProjectReference Include="..\Modules\CourseManagement\UnifiedHub.CoursesManagement\UnifiedHub.CoursesManagement.csproj" />
    <ProjectReference Include="..\Modules\DepartmentsManagement\UnifiedHub.DepartmentsManagement\UnifiedHub.DepartmentsManagement.csproj"/>
    <ProjectReference Include="..\Modules\Foundation\UnifiedHub.Foundation\UnifiedHub.Foundation.csproj" />
    <ProjectReference Include="..\Modules\Messaging\UnifiedHub.Messaging\UnifiedHub.Messaging.csproj" />
    <ProjectReference Include="..\Modules\Recruitment\UnifiedHub.Recruitment\UnifiedHub.Recruitment.csproj" />
    <ProjectReference Include="..\Modules\RolesManagement\UnifiedHub.RolesManagement\UnifiedHub.RolesManagement.csproj"/>
    <ProjectReference Include="..\Modules\AttendanceManagement\UnifiedHub.AttendanceManagement\UnifiedHub.AttendanceManagement.csproj"/>
    <ProjectReference Include="..\Modules\EmployeesManagement\UnifiedHub.EmployeesManagement\UnifiedHub.EmployeesManagement.csproj"/>
    <ProjectReference Include="..\Modules\LeavesManagement\UnifiedHub.LeavesManagement\UnifiedHub.LeavesManagement.csproj"/>
    <ProjectReference Include="..\Modules\Payroll\UnifiedHub.Payroll\UnifiedHub.Payroll.csproj" />
    <ProjectReference Include="..\Modules\SuggestionsManagement\UnifiedHub.SuggestionsManagement\UnifiedHub.SuggestionsManagement.csproj" />
    <ProjectReference Include="..\Modules\SurveysManagement\UnifiedHub.SurveysManagement\UnifiedHub.SurveysManagement.csproj" />
    <ProjectReference Include="..\Modules\UsersManagement\UnifiedHub.UsersManagement\UnifiedHub.UsersManagement.csproj"/>
  </ItemGroup>

  <ItemGroup>
    <None Include="i18n\frontend\app\ar.json"/>
    <None Include="i18n\frontend\app\en.json"/>
    <None Include="i18n\frontend\departments\ar.json"/>
    <None Include="i18n\frontend\departments\en.json"/>
    <None Include="i18n\frontend\punches\ar.json"/>
    <None Include="i18n\frontend\punches\en.json"/>
    <None Include="i18n\frontend\schedules\ar.json"/>
    <None Include="i18n\frontend\schedules\en.json"/>
    <None Include="i18n\frontend\transactions\ar.json"/>
    <None Include="i18n\frontend\transactions\en.json"/>
    <None Include="i18n\frontend\users\ar.json"/>
    <None Include="i18n\frontend\users\en.json"/>
    <None Include="i18n\languages.json"/>
  </ItemGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="i18n\frontend\auth\ar.json"/>
    <_ContentIncludedByDefault Remove="i18n\frontend\auth\en.json"/>
    <_ContentIncludedByDefault Remove="Requests\http-client.env.json"/>
    <_ContentIncludedByDefault Remove="i18n\frontend\employment-departments\ar.json" />
    <_ContentIncludedByDefault Remove="i18n\frontend\employment-departments\en.json" />
    <_ContentIncludedByDefault Remove="i18n\frontend\military-statuses\ar.json" />
    <_ContentIncludedByDefault Remove="i18n\frontend\military-statuses\en.json" />
    <_ContentIncludedByDefault Remove="i18n\frontend\allowance-types\ar.json" />
    <_ContentIncludedByDefault Remove="i18n\frontend\allowance-types\en.json" />
  </ItemGroup>

</Project>
