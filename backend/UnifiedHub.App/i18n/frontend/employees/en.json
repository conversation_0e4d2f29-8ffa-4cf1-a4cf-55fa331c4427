{"translate_add_new_employee": "Add new employee", "translate_update_employee_details": "Update employee details", "translate_marital_status": "Marital status", "translate_personal_photo": "Personal photo", "translate_emirates_id_number": "Emirates ID number", "translate_emirates_id_expiration_date": "Emirates ID expiration date", "translate_emirates_id_file": "Emirate ID file", "translate_passport_number": "Passport number", "translate_passport_expiration_date": "Passport expiration date", "translate_passport_unified_number": "Unified number", "translate_passport_issue_authority": "Passport issue authority", "translate_passport_file": "Passport file", "translate_family_book_number": "Family book number", "translate_family_book_file": "Family book file", "translate_residency_number": "Residency number", "translate_residency_expiration_date": "Residency expiration date", "translate_residency_file": "Residency file", "translate_address": "Address", "translate_phone_number": "Phone number", "translate_relatives": "Relatives", "translate_relation": "Relations", "translate_age": "Age", "translate_date_of_birth_from": "Date of birth (from)", "translate_date_of_birth_to": "Date of birth (to)", "translate_employee_details": "Employee details", "translate_employment_details": "Employment details", "translate_employee_requests": "Employee requests", "translate_annual_leave_balance": "Annual leave balance", "translate_employee_evaluation": "Employee evaluation", "translate_tasks_fulfillment_indicator": "Tasks fulfillment indicator", "translate_absence_indicator": "Absence indicator", "translate_medical_leave_balance": "Medical leave balance", "translate_rewards_count": "Rewards count", "translate_violations_count": "Violations count", "translate_attendance_rate": "Attendance rate", "translate_days": "Days", "translate_employee_attendances": "Employee attendances", "translate_employee_info": "Employee info", "translate_personal_info": "Personal info", "translate_old_name": "Old name", "translate_old_nationality": "Old nationality", "translate_height": "Height", "translate_weight": "Weight", "translate_religion": "Religion", "translate_blood_type": "Blood type", "translate_nationality_acquisition_method": "Nationality acquisition", "translate_nationality_acquisition_date": "Nationality acquisition date", "translate_is_mother_citizen": "Is mother citizen", "translate_additional_info": "Additional info", "translate_city": "City", "translate_national_id_number": "National ID number", "translate_national_id_expiration_date": "National ID expiration date", "translate_children_record": "Children record", "translate_id_number": "ID number", "translate_mother_name": "Mother name", "translate_allowance_status": "Allowance status", "translate_employee_educational_qualifications": "Employee educational qualifications", "translate_qualification": "Qualification", "translate_course_major": "Major", "translate_course_result": "Result", "translate_course_details": "Course details", "translate_specialization": "Specialization", "translate_commencement_date": "Commencement date", "translate_graduation_date": "Graduation date", "translate_grade": "Grade", "translate_institution": "Institution", "translate_place_of_study": "Place of study", "translate_employee_leaves": "Employee leaves", "translate_employee_attendance_permissions": "Employee attendance permissions", "translate_attached_user": "Attached user", "translate_employee_salaries_certificate": "Employee salaries certificate", "translate_employee_events": "Employee events", "translate_reference_number": "Reference number", "translate_attachment": "Attachment", "translate_employee_promotion_events": "Employee promotion events", "translate_promotion_date": "Promotion date", "translate_current_job_level": "Current job level", "translate_new_job_level": "New job level", "translate_attachments": "Attachments", "translate_employee_transfer_events": "Employee transfer events", "translate_transfer_date": "Transfer date", "translate_reason": "Reason", "translate_from_department": "From department", "translate_to_department": "To department", "translate_create_new_department_transfer": "Create new department transfer", "translate_employee_violation_events": "Employee violation events", "translate_violation_date": "Violation date", "translate_decision_date": "Decision date", "translate_decision_maker": "Decision maker", "translate_day_count_days": "{{ dayCount }} days", "translate_leave_count_counts": "{{ leaveCount }} leaves", "translate_taken_medical_leaves": "Medical leaves", "translate_show_employees_with_citizen_mothers": "Show employees with citizen mothers", "translate_period_of_service": "Period of service", "translate_last_educational_qualification": "Last educational qualification", "translate_update_job_level_change_details": "Update job level change details", "translate_add_new_job_level_change": "Add new job level change", "translate_promotion_type": "Promotion type", "translate_change_date": "Change date", "translate_reference_date": "Reference date", "translate_reference_file": "Reference file", "translate_update_employee_job_level_to_the_new_job_level": "Update employee job level to the new job level", "translate_job_level_change_details": "Job level change details", "translate_last_job_level_change": "Last job level change", "translate_next_promotion_due_date": "Next promotion due date", "translate_score": "Score", "translate_spouses": "Spouses", "translate_spouse_name": "Spouse name", "translate_wife_name": "Wife name", "translate_husband_name": "Husband name", "translate_add_new_husband": "Add new husband", "translate_add_new_wife": "Add new wife", "translate_update_spouse": "Update spouse", "translate_place_of_marriage": "Place of marriage", "translate_date_of_marriage": "Date of marriage", "translate_marriage_status": "Marriage status", "translate_work_address": "Work address", "translate_is_working": "Has work?", "translate_place_of_work": "Work firm", "translate_has_house_allowance": "Has house allowance?", "translate_allowance_amount": "Allowance amount", "translate_allowance_start_date": "Allowance start date", "translate_wife_details": "Wife details", "translate_husband_details": "Husband details", "translate_create_new_document": "Create new document", "translate_issue_date": "Issue date", "translate_issue_place": "Issue place", "translate_expiration_date": "Expiration date", "translate_update_document": "Update document", "translate_employee_documents": "Official documents", "translate_document_number": "Document number", "translate_document_name": "Document name", "translate_unified_number": "Unified number", "translate_family_book": "Family book number", "translate_other_details": "Other details", "translate_mother_birth_date": "Mother birth date", "translate_mother_born_place": "Mother birth place", "translate_mother_uae_passport_issue_place": "Mother UAE passport issue place", "translate_mother_nationality": "Mother nationality", "translate_mother_details": "Mother details", "translate_transfer_reasons": "Reasons", "translate_transfer_reason_details": "Reason details", "translate_birth_certificate": "Birth certificate file", "translate_add_child": "Add child", "translate_update_child": "Update child", "translate_national_id_file": "National ID file", "translate_transfer_to_date": "To date", "translate_apply_changes_to_employee_profile": "Apply the changes to employee profile", "translate_employee_termination_date": "Termination date", "translate_update_wife": "Update wife details", "translate_update_husband": "Update husband details", "translate_currently_on_leave": "Currently on leave", "translate_the_employee_is_on_leave_of_type_type_from_from_to_to": "The employee is on leave of type {{ type }} from {{ from }} to {{ to }}.", "translate_temporarily_transferred": "Temporarily transferred", "translate_the_employee_is_currently_on_transfer_of_type_type_to_department_until_date": "The employee is currently on transfer of type {{ type }} to {{ department }} until {{ date }}.", "translate_employee_terminated": "Employee terminated", "translate_the_employee_has_been_terminated_on_date_termination_type_is_type": "The employee has been terminated on {{ date }}. Termination type is {{ type }}.", "translate_marriage_certificate": "Marriage certificate", "translate_work_noc_file": "Work NOC file", "translate_seniority_records": "Seniority records", "translate_create_seniority_record": "Add seniority record", "translate_update_seniority_record": "Update seniority record", "translate_period": "Period", "translate_period_in_months": "Period in months", "translate_next_promotion_from": "Next promotion (from)", "translate_next_promotion_to": "Next promotion (to)", "translate_numbers": "Employee numbers", "translate_age_from": "Age form", "translate_age_to": "Age to", "translate_date_of_hire_from": "Hire date from", "translate_date_of_hire_to": "Hire date to", "translate_length_of_service": "Length of service", "translate_length_of_service_from_in_years": "Length of service from", "translate_length_of_service_to_in_years": "Length of service to", "translate_date_of_termination_from": "Date of termination from", "translate_date_of_termination_to": "Date of termination to", "translate_separate_numbers_with_comma": "Separate the numbers with comma (,)", "translate_spouse_details": "Spouse details", "translate_employees_courses": "Employee courses", "translate_course": "Course", "translate_result_type": "Result type", "translate_result": "result", "translate_update_state": "Update state", "translate_seniority_record": "Seniority record", "translate_medical_exemptions": "Medical exemptions", "translate_start_date": "Start date", "translate_end_date": "End date", "translate_day_count": "Day count", "translate_add_new_medical_exemption": "Add new medical exemption", "translate_update_medical_exemption_details": "Update medical exemption details", "translate_last_action": "Last action", "translate_service_request_action_create": "Create", "translate_service_request_action_override": "Override", "translate_service_request_action_cancel": "Cancel", "translate_update_educational_qualification_details": "Update educational qualification details", "translate_add_new_educational_qualification": "Add new educational qualification", "translate_bank_accounts": "Bank accounts", "translate_bank": "Bank", "translate_iban": "IBAN", "translate_is_default": "Is default?", "translate_add_new_bank_account": "Add new bank account", "translate_update_bank_account_details": "Update bank account details", "translate_bank_attachment_file": "Bank attachment file", "translate_does_not_have_signature": "Does not have signature", "translate_does_not_have_passport": "Does not have passport", "translate_does_not_have_national_id": "Does not have national ID", "translate_eligibility_date": "Eligibility date", "translate_family_book_issuing_city": "Family book issuing city", "translate_family_book_issuing_suburb": "Family book issuing suburb", "translate_update_employee_event_details": "Update employee event details", "translate_event_time": "Event time", "translate_add_new_employee_event": "Add new employee event"}