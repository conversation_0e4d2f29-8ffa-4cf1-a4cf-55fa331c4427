{"translate_add_new_salary": "Add new salary", "translate_update_salary_details": "Update salary details", "translate_salary_details": "Salary details", "translate_amount": "Amount", "translate_base_salary": "Base salary", "translate_salary_allowances": "Salary allowances", "translate_salary_deductions": "Salary deductions", "translate_allowance_name": "Allowance name", "translate_allowance_no_payment": "No payment", "translate_create_new_salary_allowance": "Create new salary allowance", "translate_create_new_salary_deduction": "Create new salary deduction", "translate_update_salary_allowance_details": "Update salary allowance", "translate_update_salary_deduction_details": "Update salary deduction", "translate_allowance_type": "Type", "translate_allowance_subtype": "Subtype", "translate_allowance_display_mode": "Display mode", "translate_linked_salaries": "Linked salaries", "translate_type": "Type", "translate_bonus_or_deduction_type": "Bonus or deduction type", "translate_bonus_or_deduction_year": "Bonus or deduction year", "translate_bonus_or_deduction_month": "Bonus or deduction month", "translate_create_new_salary_bonus_or_deduction": "Create new salary bonus or deduction", "translate_update_salary_bonus_or_deduction": "Update salary bonus or deduction", "translate_salary_bonuses_and_reductions": "Bonuses and deductions", "translate_pension_deduction_amount": "Pension deduction amount", "translate_currency": "<PERSON><PERSON><PERSON><PERSON>", "translate_financial_record_closure_date": "Financial record closure date", "translate_salary_deduction_types": "Salary deduction types", "translate_add_new_salary_deduction_type": "Add new salary deduction type", "translate_update_salary_deduction_type_details": "Update salary deduction type details", "translate_salary_deduction_type_details": "Salary deduction type details", "translate_total_salary": "Total salary", "translate_total_allowance_amount": "Total allowance amount", "translate_total_deduction_amount": "Total deduction amount", "translate_deduction_display_mode": "Display mode", "translate_action_date": "Action date", "translate_change_transactions": "Change transactions", "translate_entity_types": "Entity types", "translate_entity_type": "Entity type", "translate_salary_allowance_types": "Salary allowance types", "translate_add_new_salary_allowance_type": "Add new salary allowance type", "translate_update_salary_allowance_type_details": "Update salary allowance type details", "translate_salary_allowance_type_details": "Salary allowance type details", "translate_add_new_payroll_register": "Add new payroll register", "translate_a_payroll_register_exists": "A payroll register exists", "translate_there_is_currently_a_payroll_register_with_the_same_year_and_month_if_you_proceed_the_existing_register_will_be_overwritten": "There is currently a payroll register with the same year and month. If you proceed, the existing register will be overwritten.", "translate_payroll_register_details": "Payroll register details", "translate_payslips": "Payslips", "translate_base_amount": "Base amount", "translate_total_bonus_amount": "Total bonus amount", "translate_total_reduction_amount": "Total reduction amount", "translate_total_amount": "Total amount", "translate_only_show_payslips_that_have_been_changed_since_last_register": "Only show payslips that have been changed since last register", "translate_has_changed_since_last_payroll_register": "Has changed since last payroll register", "translate_this_payslip_contains_values_that_are_different_than_the_ones_from_the_previous_payslip": "This payslip contains values that are different than the ones from the previous payslip.", "translate_show_previous_payslip": "Show previous payslip", "translate_previous_payslip_details": "Previous payslip details", "translate_previous_value_value": "Previous value: {{ value }}", "translate_added_item": "Added item", "translate_removed_item": "Removed item", "translate_previous_register": "Previous register", "translate_next_register": "Next register", "translate_flow_action_submit": "Submit", "translate_flow_action_approve": "Approve", "translate_flow_action_return": "Return", "translate_flow_action_reject": "Reject", "translate_flow_state_draft": "Draft", "translate_flow_state_submitted": "Submitted", "translate_flow_state_first_approved": "First approved", "translate_flow_state_approved": "Approved", "translate_flow_state_rejected": "Rejected", "translate_there_is_an_already_approved_register_with_the_same_year_and_month_you_cannot_proceed_with_the_following_year_and_month": "There is an already approved register with the same year and month. You cannot proceed with the following year and month.", "translate_bank_name": "Bank name", "translate_iban": "IBAN", "translate_category": "Category", "translate_add_new_salary_bonus_or_reduction_type": "Add new salary bonus or reduction type", "translate_update_salary_bonus_or_reduction_type_details": "Update salary bonus or reduction type details", "translate_salary_bonus_or_reduction_type_details": "Salary bonus or reduction type details", "translate_add_new_salary_certificate_requesting_entity": "Add new salary certificate requesting entity", "translate_update_salary_certificate_requesting_entity_details": "Update salary certificate requesting entity details", "translate_salary_certificate_requesting_entity_details": "Salary certificate requesting entity details"}