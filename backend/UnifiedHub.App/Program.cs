using Serilog;
using UnifiedHub.App.Extensions;
using UnifiedHub.App.Servicing;
using UnifiedHub.AttendanceManagement.Extensions;
using UnifiedHub.DepartmentsManagement.Extensions;
using UnifiedHub.EmployeesManagement.Extensions;
using UnifiedHub.Auth.Extensions;
using UnifiedHub.CoursesManagement.Extensions;
using UnifiedHub.Emailing.Extensions;
using UnifiedHub.Fcm.Extensions;
using UnifiedHub.FileManagement.Extensions;
using UnifiedHub.Flows.Extensions;
using UnifiedHub.Foundation.Extensions;
using UnifiedHub.LeavesManagement.Extensions;
using UnifiedHub.Logging.Extensions;
using UnifiedHub.Messaging.Extensions;
using UnifiedHub.RolesManagement.Extensions;
using UnifiedHub.Payroll.Extensions;
using UnifiedHub.Rbac.Extensions;
using UnifiedHub.Recruitment.Extensions;
using UnifiedHub.Servicing.Extensions;
using UnifiedHub.SuggestionsManagement.Extensions;
using UnifiedHub.SurveysManagement.Extensions;
using UnifiedHub.UsersManagement.Extensions;


// Log.Logger = new LoggerConfiguration()
//     .MinimumLevel.Debug()
//     .Enrich.FromLogContext()
//     .WriteTo.Elasticsearch([new Uri("http://localhost:9200")], opts =>
//     {
//         opts.DataStream = new DataStreamName("logs", "unifiedhub", "development");
//         opts.BootstrapMethod = BootstrapMethod.Failure;
//         opts.ConfigureChannel = channelOpts =>
//         {
//             channelOpts.BufferOptions = new BufferOptions
//             {
//                 ExportMaxConcurrency = 10
//             };
//         };
//     }, transport =>
//     {
//         // transport.Authentication(new BasicAuthentication(username, password)); // Basic Auth
//         // transport.Authentication(new ApiKey(base64EncodedApiKey)); // ApiKey
//     })
//     .CreateLogger();

var builder = WebApplication.CreateBuilder(args);

// builder.Host.UseSerilog();
builder.WebHost.AddSentry(builder.Environment);
builder.WebHost.UseKestrel(opt => opt.Limits.MaxRequestBodySize = (long?) (150 * 1024 * 1024 * 1.33));

builder.AddCore();

builder
    .AddRbacFeature()
    .AddEmailingFeature()
    .AddFileManagementFeature()
    .AddServicingFeature<AppGlobalServiceConfig>()
    .AddFcmFeature()
    .AddLoggingFeature()
    .AddFlowsFeature();

builder
    .AddFoundationModule()
    .AddAuthModule(options =>
    {
        options.AllowedEndpointsDuringRequiredPasswordChange =
        [
            "/hub/default/negotiate",
            "/api/v1/languages",
            "/api/v1/translations",
        ];
    })
    .AddUsersManagementModule()
    .AddDepartmentsManagementModule()
    .AddRolesManagementModule()
    .AddAttendanceManagementModule()
    .AddEmployeesManagementModule()
    .AddLeaveManagementModule()
    .AddPayrollManagementModule()
    .AddMessagingModule()
    .AddCourseManagementModule()
    .AddSuggestionsManagementModule()
    .AddSurveysManagementModule()
    .AddRecruitmentManagementModule();


var app = builder.Build();

// app.UseSerilogRequestLogging();

app.UseCore();

await app.UseRbacModule();
await app.UseAuthModule();
await app.UseServicingModule();

try
{
    Log.Information("Starting UnifiedHub application");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
