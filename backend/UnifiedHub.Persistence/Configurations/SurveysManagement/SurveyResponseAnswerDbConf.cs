using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.SurveysManagement;

namespace UnifiedHub.Persistence.Configurations.SurveysManagement;

public sealed class SurveyResponseAnswerDbConf : IEntityTypeConfiguration<SurveyResponseAnswer>
{
    public void Configure(EntityTypeBuilder<SurveyResponseAnswer> builder)
    {
        builder.HasKey(x => new { x.QuestionId, x.ResponseId });

        builder.HasOne(x => x.Response).WithMany(x => x.Answers).OnDelete(DeleteBehavior.Restrict); // cyclic
        builder.HasOne(x => x.Question).WithMany(x => x.Answers).OnDelete(DeleteBehavior.Cascade);
        
        builder.Property(x => x.Values)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<string[]>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new string[0]);
    }
}
