using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.SurveysManagement;

namespace UnifiedHub.Persistence.Configurations.SurveysManagement;

public sealed class SurveyQuestionDbConf : IEntityTypeConfiguration<SurveyQuestion>
{
    public void Configure(EntityTypeBuilder<SurveyQuestion> builder)
    {
        builder.HasOne(x => x.Survey).WithMany(x => x.Questions).OnDelete(DeleteBehavior.Cascade);
        
        builder.Property(x => x.Type)
            .HasMaxLength(32);
            
        builder.Property(x => x.Options)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<SurveyQuestion.Option[]>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new SurveyQuestion.Option[0]);
    }
}
