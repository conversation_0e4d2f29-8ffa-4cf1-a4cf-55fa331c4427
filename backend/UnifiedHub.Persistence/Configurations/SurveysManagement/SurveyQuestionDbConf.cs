using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.SurveysManagement;
using UnifiedHub.Persistence.ValueConverters;

namespace UnifiedHub.Persistence.Configurations.SurveysManagement;

public sealed class SurveyQuestionDbConf : IEntityTypeConfiguration<SurveyQuestion>
{
    public void Configure(EntityTypeBuilder<SurveyQuestion> builder)
    {
        builder.HasOne(x => x.Survey).WithMany(x => x.Questions).OnDelete(DeleteBehavior.Cascade);
        
        builder.Property(x => x.Type)
            .HasMaxLength(32)
            .HasConversion<SnakeCaseEnumConverter<SurveyQuestion.QuestionType>>();
            
        builder.Property(x => x.Options)
            .HasConversion(
                v => System.Text.Json.JsonSerializer.Serialize(v, (System.Text.Json.JsonSerializerOptions?)null),
                v => System.Text.Json.JsonSerializer.Deserialize<SurveyQuestionOption[]>(v, (System.Text.Json.JsonSerializerOptions?)null) ?? new SurveyQuestionOption[0]);
    }
}
