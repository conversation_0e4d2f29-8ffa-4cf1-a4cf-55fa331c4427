using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.Recruitment;

namespace UnifiedHub.Persistence.Configurations.Recruitment;

public sealed class RecruitmentApplicantJobClassificationLinkDbConf :
    IEntityTypeConfiguration<RecruitmentApplicantJobClassificationLink>
{
    public void Configure(EntityTypeBuilder<RecruitmentApplicantJobClassificationLink> builder)
    {
        builder.HasKey(x => new { x.ApplicantId, x.ClassificationId });

        builder.HasOne(x => x.Applicant).WithMany().OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(x => x.Classification).WithMany().OnDelete(DeleteBehavior.Cascade);
    }
}
