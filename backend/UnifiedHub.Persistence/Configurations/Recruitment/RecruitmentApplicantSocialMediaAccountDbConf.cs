using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.Recruitment;

namespace UnifiedHub.Persistence.Configurations.Recruitment;

public sealed class RecruitmentApplicantSocialMediaAccountDbConf
    : IEntityTypeConfiguration<RecruitmentApplicantSocialMediaAccount>
{
    public void Configure(EntityTypeBuilder<RecruitmentApplicantSocialMediaAccount> builder)
    {
        builder.Property(x => x.Type).HasMaxLength(32);

        builder.HasOne(x => x.Applicant).WithMany(x => x.SocialMediaAccounts).OnDelete(DeleteBehavior.Cascade);
    }
}
