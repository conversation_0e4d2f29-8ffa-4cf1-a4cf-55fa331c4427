using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.Recruitment;

namespace UnifiedHub.Persistence.Configurations.Recruitment;

public sealed class RecruitmentApplicantTalentDbConf : IEntityTypeConfiguration<RecruitmentApplicantTalent>
{
    public void Configure(EntityTypeBuilder<RecruitmentApplicantTalent> builder)
    {
        builder.Property(x => x.Name).HasMaxLength(512);
        builder.Property(x => x.Category).HasMaxLength(32);

        builder.HasOne(x => x.Applicant).WithMany(x => x.Talents).OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(x => x.Talent).WithMany().OnDelete(DeleteBehavior.SetNull);
    }
}
