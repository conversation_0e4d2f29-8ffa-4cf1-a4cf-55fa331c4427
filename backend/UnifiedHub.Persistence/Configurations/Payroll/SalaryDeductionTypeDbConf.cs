using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.Payroll;

namespace UnifiedHub.Persistence.Configurations.Payroll;

public sealed class SalaryDeductionTypeDbConf : IEntityTypeConfiguration<SalaryDeductionType>
{
    public void Configure(EntityTypeBuilder<SalaryDeductionType> builder)
    {
        builder.Property(x => x.DisplayMode).HasMaxLength(32);
    }
}
