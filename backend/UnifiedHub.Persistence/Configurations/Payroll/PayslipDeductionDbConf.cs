using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.Payroll;

namespace UnifiedHub.Persistence.Configurations.Payroll;

public sealed class PayslipDeductionDbConf : IEntityTypeConfiguration<PayslipDeduction>
{
    public void Configure(EntityTypeBuilder<PayslipDeduction> builder)
    {
        builder.Property(x => x.Amount).HasPrecision(9, 2);

        builder.HasOne(x => x.Payslip).WithMany(x => x.Deductions).OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(x => x.Type).WithMany().OnDelete(DeleteBehavior.Restrict);
    }
}
