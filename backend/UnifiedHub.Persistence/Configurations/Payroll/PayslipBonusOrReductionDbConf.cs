using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.Payroll;

namespace UnifiedHub.Persistence.Configurations.Payroll;

public sealed class PayslipBonusOrReductionDbConf : IEntityTypeConfiguration<PayslipBonusOrReduction>
{
    public void Configure(EntityTypeBuilder<PayslipBonusOrReduction> builder)
    {
        builder.Property(x => x.Type).HasMaxLength(32);
        builder.Property(x => x.Amount).HasPrecision(9, 2);

        builder.HasOne(x => x.Payslip).WithMany(x => x.BonusOrReductions).OnDelete(DeleteBehavior.Cascade);
    }
}
