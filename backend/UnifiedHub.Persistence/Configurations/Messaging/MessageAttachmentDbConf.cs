using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.Messaging;

namespace UnifiedHub.Persistence.Configurations.Messaging;

public sealed class MessageAttachmentDbConf : IEntityTypeConfiguration<MessageAttachment>
{
    public void Configure(EntityTypeBuilder<MessageAttachment> builder)
    {
        builder.HasKey(x => new { x.MessageId, x.FileId });

        builder.HasOne(x => x.Message).WithMany(x => x.Attachments).OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(x => x.File).WithOne().OnDelete(DeleteBehavior.Restrict);
    }
}
