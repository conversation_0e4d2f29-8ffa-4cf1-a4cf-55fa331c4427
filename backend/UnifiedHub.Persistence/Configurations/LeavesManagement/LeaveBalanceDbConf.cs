using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.LeavesManagement;

namespace UnifiedHub.Persistence.Configurations.LeavesManagement;

public sealed class LeaveBalanceDbConf : IEntityTypeConfiguration<LeaveBalance>
{
    public void Configure(EntityTypeBuilder<LeaveBalance> builder)
    {
        builder.HasKey(x => new { x.EmployeeId, x.Type });

        builder.Property(x => x.Type).HasMaxLength(32);

        builder.HasOne(x => x.Employee).WithMany(x => x.LeaveBalances).OnDelete(DeleteBehavior.Cascade);
    }
}
