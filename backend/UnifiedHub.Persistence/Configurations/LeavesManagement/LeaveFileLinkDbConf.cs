using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.LeavesManagement;

namespace UnifiedHub.Persistence.Configurations.LeavesManagement;

public sealed class LeaveFileLinkDbConf : IEntityTypeConfiguration<LeaveFileLink>
{
    public void Configure(EntityTypeBuilder<LeaveFileLink> builder)
    {
        builder.HasOne(x => x.Leave).WithMany(x => x.FileLinks).OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(x => x.File).WithOne().OnDelete(DeleteBehavior.Restrict);

        builder.<PERSON><PERSON>ey(x => new { x.LeaveId, x.FileId });
    }
}
