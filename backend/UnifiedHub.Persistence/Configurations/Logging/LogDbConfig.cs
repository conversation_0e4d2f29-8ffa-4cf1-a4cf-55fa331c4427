using System.Text.Json;
using System.Text.Json.Nodes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.Logging;

namespace UnifiedHub.Persistence.Configurations.Logging;

public sealed class LogDbConfig : IEntityTypeConfiguration<Log>
{
    private static readonly JsonSerializerOptions JsonSerializerOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false,
        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
    };

    public void Configure(EntityTypeBuilder<Log> builder)
    {
        builder.Property(x => x.Title).HasMaxLength(2048);

        builder.Property(x => x.Data)
            .HasConversion(
                x => x == null ? null : x.ToJsonString(JsonSerializerOptions),
                x => x == null ? null : JsonNode.Parse(x, null, default));

        builder.HasOne(x => x.User).WithMany().OnDelete(DeleteBehavior.SetNull);
    }
}
