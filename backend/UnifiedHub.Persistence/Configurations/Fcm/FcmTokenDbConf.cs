using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.Fcm;

namespace UnifiedHub.Persistence.Configurations.Fcm;

public sealed class FcmTokenDbConf : IEntityTypeConfiguration<FcmToken>
{
    public void Configure(EntityTypeBuilder<FcmToken> builder)
    {
        builder.Property(x => x.Token).HasMaxLength(1024);

        builder.HasOne(x => x.User).WithMany().OnDelete(DeleteBehavior.Cascade);
        builder.HasIndex(x => x.Token).IsUnique();
    }
}
