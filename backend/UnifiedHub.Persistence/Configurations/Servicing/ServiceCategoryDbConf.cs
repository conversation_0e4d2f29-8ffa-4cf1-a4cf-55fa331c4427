using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.Servicing;

namespace UnifiedHub.Persistence.Configurations.Servicing;

public sealed class ServiceCategoryDbConf : IEntityTypeConfiguration<ServiceCategory>

{
    public void Configure(EntityTypeBuilder<ServiceCategory> builder)
    {
        builder.Property(x => x.BuiltInId).HasMaxLength(256);

        builder.HasIndex(x => x.BuiltInId).IsUnique();
    }
}
