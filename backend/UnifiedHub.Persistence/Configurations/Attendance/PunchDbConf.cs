using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.Attendance;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Persistence.ValueConverters;

namespace UnifiedHub.Persistence.Configurations.Attendance;

public class PunchDbConf : IEntityTypeConfiguration<Punch>
{
    public void Configure(EntityTypeBuilder<Punch> builder)
    {
        builder.Property(x => x.Type)
            .HasMaxLength(32)
            .HasConversion<SnakeCaseEnumConverter<Punch.PunchType>>();

        builder.Property(x => x.DeviceName).HasMaxLength(128);
        builder.Property(x => x.DeviceType).HasMaxLength(128);
        builder.Property(x => x.DeviceOperatingSystem).HasMaxLength(128);
        builder.Property(x => x.DeviceId).HasMaxLength(128);
        builder.Property(x => x.AppVersion).HasMaxLength(128);

        builder.HasOne<Employee>(x => x.Employee).WithMany(x => x.Punch<PERSON>).OnDelete(DeleteBehavior.Cascade);
    }
}
