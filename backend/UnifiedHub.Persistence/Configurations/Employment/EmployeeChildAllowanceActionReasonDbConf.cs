using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Persistence.ValueConverters;

namespace UnifiedHub.Persistence.Configurations.Employment;

public sealed class EmployeeChildAllowanceActionReasonDbConf
    : IEntityTypeConfiguration<EmployeeChildAllowanceActionReason>
{
    public void Configure(EntityTypeBuilder<EmployeeChildAllowanceActionReason> builder)
    {
        builder.Property(x => x.Type).HasMaxLength(64)
            .HasConversion<SnakeCaseEnumConverter<EmployeeChildAllowanceActionReason.ReasonType>>();
    }
}
