using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.Persistence.Configurations.Employment;

public sealed class EmployeeViolationEventDbConf
    : IEntityTypeConfiguration<EmployeeViolationEvent>
{
    public void Configure(EntityTypeBuilder<EmployeeViolationEvent> builder)
    {
        builder.Property(x => x.DecisionMaker).HasMaxLength(1024);

        builder.HasOne(x => x.Employee).WithMany(x => x.ViolationEvents).OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(x => x.ViolationType).WithMany().OnDelete(DeleteBehavior.Restrict);
    }
}
