using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using UnifiedHub.Common.Extensions;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Entities.LeavesManagement.ValueObjects;

namespace UnifiedHub.Persistence.Configurations.Employment;

public sealed class JobLevelDbConf : IEntityTypeConfiguration<JobLevel>
{
    public void Configure(EntityTypeBuilder<JobLevel> builder)
    {
        builder.Property(x => x.LeaveConfig).HasConversion(
            x => x.SerializeToJsonString(),
            x => x.DeserializeFromJsonString<LeaveConfig>(),
            new ValueComparer<LeaveConfig>((c1, c2) => (c1 == null && c2 == null) || c1!.Equals(c2),
                c => c.GetHashCode(), c => c.Clone()));

        builder.HasOne(x => x.Classification).WithMany(x => x.JobLevels).OnDelete(DeleteBehavior.Restrict);
    }
}
