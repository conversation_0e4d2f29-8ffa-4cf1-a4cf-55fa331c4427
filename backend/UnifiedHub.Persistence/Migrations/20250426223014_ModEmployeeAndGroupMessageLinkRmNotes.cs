using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UnifiedHub.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class ModEmployeeAndGroupMessageLinkRmNotes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "notes",
                table: "message_to_messaging_group_links");

            migrationBuilder.DropColumn(
                name: "notes",
                table: "message_to_employee_links");

            migrationBuilder.DropColumn(
                name: "notes",
                table: "message_employee_accesses");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "notes",
                table: "message_to_messaging_group_links",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "notes",
                table: "message_to_employee_links",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "notes",
                table: "message_employee_accesses",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
