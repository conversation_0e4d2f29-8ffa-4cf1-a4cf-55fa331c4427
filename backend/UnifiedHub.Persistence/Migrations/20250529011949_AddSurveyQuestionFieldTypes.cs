using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UnifiedHub.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddSurveyQuestionFieldTypes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "is_required",
                table: "survey_questions",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "options",
                table: "survey_questions",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "order",
                table: "survey_questions",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "type",
                table: "survey_questions",
                type: "nvarchar(32)",
                maxLength: 32,
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "is_required",
                table: "survey_questions");

            migrationBuilder.DropColumn(
                name: "options",
                table: "survey_questions");

            migrationBuilder.DropColumn(
                name: "order",
                table: "survey_questions");

            migrationBuilder.DropColumn(
                name: "type",
                table: "survey_questions");
        }
    }
}
