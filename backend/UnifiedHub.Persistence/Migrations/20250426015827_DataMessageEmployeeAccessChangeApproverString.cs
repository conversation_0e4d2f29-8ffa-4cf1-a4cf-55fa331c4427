using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UnifiedHub.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class DataMessageEmployeeAccessChangeApproverString : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("""
                                 UPDATE message_employee_accesses
                                 SET roles = REPLACE(
                                         roles,
                                         '"approver"',
                                         '"pending_approver"'
                                             )
                                 WHERE roles LIKE '%"approver"%'
                                 """);

        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
