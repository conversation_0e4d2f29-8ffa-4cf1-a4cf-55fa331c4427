using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace UnifiedHub.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class AddSalaryBonusOrReductionType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "type_id",
                table: "salary_bonus_or_reductions",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "salary_bonus_or_reduction_types",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    category = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    modified_time = table.Column<DateTime>(type: "datetime2", nullable: true),
                    created_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    modified_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    version = table.Column<long>(type: "bigint", nullable: false),
                    deleted_time = table.Column<DateTime>(type: "datetime2", nullable: true),
                    deleted_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_salary_bonus_or_reduction_types", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "ix_salary_bonus_or_reductions_type_id",
                table: "salary_bonus_or_reductions",
                column: "type_id");

            migrationBuilder.AddForeignKey(
                name: "fk_salary_bonus_or_reductions_salary_bonus_or_reduction_types_type_id",
                table: "salary_bonus_or_reductions",
                column: "type_id",
                principalTable: "salary_bonus_or_reduction_types",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_salary_bonus_or_reductions_salary_bonus_or_reduction_types_type_id",
                table: "salary_bonus_or_reductions");

            migrationBuilder.DropTable(
                name: "salary_bonus_or_reduction_types");

            migrationBuilder.DropIndex(
                name: "ix_salary_bonus_or_reductions_type_id",
                table: "salary_bonus_or_reductions");

            migrationBuilder.DropColumn(
                name: "type_id",
                table: "salary_bonus_or_reductions");
        }
    }
}
