using Microsoft.EntityFrameworkCore;
using UnifiedHub.Model.Entities.Recruitment;

namespace UnifiedHub.Persistence.Initialization;

public partial class DatabaseInitializer
{
    public DbSet<RecruitmentApplicant> RecruitmentApplicants { get; set; }
    public DbSet<RecruitmentApplicantAttachment> RecruitmentApplicantAttachments { get; set; }
    public DbSet<RecruitmentApplicantAttachmentType> RecruitmentApplicantAttachmentTypes { get; set; }
    public DbSet<RecruitmentApplicantCourse> RecruitmentApplicantCourses { get; set; }
    public DbSet<RecruitmentApplicantCreditCard> RecruitmentApplicantCreditCards { get; set; }
    public DbSet<RecruitmentApplicantDriverLicense> RecruitmentApplicantDriverLicenses { get; set; }

    public DbSet<RecruitmentApplicantEducationalQualification> RecruitmentApplicantEducationalQualifications
    {
        get;
        set;
    }

    public DbSet<RecruitmentApplicantJobClassificationLink> RecruitmentApplicantJobClassificationLinks { get; set; }
    public DbSet<RecruitmentApplicantLanguage> RecruitmentApplicantLanguages { get; set; }
    public DbSet<RecruitmentApplicantRelation> RecruitmentApplicantRelations { get; set; }
    public DbSet<RecruitmentApplicantSocialMediaAccount> RecruitmentApplicantSocialMediaAccounts { get; set; }
    public DbSet<RecruitmentApplicantTalent> RecruitmentApplicantTalents { get; set; }
    public DbSet<RecruitmentApplicantVehicle> RecruitmentApplicantVehicles { get; set; }
    public DbSet<RecruitmentApplicantVisitedCountry> RecruitmentApplicantVisitedCountries { get; set; }
    public DbSet<RecruitmentApplicantWorkExperience> RecruitmentApplicantWorkExperiences { get; set; }

    public DbSet<Vacancy> Vacancies { get; set; }
    public DbSet<VacancyEducationalQualificationLink> VacancyEducationalQualificationLinks { get; set; }
    public DbSet<VacancyNationalityLink> VacancyNationalityLinks { get; set; }
    public DbSet<VacancyApplication> VacancyApplications { get; set; }
}
