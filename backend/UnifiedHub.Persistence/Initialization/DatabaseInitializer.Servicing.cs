using Microsoft.EntityFrameworkCore;
using UnifiedHub.Model.Entities.Servicing;

namespace UnifiedHub.Persistence.Initialization;

public partial class DatabaseInitializer
{
    public DbSet<Service> Services { get; set; }
    public DbSet<ServiceCategory> ServiceCategories { get; set; }
    public DbSet<ServiceSubcategory> ServiceSubcategories { get; set; }
    public DbSet<ServiceStateTransition> ServiceStateTransitions { get; set; }
    public DbSet<ServiceRequest> ServiceRequests { get; set; }
    public DbSet<ServiceRequestTransaction> ServiceRequestTransactions { get; set; }
    public DbSet<ServiceUserFavorite> ServiceUserFavorites { get; set; }
}
