using Microsoft.EntityFrameworkCore;
using UnifiedHub.Model.Entities.Foundation;

namespace UnifiedHub.Persistence.Initialization;

public partial class DatabaseInitializer
{
    public DbSet<Bank> Banks { get; set; }
    public DbSet<FamilyRelationType> FamilyRelationTypes { get; set; }
    public DbSet<MaritalStatus> MaritalStatuses { get; set; }
    public DbSet<Religion> Religions { get; set; }
    public DbSet<Nationality> Nationalities { get; set; }
    public DbSet<City> Cities { get; set; }
    public DbSet<Suburb> Suburbs { get; set; }
    public DbSet<EducationalQualification> EducationalQualifications { get; set; }
    public DbSet<EducationalMajor> EducationalMajors { get; set; }
    public DbSet<EducationalSpecialization> EducationalSpecializations { get; set; }
}
