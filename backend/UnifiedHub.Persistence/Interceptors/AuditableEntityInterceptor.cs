using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using UnifiedHub.Common.Extensions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Services;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Model.Entities.Logging;
using UnifiedHub.Model.Misc;
using UnifiedHub.Persistence.Extensions;
using UnifiedHub.Persistence.Services.AuditingInterceptor;

namespace UnifiedHub.Persistence.Interceptors;

public sealed class AuditableEntityInterceptor(
    IDateTimeProvider dateTimeProvider,
    IServiceProvider serviceProvider,
    IAuditingInterceptorService auditingInterceptorService) : SaveChangesInterceptor
{
    public override InterceptionResult<int> SavingChanges(
        DbContextEventData eventData,
        InterceptionResult<int> result)
    {
        UpdateEntities(eventData.Context);

        return base.SavingChanges(eventData, result);
    }


    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData,
        InterceptionResult<int> result,
        CancellationToken cancellationToken = default)
    {
        UpdateEntities(eventData.Context);
        CreateLogs(eventData.Context);
        UpdateDeleted(eventData.Context);

        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    private void CreateLogs(DbContext? context)
    {
        var identityService = serviceProvider.GetRequiredService<IIdentityService>();

        if (!auditingInterceptorService.IsEnabled)
        {
            return;
        }

        var logs = new List<Log>();

        context?.ChangeTracker.Entries<ILoggableEntity>().ToList().ForEach(entry =>
        {
            var log = new Log
            {
                Timestamp = dateTimeProvider.UtcNow,
                UserId = identityService.User?.Id
            };

            if (entry.State == EntityState.Added)
            {
                log.Type = Log.TypeEntityCreation;
                log.Title = $"A new entity of type `{entry.Metadata.DisplayName()}` has been created.";
                log.Data = new
                {
                    Type = entry.Metadata.DisplayName(),
                    Id = entry.Metadata
                        .FindPrimaryKey()?
                        .Properties
                        .Select(p => entry.Property(p.Name).CurrentValue)
                        .ToArray(),
                    Entity = entry.Properties
                        .Where(p => entry.Metadata.GetNavigations().All(n => n.Name != p.Metadata.Name))
                        .ToDictionary(
                            p => p.Metadata.Name,
                            p => p.CurrentValue
                        )
                }.ToJsonNode();

                logs.Add(log);
            }

            else if (entry.State == EntityState.Modified || entry.HasChangedOwnedEntities())
            {
                log.Type = Log.TypeEntityUpdate;
                log.Title = $"An entity of type `{entry.Metadata.DisplayName()}` has been updated.";
                log.Data = new ObjectChangeSnapshot
                    {
                        Type = entry.Metadata.DisplayName(),
                        Id = entry.Metadata
                            .FindPrimaryKey()?
                            .Properties
                            .Select(p => entry.Property(p.Name).CurrentValue)
                            .ToArray(),
                        Changes = entry.Properties
                            .Where(p => p.IsModified)
                            .Select(p => new ObjectChangeSnapshot.ObjectChange
                            {
                                PropertyName = p.Metadata.Name,
                                OldValue = p.OriginalValue,
                                NewValue = p.CurrentValue
                            })
                            .ToArray()
                    }
                    .ToJsonNode();

                logs.Add(log);
            }

            else if (entry.State == EntityState.Deleted)
            {
                log.Type = Log.TypeEntityDeletion;
                log.Title = $"An entity of type `{entry.Metadata.DisplayName()}` has been deleted.";
                log.Data = new
                {
                    Type = entry.Metadata.DisplayName(),
                    Id = entry.Metadata
                        .FindPrimaryKey()?
                        .Properties
                        .Select(p => entry.Property(p.Name).CurrentValue)
                        .ToArray(),
                    Entity = entry.Properties
                        .Where(p => entry.Metadata.GetNavigations().All(n => n.Name != p.Metadata.Name))
                        .ToDictionary(
                            p => p.Metadata.Name,
                            p => p.CurrentValue
                        )
                }.ToJsonNode();

                logs.Add(log);
            }
        });

        context?.AddRange(logs);
    }

    private void UpdateEntities(DbContext? context)
    {
        var identityService = serviceProvider.GetRequiredService<IIdentityService>();

        if (!auditingInterceptorService.IsEnabled)
        {
            return;
        }


        context?.ChangeTracker.Entries<AuditableEntity>().ToList().ForEach(entry =>
        {
            if (entry.State != EntityState.Added &&
                entry.State != EntityState.Modified && !entry.HasChangedOwnedEntities() &&
                entry.State != EntityState.Deleted)
            {
                return;
            }

            entry.Entity.ModifiedTime = dateTimeProvider.UtcNow;
            entry.Entity.ModifiedById = identityService.User?.Id;
            entry.Entity.Version = dateTimeProvider.Timestamp;

            if (entry.State == EntityState.Added)
            {
                entry.Entity.CreatedTime = dateTimeProvider.UtcNow;
                entry.Entity.CreatedById = identityService.User?.Id;
            }
        });
    }

    private void UpdateDeleted(DbContext? context)
    {
        var identityService = serviceProvider.GetRequiredService<IIdentityService>();

        if (!auditingInterceptorService.IsEnabled)
        {
            return;
        }

        context?.ChangeTracker.Entries<AuditableWithSoftDeleteEntity>().ToList().ForEach(entry =>
        {
            if (entry.State != EntityState.Deleted)
            {
                return;
            }

            entry.State = EntityState.Modified;
            entry.Entity.DeletedTime = dateTimeProvider.UtcNow;
            entry.Entity.DeletedById = identityService.User?.Id;
        });
    }
}
