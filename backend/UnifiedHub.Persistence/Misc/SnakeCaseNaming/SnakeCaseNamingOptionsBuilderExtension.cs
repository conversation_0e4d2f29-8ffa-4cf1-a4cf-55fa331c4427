using System.Globalization;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;

namespace UnifiedHub.Persistence.Misc.SnakeCaseNaming;

public static class SnakeCaseNamingOptionsBuilderExtension
{
    public static DbContextOptionsBuilder UseTestSnakeCaseNamingConvention(
        this DbContextOptionsBuilder optionsBuilder,
        CultureInfo? culture = null)
    {
        var extension = optionsBuilder.Options.FindExtension<SnakeCaseNamingDbContextOptionsExtension>()
                        ?? new SnakeCaseNamingDbContextOptionsExtension();

        ((IDbContextOptionsBuilderInfrastructure) optionsBuilder).AddOrUpdateExtension(extension);

        return optionsBuilder;
    }
}
