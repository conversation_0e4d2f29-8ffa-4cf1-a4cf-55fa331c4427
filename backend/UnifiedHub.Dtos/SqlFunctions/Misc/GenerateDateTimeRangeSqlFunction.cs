using System.Reflection;
using UnifiedHub.Dtos.Misc;

namespace UnifiedHub.Dtos.SqlFunctions.Misc;

public class GenerateDateTimeRangeSqlFunction : ISqlFunction
{
    public string GetName()
    {
        return "fn_generate_datetime_range";
    }

    public MethodInfo[] GetBindings()
    {
        return [GetType().GetMethod(nameof(Call))!];
    }

    public IEnumerable<Type> GetDependencies()
    {
        return Array.Empty<Type>();
    }

    public string GetDefinition()
    {
        return $@"
                CREATE OR ALTER FUNCTION fn_generate_datetime_range(
                    @start DATETIME,
                    @end DATETIME,
                    @step INT
                )
                    RETURNS @datetime_range TABLE
                                            (
                                                value DATETIME
                                            )
                AS
                BEGIN
                    ;
                    WITH datetime_range_cte AS
                             (SELECT @start AS value
                              UNION ALL
                              SELECT DATEADD(SECOND, @step, value)
                              FROM datetime_range_cte
                              WHERE DATEADD(SECOND, @step, value) <= @end)
                    INSERT
                    INTO @datetime_range
                    SELECT value
                    FROM datetime_range_cte
                    OPTION (MAXRECURSION 0);

                    RETURN;
                END
        ";
    }

    public static IQueryable<ValueWrapper<DateTime>> Call(DateTime start, DateTime end, int step)
    {
        throw new NotSupportedException();
    }
}
