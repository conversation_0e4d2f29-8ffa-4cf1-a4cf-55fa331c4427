using System.Reflection;
using System.Text.Json.Nodes;

namespace UnifiedHub.Dtos.SqlFunctions.Misc;

public sealed class GetJsonValueAsInt : ISqlFunction
{
    public string GetName()
    {
        return "fn_get_json_value_as_int";
    }

    public MethodInfo[] GetBindings()
    {
        return [GetType().GetMethod(nameof(Call))!];
    }

    public IEnumerable<Type> GetDependencies()
    {
        return Array.Empty<Type>();
    }

    public string GetDefinition()
    {
        return $"""
                CREATE OR ALTER FUNCTION {GetName()}(@json NVARCHAR(MAX), @property NVARCHAR(128)) RETURNS INT AS
                BEGIN
                    RETURN CAST(JSON_VALUE(@json, '$.' + @property) AS INT)
                END
                """;
    }

    public static int? Call(JsonNode json, string property)
    {
        throw new NotSupportedException();
    }
}
