using System.Linq.Expressions;
using System.Text.Json.Nodes;
using LinqKit;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.Dtos.Modules.UsersManagement.Users;
using UnifiedHub.Model.Entities.Servicing;

namespace UnifiedHub.Dtos.Features.Servicing.ServiceRequestTransactions;

public sealed class ServiceRequestTransactionDto
{
    public static Expression<Func<ServiceRequestTransaction, ServiceRequestTransactionDto>> Mapper()
    {
        var userExp = UserSimpleDto.Mapper();
        var stateExp = E.ServiceRequestTransactions.State();
        var actionExp = E.ServiceRequestTransactions.Action();

        return item => new ServiceRequestTransactionDto
        {
            Id = item.Id,
            CreatedTime = item.CreatedTime,
            User = userExp.Invoke(item.User),
            FromState = stateExp.Invoke(item, false),
            ToState = stateExp.Invoke(item, true)!,
            Action = actionExp.Invoke(item),
            Data = item.Data,
        };
    }

    public Guid Id { get; set; }

    public DateTime CreatedTime { get; set; }

    public UserSimpleDto User { get; set; } = null!;

    public ServiceState? FromState { get; set; }

    public ServiceAction? Action { get; set; }

    public ServiceState ToState { get; set; } = null!;

    public JsonNode Data { get; set; } = null!;
}
