using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Dtos.Features.Servicing.Misc;

public sealed class UserServicingDto
{
    public static Expression<Func<User, UserServicingDto>> Mapper(
        Expression<Func<User, object?>> extraExpression)
    {
        return item => new UserServicingDto
        {
            Id = item.Id,
            Name = item.Name,
            GlobalExtraData = extraExpression.Invoke(item)
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public object? GlobalExtraData { get; set; }
}
