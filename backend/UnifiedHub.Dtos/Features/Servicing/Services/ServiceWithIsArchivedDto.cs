using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Servicing;

namespace UnifiedHub.Dtos.Features.Servicing.Services;

public sealed class ServiceWithIsArchivedDto
{
    public static Expression<Func<Service, ServiceWithIsArchivedDto>> Mapper()
    {
        return item => new ServiceWithIsArchivedDto
        {
            Id = item.Id,
            Name = item.Name,
            IsArchived = item.IsArchived
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public bool IsArchived { get; set; }
}
