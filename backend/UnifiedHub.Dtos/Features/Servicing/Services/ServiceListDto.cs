using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Features.Servicing.ServiceSubcategories;
using UnifiedHub.Model.Entities.Servicing;

namespace UnifiedHub.Dtos.Features.Servicing.Services;

public sealed class ServiceListDto
{
    public static Expression<Func<Service, ServiceListDto>> Mapper(
        Guid? userId,
        Expression<Func<Service, Guid, int>>? userPendingCount = null)
    {
        var subcategoryExp = ServiceSubcategoryGetDto.Mapper();

        userId ??= Guid.Empty;
        userPendingCount ??= (s, u) => -1;

        return item => new ServiceListDto
        {
            Id = item.Id,
            BuiltInId = item.BuiltInId,
            Name = item.Name,
            Description = item.Description,
            Subcategory = item.Subcategory == null ? null : subcategoryExp.Invoke(item.Subcategory),
            IsArchived = item.IsArchived,
            IsSystemManaged = item.IsSystemManaged,
            IsFavorite = item.UserFavorites.Any(x => x.UserId == userId),
            PendingRequestCount = userPendingCount.Invoke(item, userId.Value)
        };
    }

    public Guid Id { get; set; }

    public string BuiltInId { get; set; } = null!;

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? Description { get; set; }

    public ServiceSubcategoryGetDto? Subcategory { get; set; }

    public bool IsArchived { get; set; }
    
    public bool IsSystemManaged { get; set; }

    public bool IsFavorite { get; set; }

    public int PendingRequestCount { get; set; }
}
