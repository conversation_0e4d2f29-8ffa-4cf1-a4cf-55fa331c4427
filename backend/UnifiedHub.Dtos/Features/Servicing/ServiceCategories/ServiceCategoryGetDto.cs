using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Features.Servicing.ServiceSubcategories;
using UnifiedHub.Model.Entities.Servicing;

namespace UnifiedHub.Dtos.Features.Servicing.ServiceCategories;

public sealed class ServiceCategoryGetDto
{
    public static Expression<Func<ServiceCategory, ServiceCategoryGetDto>> Mapper()
    {
        var subcategoryExp = ServiceSubcategorySimpleDto.Mapper();

        return item => new ServiceCategoryGetDto
        {
            Id = item.Id,
            Name = item.Name,
            Description = item.Description,
            Subcategories = item.Subcategories.Select(x => subcategoryExp.Invoke(x)).ToList()
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? Description { get; set; }

    public IEnumerable<ServiceSubcategorySimpleDto> Subcategories { get; set; } = null!;
}
