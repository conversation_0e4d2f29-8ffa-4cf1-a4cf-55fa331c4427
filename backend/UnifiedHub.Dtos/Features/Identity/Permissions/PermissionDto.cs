using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Dtos.Features.Identity.Permissions;

public sealed class PermissionDto
{
    public static Expression<Func<Permission, PermissionDto>> Mapper()
    {
        return item => new PermissionDto
        {
            Id = item.Id,
            Name = item.Name,
            Description = item.Description
        };
    }

    public string Id { get; set; } = null!;

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString Description { get; set; } = null!;
}
