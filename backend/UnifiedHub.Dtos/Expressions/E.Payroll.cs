using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Model.Entities.Payroll;
using UnifiedHub.Model.Entities.Payroll.Misc;

namespace UnifiedHub.Dtos.Expressions;

public partial class E
{
    public static class Payroll
    {
        public static Expression<Func<Salary, decimal>> BaseSalary(DateOnly now)
        {
            var isAllowanceIncludedInBase = IsAllowanceIncludedInBase();
            var isAllowanceActive = IsAllowanceOrDeductionActive(now);

            return item => item.Amount
                + item.Allowances
                    .Where(x => x.Amount != null)
                    .Where(x => isAllowanceActive.Invoke(x))
                    .Where(x => isAllowanceIncludedInBase.Invoke(x))
                    .Sum(x => x.Amount) ?? 0;
        }

        public static Expression<Func<Salary, decimal>> TotalSalaryAmount(DateOnly now)
        {
            var isAllowanceIncludedInBase = IsAllowanceIncludedInBase();
            var baseExp = BaseSalary(now);
            var isActive = IsAllowanceOrDeductionActive(now);

            return item => baseExp.Invoke(item)
                           - (item.PensionDeductionAmount ?? 0)
                           + (item.Allowances
                               .Where(x => x.Amount != null)
                               .Where(x => isActive.Invoke(x))
                               .Where(x => !isAllowanceIncludedInBase.Invoke(x))
                               .Sum(x => x.Amount) ?? 0)
                           - item.Deductions
                               .Where(x => x.Amount != null)
                               .Where(x => isActive.Invoke(x))
                               .Sum(x => x.Amount ?? 0);
        }

        public static Expression<Func<Salary, decimal>> TotalSalaryAllowanceAmount(DateOnly now)
        {
            var isAllowanceIncludedInBase = IsAllowanceIncludedInBase();
            var isActive = IsAllowanceOrDeductionActive(now);

            return item => item.Allowances
                .Where(x => x.Amount != null)
                .Where(x => isActive.Invoke(x))
                .Where(x => !isAllowanceIncludedInBase.Invoke(x))
                .Sum(x => x.Amount) ?? 0;
        }


        public static Expression<Func<Payslip, decimal>> BasePayslip()
        {
            var isAllowanceIncludedInBase = IsAllowanceIncludedInBase();

            return item => item.Amount
                + item.Allowances
                    .Where(x => x.Amount != null)
                    .Where(x => isAllowanceIncludedInBase.Invoke(x))
                    .Sum(x => x.Amount) ?? 0;
        }

        public static Expression<Func<Payslip, decimal>> TotalPayslip()
        {
            var isAllowanceIncludedInBase = IsAllowanceIncludedInBase();
            var baseExp = BasePayslip();

            return item => baseExp.Invoke(item)
                           - (item.PensionDeductionAmount ?? 0)
                           + (item.Allowances
                               .Where(x => x.Amount != null)
                               .Where(x => !isAllowanceIncludedInBase.Invoke(x))
                               .Sum(x => x.Amount) ?? 0)
                           - item.Deductions
                               .Where(x => x.Amount != null)
                               .Sum(x => x.Amount ?? 0)
                           + item
                               .BonusOrReductions
                               .Sum(x => (x.Type == SalaryBonusOrReduction.CategoryBonus ? 1 : -1) * x.Amount);
        }

        public static Expression<Func<Payslip, decimal>> TotalPayslipAllowanceAmount()
        {
            var isAllowanceIncludedInBase = IsAllowanceIncludedInBase();

            return item => item.Allowances
                .Where(x => x.Amount != null)
                .Where(x => !isAllowanceIncludedInBase.Invoke(x))
                .Sum(x => x.Amount) ?? 0;
        }

        public static Expression<Func<Payslip, decimal>> TotalPayslipDeductionAmount()
        {
            return item => item.Deductions
                .Where(x => x.Amount != null)
                .Sum(x => x.Amount) ?? 0;
        }

        public static Expression<Func<Payslip, decimal>> TotalPayslipBonusAmount()
        {
            return item => item.BonusOrReductions
                .Where(x => x.Type == SalaryBonusOrReduction.CategoryBonus)
                .Sum(x => x.Amount);
        }

        public static Expression<Func<Payslip, decimal>> TotalPayslipReductionAmount()
        {
            return item => item.BonusOrReductions
                .Where(x => x.Type == SalaryBonusOrReduction.CategoryReduction)
                .Sum(x => x.Amount);
        }

        public static Expression<Func<ISalaryOrPayslipAllowance, bool>> IsAllowanceIncludedInBase()
        {
            return item => item.Type.Type != SalaryAllowanceType.TypeAllowance &&
                           (item.Type.Subtype == SalaryAllowanceType.SubtypeComplementary ||
                            item.Type.Subtype == SalaryAllowanceType.SubtypeBaseSalaryAddition);
        }

        public static Expression<Func<ISalaryAllowanceOrDeduction, bool>> IsAllowanceOrDeductionActive(DateOnly now)
        {
            return item => item.From <= now && (now <= item.To || item.To == null);
        }

        public static Expression<Func<ISalaryAllowanceOrDeduction, bool>> IsAllowanceOrDeductionExpired(DateOnly now)
        {
            return item => item.To < now;
        }

        public static Expression<Func<ISalaryAllowanceOrDeduction, bool>> IsAllowanceOrDeductionActive(
            DateOnly from,
            DateOnly to)
        {
            return item => from <= (item.To ?? from) && item.From <= to;
        }

        public static Expression<Func<ISalaryAllowanceOrDeduction, decimal?>> GetAllowanceOrDeductionAmount(
            DateOnly from,
            DateOnly to)
        {
            var period = to.DayNumber - from.DayNumber;

            var max = MaxDate();
            var min = MinDate();

            Expression<Func<ISalaryAllowanceOrDeduction, int>> overlap = item =>
                Math.Max(0, min.Invoke(to, item.To ?? to).DayNumber - max.Invoke(from, item.From).DayNumber + 1);

            return item => overlap.Invoke(item) >= period
                ? item.Amount
                : item.Amount * overlap.Invoke(item) / 30m;
        }

        public static Expression<Func<Payslip, Payslip?>> PreviousPayslip()
        {
            return item => item.Register.PreviousRegister!.Payslips.First(x => x.SalaryId == item.SalaryId);
        }

        public static Expression<Func<Payslip, bool?>> HasPayslipChangedSincePrevious()
        {
            var previousPayslipExp = PreviousPayslip();
            return item => previousPayslipExp.Invoke(item) != null &&
                           (previousPayslipExp.Invoke(item)!.Amount != item.Amount ||
                            previousPayslipExp.Invoke(item)!.Amounts.BaseAmount != item.Amounts.BaseAmount ||
                            previousPayslipExp.Invoke(item)!.Amounts.TotalAllowanceAmount !=
                            item.Amounts.TotalAllowanceAmount ||
                            previousPayslipExp.Invoke(item)!.Amounts.TotalDeductionAmount !=
                            item.Amounts.TotalDeductionAmount ||
                            previousPayslipExp.Invoke(item)!.Amounts.TotalBonusAmount !=
                            item.Amounts.TotalBonusAmount ||
                            previousPayslipExp.Invoke(item)!.Amounts.TotalReductionAmount !=
                            item.Amounts.TotalReductionAmount ||
                            previousPayslipExp.Invoke(item)!.PensionDeductionAmount !=
                            item.Amounts.PensionDeductionAmount);
        }
    }
}
