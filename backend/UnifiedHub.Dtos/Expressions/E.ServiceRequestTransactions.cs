using System.Linq.Expressions;
using UnifiedHub.Model.Entities.Servicing;

namespace UnifiedHub.Dtos.Expressions;

public partial class E
{
    public static class ServiceRequestTransactions
    {
        public static Expression<Func<ServiceRequestTransaction, bool, ServiceState?>> State()
        {
            return (item, isToState) => item.Request.Service.States.OrderBy(x => x.Value)
                .SingleOrDefault(x => x.Value == (isToState ? item.ToState : item.FromState)) ?? (isToState
                ? new ServiceState
                {
                    Value = item.ToState,
                    Name = new() { Ar = item.ToState, En = item.ToState }
                }
                : item.FromState == null
                    ? null
                    : new ServiceState
                    {
                        Value = item.FromState,
                        Name = new() { Ar = item.FromState, En = item.FromState }
                    });
        }

        public static Expression<Func<ServiceRequestTransaction, ServiceAction?>> Action()
        {
            return item =>
                item.Request.Service.Actions.OrderBy(x => x.Key).SingleOrDefault(x => x.Key == item.Action) ??
                new ServiceAction
                {
                    Key = item.Action,
                    Name = new() { Ar = item.Action, En = item.Action }
                };
        }
    }
}
