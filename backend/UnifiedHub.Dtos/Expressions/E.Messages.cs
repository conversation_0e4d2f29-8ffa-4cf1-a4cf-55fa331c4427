using System.Linq.Expressions;
using UnifiedHub.Model.Entities.Messaging;

namespace UnifiedHub.Dtos.Expressions;

public partial class E
{
    public static class Messages
    {
        public static Expression<Func<Message, bool>> CanReadByEmployeeId(Guid employeeId)
        {
            return item => item.EmployeeAccesses.Any(x => x.EmployeeId == employeeId && x.Roles.Length != 0);
        }

        public static Expression<Func<Message, bool>> CanRead(Guid userId)
        {
            return item =>
                item.EmployeeAccesses.Any(
                    x => x.Employee.UserId != null && x.Employee.UserId == userId && x.Roles.Length != 0);
        }

        public static Expression<Func<Message, bool>> CanWrite(Guid userId)
        {
            return item => (item.State == Message.StateReturned || item.State == Message.StateDraft) &&
                           item.SendingEmployee.User != null && item.SendingEmployee.User.Id == userId;
        }
    }
}
