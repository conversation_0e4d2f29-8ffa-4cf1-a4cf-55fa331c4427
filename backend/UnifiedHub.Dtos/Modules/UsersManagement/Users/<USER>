using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Dtos.Modules.UsersManagement.Users;

public class UserSimpleDto
{
    public static Expression<Func<User, UserSimpleDto>> Mapper()
    {
        return item => new UserSimpleDto
        {
            Id = item.Id,
            Name = item.Name
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;
}
