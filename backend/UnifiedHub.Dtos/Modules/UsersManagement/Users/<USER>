using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Dtos.Modules.UsersManagement.Users;

public class UserListDto
{
    public static Expression<Func<User, UserListDto>> Mapper()
    {
        return item => new UserListDto
        {
            Id = item.Id,
            Email = item.Email,
            Name = item.Name,
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public string Email { get; set; } = null!;
}
