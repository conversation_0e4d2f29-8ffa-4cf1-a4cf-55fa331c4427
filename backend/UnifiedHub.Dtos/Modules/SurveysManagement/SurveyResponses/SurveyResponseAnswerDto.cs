using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Dtos.Modules.SurveysManagement.Surveys;
using UnifiedHub.Model.Entities.SurveysManagement;

namespace UnifiedHub.Dtos.Modules.SurveysManagement.SurveyResponses;

public sealed class SurveyResponseAnswerDto
{
    public static Expression<Func<SurveyResponseAnswer, SurveyResponseAnswerDto>> Mapper()
    {
        var questionExp = SurveyQuestionDto.Mapper();

        return item => new()
        {
            Question = questionExp.Invoke(item.Question),
            Value = item.Value,
            Values = item.Values,
            CreatedTime = item.CreatedTime,
            ModifiedTime = item.ModifiedTime,
        };
    }

    public SurveyQuestionDto Question { get; set; } = null!;

    public string? Value { get; set; }
    
    public string[] Values { get; set; } = [];

    public DateTime CreatedTime { get; set; }

    public DateTime? ModifiedTime { get; set; }
}
