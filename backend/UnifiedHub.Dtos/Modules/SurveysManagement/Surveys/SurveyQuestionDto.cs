using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.SurveysManagement;

namespace UnifiedHub.Dtos.Modules.SurveysManagement.Surveys;

public sealed class SurveyQuestionDto
{
    public static Expression<Func<SurveyQuestion, SurveyQuestionDto>> Mapper()
    {
        return item => new()
        {
            Id = item.Id,
            Name = item.Name,
            Description = item.Description,
            Type = item.Type,
            Order = item.Order,
            IsRequired = item.IsRequired,
            Options = item.Options,
            CreatedTime = item.CreatedTime,
            ModifiedTime = item.ModifiedTime,
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? Description { get; set; }

    public SurveyQuestion.QuestionType Type { get; set; }

    public int Order { get; set; }

    public bool IsRequired { get; set; }

    public SurveyQuestionOption[] Options { get; set; } = [];

    public DateTime CreatedTime { get; set; }

    public DateTime? ModifiedTime { get; set; }
}
