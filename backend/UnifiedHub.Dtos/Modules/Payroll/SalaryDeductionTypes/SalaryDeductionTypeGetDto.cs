using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Payroll;

namespace UnifiedHub.Dtos.Modules.Payroll.SalaryDeductionTypes;

public sealed class SalaryDeductionTypeGetDto
{
    public static Expression<Func<SalaryDeductionType, SalaryDeductionTypeGetDto>> Mapper()
    {
        return item => new SalaryDeductionTypeGetDto
        {
            Id = item.Id,
            Name = item.Name,
            Description = item.Description,
            DisplayMode = item.DisplayMode,
            DeductionCount = item.Deductions.Count
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? Description { get; set; }

    public string DisplayMode { get; set; } = null!;

    public int DeductionCount { get; set; }
}
