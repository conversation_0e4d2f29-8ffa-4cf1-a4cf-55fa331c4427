using System.Linq.Expressions;
using UnifiedHub.Model.Entities.Payroll;

namespace UnifiedHub.Dtos.Modules.Payroll.PayrollRegisters;

public sealed class PayrollRegisterActionAbilityDto
{
    public static Expression<Func<PayrollRegister, PayrollRegisterActionAbilityDto>> Mapper(
        bool hasDeletePermission)
    {
        return item => new()
        {
            CanDelete = item.FlowState == PayrollRegister.FlowStateDraft && hasDeletePermission
        };
    }

    public bool CanDelete { get; set; }
}
