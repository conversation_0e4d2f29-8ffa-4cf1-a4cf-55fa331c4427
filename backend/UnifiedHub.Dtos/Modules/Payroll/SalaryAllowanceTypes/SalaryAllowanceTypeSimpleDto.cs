using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Payroll;

namespace UnifiedHub.Dtos.Modules.Payroll.SalaryAllowanceTypes;

public sealed class SalaryAllowanceTypeSimpleDto
{
    public static Expression<Func<SalaryAllowanceType, SalaryAllowanceTypeSimpleDto>> Mapper()
    {
        return item => new SalaryAllowanceTypeSimpleDto
        {
            Id = item.Id,
            Name = item.Name
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;
}
