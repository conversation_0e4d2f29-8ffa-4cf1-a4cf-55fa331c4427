using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Dtos.Modules.Payroll.SalaryDeductionTypes;
using UnifiedHub.Model.Entities.Payroll;

namespace UnifiedHub.Dtos.Modules.Payroll.Payslips;

public sealed class PayslipDeductionDto
{
    public static Expression<Func<PayslipDeduction, PayslipDeductionDto>> Mapper()
    {
        var typeExp = SalaryDeductionTypeSimpleDto.Mapper();

        return item => new()
        {
            Id = item.Id,
            Type = typeExp.Invoke(item.Type),
            Amount = item.Amount,
            From = item.From,
            To = item.To,
            Notes = item.Notes,
        };
    }

    public Guid Id { get; set; }

    public SalaryDeductionTypeSimpleDto Type { get; set; } = null!;

    public decimal? Amount { get; set; }

    public DateOnly From { get; set; }

    public DateOnly? To { get; set; }

    public string? Notes { get; set; }
}
