using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.CoursesManagement;

namespace UnifiedHub.Dtos.Modules.CoursesManagement.CourseTypes;

public sealed class CourseTypeGetDto
{
    public static Expression<Func<CourseType, CourseTypeGetDto>> Mapper()
    {
        return item => new CourseTypeGetDto
        {
            Id = item.Id,
            Name = item.Name
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;
}
