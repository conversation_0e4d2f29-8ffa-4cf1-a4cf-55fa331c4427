using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.CoursesManagement;

namespace UnifiedHub.Dtos.Modules.CoursesManagement.CourseTypes;

public sealed class CourseTypeSimpleDto
{
    public static Expression<Func<CourseType, CourseTypeSimpleDto>> Mapper()
    {
        return item => new CourseTypeSimpleDto
        {
            Id = item.Id,
            Name = item.Name
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;
}

