using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.CoursesManagement;

namespace UnifiedHub.Dtos.Modules.CoursesManagement.CourseStatusTypes;

public sealed class CourseStatusTypeGetDto
{
    public static Expression<Func<CourseStatusType, CourseStatusTypeGetDto>> Mapper()
    {
        return item => new CourseStatusTypeGetDto
        {
            Id = item.Id,
            Name = item.Name
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;
}

