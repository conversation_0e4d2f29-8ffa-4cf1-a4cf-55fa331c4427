using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.Dtos.Modules.DepartmentsManagement.Departments;
using UnifiedHub.Dtos.Modules.EmployeesManagement.JobLevels;
using UnifiedHub.Dtos.Modules.EmployeesManagement.JobTitles;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.Dtos.Modules.CoursesManagement.CourseEmployees;

public sealed class EmployeeDetailForCourseDto
{
    public static Expression<Func<Employee, EmployeeDetailForCourseDto>> Mapper()
    {
        var primaryDepartment = E.Employees.PrimaryDepartment();
        var departmentExp = DepartmentSimpleDto.Mapper();
        var jobLevelExp = JobLevelSimpleDto.Mapper();
        var jobTitle = JobTitleSimpleDto.Mapper();

        return item => new()
        {
            Id = item.Id,
            Name = item.Name,
            Number = item.Number,
            Department = primaryDepartment.Invoke(item) == null
                ? null
                : departmentExp.Invoke(primaryDepartment.Invoke(item)!),
            JobLevel = item.JobLevel == null ? null : jobLevelExp.Invoke(item.JobLevel),
            JobTitle = item.JobTitle == null ? null : jobTitle.Invoke(item.JobTitle),
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public string? Number { get; set; }

    public DepartmentSimpleDto? Department { get; set; }

    public JobLevelSimpleDto? JobLevel { get; set; }

    public JobTitleSimpleDto? JobTitle { get; set; }
}
