using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.SuggestionsManagement;

namespace UnifiedHub.Dtos.Modules.SuggestionsManagement.SuggestionCategories;

public sealed class SuggestionCategoryListDto
{
    public static Expression<Func<SuggestionCategory, SuggestionCategoryListDto>> Mapper()
    {
        return item => new()
        {
            Id = item.Id,
            Name = item.Name,
            Description = item.Description,
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? Description { get; set; }
}
