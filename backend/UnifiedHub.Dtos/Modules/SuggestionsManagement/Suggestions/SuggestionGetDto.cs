using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.Dtos.Modules.DepartmentsManagement.Departments;
using UnifiedHub.Dtos.Modules.EmployeesManagement.Employees;
using UnifiedHub.Dtos.Modules.SuggestionsManagement.SuggestionCategories;
using UnifiedHub.Dtos.Modules.SuggestionsManagement.SuggestionImpactLevels;
using UnifiedHub.Model.Entities.SuggestionsManagement;

namespace UnifiedHub.Dtos.Modules.SuggestionsManagement.Suggestions;

public sealed class SuggestionGetDto
{
    public static Expression<Func<Suggestion, SuggestionGetDto>> Mapper(
        Guid? userId,
        string[] hierarchyCodes,
        bool hasAssessPermission,
        bool hasDepartmentAssessPermission)
    {
        var categoryExp = SuggestionCategorySimpleDto.Mapper();
        var impactExp = SuggestionImpactLevelSimpleDto.Mapper();
        var departmentExp = DepartmentSimpleDto.Mapper();
        var employeeExp = EmployeeBasicDto.Mapper();
        var fileExp = FileDto.Mapper();
        var stateAvailability = SuggestionStateAvailability.Mapper(
            userId,
            hierarchyCodes,
            hasAssessPermission,
            hasDepartmentAssessPermission
        );
        var transactionExp = SuggestionAssessmentTransactionDto.Mapper();
        var transferExp = SuggestionAssessmentTransferDto.Mapper();
        var actionAbilityExp = SuggestionActionAbilityDto.Mapper(userId);
        var isOwnerExp = E.Suggestions.IsOwner(userId);
        var assessingDepartmentExp = E.Suggestions.AssessingDepartment();

        return item => new()
        {
            Id = item.Id,
            Name = item.Name,
            Description = item.Description,
            Category = categoryExp.Invoke(item.Category),
            ExpectedGoals = item.ExpectedGoals,
            Beneficiaries = item.Beneficiaries,
            ImpactLevel = impactExp.Invoke(item.ImpactLevel),
            Department = item.Department == null ? null : departmentExp.Invoke(item.Department),
            AssessingDepartment = assessingDepartmentExp.Invoke(item) == null
                ? null
                : departmentExp.Invoke(assessingDepartmentExp.Invoke(item)!),
            State = item.State,
            ExpectedImplementationTime = item.ExpectedImplementationTime,
            StateAvailability = stateAvailability.Invoke(item),
            ActionAbility = actionAbilityExp.Invoke(item),
            Employees = item.State == Suggestion.StateInProgress ||
                        item.State == Suggestion.StateCompleted ||
                        isOwnerExp.Invoke(item)
                ? item.EmployeeLinks.Select(x => employeeExp.Invoke(x.Employee)).ToList()
                : new EmployeeBasicDto[] { },
            Files = item.FileLinks.Select(x => fileExp.Invoke(x.File)).ToList(),
            AssessmentTransactions = item.AssessmentTransactions
                .OrderByDescending(x => x.CreatedTime)
                .Select(x => transactionExp.Invoke(x)).ToList(),
            AssessmentTransfers = item.AssessmentTransfers
                .OrderByDescending(x => x.CreatedTime)
                .Select(x => transferExp.Invoke(x)).ToList(),
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? Description { get; set; }

    public SuggestionCategorySimpleDto Category { get; set; } = null!;

    public string? ExpectedGoals { get; set; }

    public string? Beneficiaries { get; set; }

    public SuggestionImpactLevelSimpleDto ImpactLevel { get; set; } = null!;

    public DepartmentSimpleDto? Department { get; set; }

    public DepartmentSimpleDto? AssessingDepartment { get; set; }

    public string State { get; set; } = null!;

    public string? ExpectedImplementationTime { get; set; }

    public SuggestionStateAvailability StateAvailability { get; set; } = null!;

    public SuggestionActionAbilityDto ActionAbility { get; set; } = null!;

    public IEnumerable<EmployeeBasicDto> Employees { get; set; } = [];

    public IEnumerable<FileDto> Files { get; set; } = [];

    public IEnumerable<SuggestionAssessmentTransactionDto> AssessmentTransactions { get; set; } = [];

    public IEnumerable<SuggestionAssessmentTransferDto> AssessmentTransfers { get; set; } = [];
}
