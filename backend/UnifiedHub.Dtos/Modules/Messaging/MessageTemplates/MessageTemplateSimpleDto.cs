using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Messaging;

namespace UnifiedHub.Dtos.Modules.Messaging.MessageTemplates;

public sealed class MessageTemplateSimpleDto
{
    public static Expression<Func<MessageTemplate, MessageTemplateSimpleDto>> Mapper()
    {
        return item => new MessageTemplateSimpleDto
        {
            Id = item.Id,
            Name = item.Name
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;
}
