using System.Linq.Expressions;
using UnifiedHub.Model.Entities.Recruitment;

namespace UnifiedHub.Dtos.Modules.Recruitment.VacancyApplications;

public sealed class VacancyApplicationBasicDto
{
    public static Expression<Func<VacancyApplication, VacancyApplicationBasicDto>> Mapper()
    {
        return item => new()
        {
            Id = item.Id,
            ReferenceNumber = item.ReferenceNumber,
        };
    }

    public Guid Id { get; set; }

    public string ReferenceNumber { get; set; } = null!;
}
