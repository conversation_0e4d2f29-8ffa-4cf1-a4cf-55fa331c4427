using System.Linq.Expressions;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.Model.Entities.Recruitment;
using LinqKit;
using UnifiedHub.Dtos.Modules.Foundation.Countries;

namespace UnifiedHub.Dtos.Modules.Recruitment.RecruitmentApplicants;

public sealed class RecruitmentApplicantWorkExperienceGetDto
{
    public static Expression<Func<RecruitmentApplicantWorkExperience, RecruitmentApplicantWorkExperienceGetDto>>
        Mapper()
    {
        var countryExp = CountrySimpleDto.Mapper();
        var fileExp = FileDto.Mapper();

        return item => new()
        {
            Id = item.Id,
            JobTitle = item.JobTitle,
            Employer = item.Employer,
            JobType = item.JobType,
            From = item.From,
            To = item.To,
            Country = item.Country == null ? null : countryExp.Invoke(item.Country),
            ExperienceLetterFile = item.ExperienceLetterFile == null ? null : fileExp.Invoke(item.ExperienceLetterFile),
            NoObjectionCertificateFile = item.NoObjectionCertificateFile == null
                ? null
                : fileExp.Invoke(item.NoObjectionCertificateFile)
        };
    }

    public Guid Id { get; set; }

    public string? JobTitle { get; set; }

    public string? Employer { get; set; }

    public string? JobType { get; set; }

    public DateOnly? From { get; set; }

    public DateOnly? To { get; set; }

    public CountrySimpleDto? Country { get; set; }

    public FileDto? ExperienceLetterFile { get; set; }

    public FileDto? NoObjectionCertificateFile { get; set; }
}
