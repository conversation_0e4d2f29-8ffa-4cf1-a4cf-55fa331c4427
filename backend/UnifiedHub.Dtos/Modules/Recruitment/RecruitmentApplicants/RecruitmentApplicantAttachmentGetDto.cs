using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.Dtos.Modules.Recruitment.RecruitmentApplicantAttachmentTypes;
using UnifiedHub.Model.Entities.Recruitment;

namespace UnifiedHub.Dtos.Modules.Recruitment.RecruitmentApplicants;

public sealed class RecruitmentApplicantAttachmentGetDto
{
    public static Expression<Func<RecruitmentApplicantAttachment, RecruitmentApplicantAttachmentGetDto>> Mapper()
    {
        var typeExp = RecruitmentApplicantAttachmentTypeSimpleDto.Mapper();
        var fileExp = FileDto.Mapper();

        return item => new()
        {
            Id = item.Id,
            Type = item.Type == null ? null : typeExp.Invoke(item.Type),
            File = item.File == null ? null : fileExp.Invoke(item.File)
        };
    }

    public Guid Id { get; set; }

    public RecruitmentApplicantAttachmentTypeSimpleDto? Type { get; set; }

    public FileDto? File { get; set; }
}
