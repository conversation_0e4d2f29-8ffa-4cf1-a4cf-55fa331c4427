using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Foundation;

namespace UnifiedHub.Dtos.Modules.Foundation.EducationalSpecializations;

public sealed class EducationalSpecializationListDto
{
    public static Expression<Func<EducationalSpecialization, EducationalSpecializationListDto>> Mapper()
    {
        return item => new EducationalSpecializationListDto
        {
            Id = item.Id,
            Name = item.Name
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;
}
