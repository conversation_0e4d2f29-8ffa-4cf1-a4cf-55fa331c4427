using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Foundation;

namespace UnifiedHub.Dtos.Modules.Foundation.Languages;

public sealed class LanguageListDto
{
    public static Expression<Func<Language, LanguageListDto>> Mapper()
    {
        return item => new LanguageListDto
        {
            Id = item.Id,
            Name = item.Name
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;
}
