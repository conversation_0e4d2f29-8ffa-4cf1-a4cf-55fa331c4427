using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.Dtos.Modules.EmployeesManagement.Employees;
using UnifiedHub.Dtos.Modules.Foundation.Banks;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeBankAccounts;

public sealed class EmployeeBankAccountGetDto
{
    public static Expression<Func<EmployeeBankAccount, EmployeeBankAccountGetDto>> Mapper()
    {
        var employeeExp = EmployeeBasicDto.Mapper();
        var bankExp = BankSimpleDto.Mapper();
        var fileExp = FileDto.Mapper();

        return item => new()
        {
            Id = item.Id,
            Employee = employeeExp.Invoke(item.Employee),
            Bank = bankExp.Invoke(item.Bank),
            ChangeDate = item.ChangeDate,
            Iban = item.Iban,
            IsDefault = item.IsDefault,
            BankAttachmentFile = item.BankAttachmentFile == null ? null : fileExp.Invoke(item.BankAttachmentFile),
        };
    }

    public Guid Id { get; set; }
    
    public EmployeeBasicDto Employee { get; set; } = null!;

    public BankSimpleDto Bank { get; set; } = null!;

    public DateTime? ChangeDate { get; set; }

    public string Iban { get; set; } = null!;

    public bool IsDefault { get; set; }

    public FileDto? BankAttachmentFile { get; set; }
}
