using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.Dtos.Features.Servicing.Requests;
using UnifiedHub.Dtos.Modules.EmployeesManagement.Employees;
using UnifiedHub.Dtos.Modules.Foundation.Countries;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeSpouses;

public sealed class EmployeeSpouseGetDto
{
    public static Expression<Func<EmployeeSpouse, EmployeeSpouseGetDto>> Mapper()
    {
        var employeeExp = EmployeeSimpleDto.Mapper();
        var countryExp = CountrySimpleDto.Mapper();
        var fileExp = FileDto.Mapper();
        var requestExp = ServiceRequestSimpleDto.Mapper();

        return item => new EmployeeSpouseGetDto
        {
            Id = item.Id,
            Employee = employeeExp.Invoke(item.Employee),
            Name = item.Name,
            Gender = item.Gender,
            IdNumber = item.IdNumber,
            DateOfBirth = item.DateOfBirth,
            PlaceOfBirth = item.PlaceOfBirth,
            DateOfMarriage = item.DateOfMarriage,
            MarriageStatus = item.MarriageStatus,
            Nationality = item.Nationality == null ? null : countryExp.Invoke(item.Nationality),
            IsWorking = item.IsWorking,
            JobTitle = item.JobTitle,
            PlaceOfMarriage = item.PlaceOfMarriage,
            WorkAddress = item.WorkAddress,
            PlaceOfWork = item.PlaceOfWork,
            HireDate = item.HireDate,
            HasHouseAllowance = item.HasHouseAllowance,
            AllowanceStartDate = item.AllowanceStartDate,
            HouseAllowanceAmount = item.HouseAllowanceAmount,
            Notes = item.Notes,
            MarriageCertificateFile = item.MarriageCertificateFile == null
                ? null
                : fileExp.Invoke(item.MarriageCertificateFile),
            WorkNocFile = item.WorkNocFile == null ? null : fileExp.Invoke(item.WorkNocFile),
            ServiceRequest = item.ServiceRequest == null ? null : requestExp.Invoke(item.ServiceRequest),
        };
    }

    public string? PlaceOfMarriage { get; set; }

    public Guid Id { get; set; }

    public EmployeeSimpleDto Employee { get; set; } = null!;

    public MultilingualString Name { get; set; } = null!;

    public string Gender { get; set; } = null!;

    public string? IdNumber { get; set; }

    public DateTime? DateOfBirth { get; set; }

    public string? PlaceOfBirth { get; set; }

    public DateTime? DateOfMarriage { get; set; }

    public string MarriageStatus { get; set; } = null!;

    public CountrySimpleDto? Nationality { get; set; }

    public bool IsWorking { get; set; }

    public string? JobTitle { get; set; }

    public string? WorkAddress { get; set; }

    public string? PlaceOfWork { get; set; }

    public DateTime? HireDate { get; set; }

    public bool? HasHouseAllowance { get; set; }

    public decimal? HouseAllowanceAmount { get; set; }

    public DateTime? AllowanceStartDate { get; set; }

    public string? Notes { get; set; }

    public FileDto? MarriageCertificateFile { get; set; }

    public FileDto? WorkNocFile { get; set; }

    public ServiceRequestSimpleDto? ServiceRequest { get; set; }
}
