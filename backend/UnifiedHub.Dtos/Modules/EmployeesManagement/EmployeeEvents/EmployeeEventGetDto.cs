using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.Dtos.Modules.EmployeesManagement.Employees;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeEvents;

public sealed class EmployeeEventGetDto
{
    public static Expression<Func<EmployeeEvent, EmployeeEventGetDto>> Mapper()
    {
        var employeeExp = EmployeeSimpleDto.Mapper();
        var fileExp = FileDto.Mapper();

        return item => new()
        {
            Id = item.Id,
            Employee = employeeExp.Invoke(item.Employee),
            Type = item.Type,
            Time = item.Time,
            Details = item.Details,
            ReferenceNumber = item.ReferenceNumber,
            File = item.File == null ? null : fileExp.Invoke(item.File)
        };
    }

    public Guid Id { get; set; }

    public EmployeeSimpleDto Employee { get; set; } = null!;

    public DateTime? Time { get; set; }

    public string Type { get; set; } = null!;

    public MultilingualString? Details { get; set; }

    public string? ReferenceNumber { get; set; }

    public FileDto? File { get; set; }
}
