using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.EmployeesManagement.JobClassifications;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.Dtos.Modules.EmployeesManagement.JobTitles;

public sealed class JobTitleGetDto
{
    public static Expression<Func<JobTitle, JobTitleGetDto>> Mapper()
    {
        var classificationExp = JobClassificationSimpleDto.Mapper();

        return item => new JobTitleGetDto
        {
            Id = item.Id,
            Name = item.Name,
            Order = item.Order,
            Description = item.Description,
            Classification = item.Classification == null ? null : classificationExp.Invoke(item.Classification)
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? Description { get; set; }

    public int Order { get; set; }

    public JobClassificationSimpleDto? Classification { get; set; }
}
