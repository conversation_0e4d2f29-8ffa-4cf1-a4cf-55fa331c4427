using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeViolationTypes;

public sealed class EmployeeViolationTypeSimpleDto
{
    public static Expression<Func<EmployeeViolationType, EmployeeViolationTypeSimpleDto>> Mapper()
    {
        return item => new EmployeeViolationTypeSimpleDto
        {
            Id = item.Id,
            Name = item.Name
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;
}
