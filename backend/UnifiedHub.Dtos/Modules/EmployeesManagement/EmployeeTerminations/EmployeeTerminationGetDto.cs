using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.Dtos.Modules.EmployeesManagement.Employees;
using UnifiedHub.Dtos.Modules.EmployeesManagement.Misc;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeTerminations;

public sealed class EmployeeTerminationGetDto
{
    public static Expression<Func<EmployeeTermination, EmployeeTerminationGetDto>> Mapper()
    {
        var employeeExp = EmployeeSimpleDto.Mapper();
        var typeExp = EmployeeTerminationTypeSimpleDto.Mapper();
        var reasonExp = EmployeeTerminationReasonSimpleDto.Mapper();
        var fileExp = FileDto.Mapper();

        return item => new EmployeeTerminationGetDto
        {
            Id = item.Id,
            Employee = employeeExp.Invoke(item.Employee),
            LastWorkDate = item.LastWorkDate,
            Type = item.Type == null ? null : typeExp.Invoke(item.Type),
            TerminationReason = item.TerminationReason == null ? null : reasonExp.Invoke(item.TerminationReason),
            Details = item.Details,
            SignedRequestFile = item.SignedRequestFile == null ? null : fileExp.Invoke(item.SignedRequestFile!),
            FinanceNotes = item.FinanceNotes,
            WarehouseNotes = item.WarehouseNotes,
            ArmoryNotes = item.ArmoryNotes,
            IsResidencyCanceled = item.IsResidencyCanceled,
            IsHealthInsuranceCanceled = item.IsHealthInsuranceCanceled,
            ResidencyAndHealthInsuranceCancellationNotes = item.ResidencyAndHealthInsuranceCancellationNotes,
            LengthOfServiceInDays = item.LengthOfServiceInDays,
            EndOfServiceBenefits = item.EndOfServiceBenefits
        };
    }

    public Guid Id { get; set; }

    public EmployeeSimpleDto Employee { get; set; } = null!;

    public DateTime LastWorkDate { get; set; }

    public EmployeeTerminationTypeSimpleDto? Type { get; set; }

    public EmployeeTerminationReasonSimpleDto? TerminationReason { get; set; }

    public string? Details { get; set; }

    public FileDto? SignedRequestFile { get; set; }

    public string? FinanceNotes { get; set; }

    public string? WarehouseNotes { get; set; }

    public string? ArmoryNotes { get; set; }

    public bool? IsResidencyCanceled { get; set; }

    public bool? IsHealthInsuranceCanceled { get; set; }

    public string? ResidencyAndHealthInsuranceCancellationNotes { get; set; }

    public double? LengthOfServiceInDays { get; set; }

    public decimal? EndOfServiceBenefits { get; set; }
}
