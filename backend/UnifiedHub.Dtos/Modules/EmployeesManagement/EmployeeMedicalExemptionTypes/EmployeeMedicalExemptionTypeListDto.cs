using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeMedicalExemptionTypes;

public sealed class EmployeeMedicalExemptionTypeListDto
{
    public static Expression<Func<EmployeeMedicalExemptionType, EmployeeMedicalExemptionTypeListDto>> Mapper()
    {
        return item => new EmployeeMedicalExemptionTypeListDto
        {
            Id = item.Id,
            Name = item.Name
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;
}
