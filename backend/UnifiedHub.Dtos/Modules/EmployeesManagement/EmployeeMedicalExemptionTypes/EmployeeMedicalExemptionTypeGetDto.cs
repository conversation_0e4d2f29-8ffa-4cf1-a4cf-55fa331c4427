using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeMedicalExemptionTypes;

public sealed class EmployeeMedicalExemptionTypeGetDto
{
    public static Expression<Func<EmployeeMedicalExemptionType, EmployeeMedicalExemptionTypeGetDto>> Mapper()
    {
        return item => new EmployeeMedicalExemptionTypeGetDto
        {
            Id = item.Id,
            Name = item.Name,
            Description = item.Description,
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? Description { get; set; }
}
