using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeDocuments;

public sealed class EmployeeDocumentSimpleDto
{
    public static Expression<Func<EmployeeDocument, EmployeeDocumentSimpleDto>> Mapper()
    {
        return item => new EmployeeDocumentSimpleDto
        {
            Id = item.Id,
            Name = item.Name
        };
    }

    public Guid Id { get; set; }

    public MultilingualString? Name { get; set; }
}
