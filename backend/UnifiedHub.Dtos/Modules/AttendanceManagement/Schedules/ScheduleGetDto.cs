using System.Linq.Expressions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.Attendance;

namespace UnifiedHub.Dtos.Modules.AttendanceManagement.Schedules;

public sealed class ScheduleGetDto
{
    public static Expression<Func<Schedule, ScheduleGetDto>> Mapper()
    {
        return item => new ScheduleGetDto
        {
            Id = item.Id,
            Name = item.Name,
            StartTime = item.StartTime,
            FlexOffsetInMinutes = item.FlexOffsetInMinutes,
            EarlyStartOffsetInMinutes = item.EarlyStartOffsetInMinutes,
            LateEndOffsetInMinutes = item.LateEndOffsetInMinutes,
            Records = item.Records.ToArray()
        };
    }

    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public DateTime StartTime { get; set; }

    public int FlexOffsetInMinutes { get; set; }

    public int EarlyStartOffsetInMinutes { get; set; }

    public int LateEndOffsetInMinutes { get; set; }

    public Schedule.Record[] Records { get; set; } = null!;
}
