using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using UnifiedHub.Servicing.Abstraction;

namespace UnifiedHub.Servicing.Extensions;

public static class ServiceCollectionExtension
{
    public static IServiceCollection AddServicingConfigs(
        this IServiceCollection services,
        Assembly assembly)
    {
        assembly
            .GetTypes()
            .Where(x => x.IsAssignableTo(typeof(IServiceConfig)) && x is { IsAbstract: false, IsClass: true })
            .ToList()
            .ForEach(serviceConfigType => services.AddScoped(typeof(IServiceConfig), serviceConfigType));


        return services;
    }

    public static IServiceCollection AddServicingCategories(
        this IServiceCollection services,
        Assembly assembly)
    {
        assembly
            .GetTypes()
            .Where(x => x.IsAssignableTo(typeof(IServiceSubcategoryDefinitionProvider)) &&
                        x is { IsAbstract: false, IsClass: true })
            .ToList()
            .ForEach(providerType =>
                services.AddSingleton(typeof(IServiceSubcategoryDefinitionProvider), providerType));


        assembly
            .GetTypes()
            .Where(x => x.IsAssignableTo(typeof(IServiceCategoryDefinitionProvider)) &&
                        x is { IsAbstract: false, IsClass: true })
            .ToList()
            .ForEach(providerType => services.AddSingleton(typeof(IServiceCategoryDefinitionProvider), providerType));


        return services;
    }
}
