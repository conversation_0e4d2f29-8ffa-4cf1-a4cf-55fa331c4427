using System.Linq.Expressions;
using LinqKit;
using UnifiedHub.Model.Entities.Servicing;
using UnifiedHub.Servicing.Utilities;

namespace UnifiedHub.Servicing.Abstraction;

public partial class ServiceConfig
{
    /**
    * Gets valid transitions for a service request without user permission checks
    */
    public Expression<Func<ServiceRequest, ServiceStateTransition[]>> GetValidTransitions()
    {
        // Reuse the user-aware method but skip user checks
        var transitionsWithUserExp = GetValidTransitions(false);

        // Create an expression that invokes the user-aware expression with an empty user ID
        return r => transitionsWithUserExp.Invoke(r, Guid.Empty);
    }

    public Expression<Func<ServiceRequest, Guid, ServiceStateTransition[]>> GetValidTransitionsForUser()
    {
        return GetValidTransitions(true);
    }

    /**
     * Gets valid transitions for a service request with optional user permission checks
     *
     * @param withUserCheck Whether to include user permission checks
     * @return Expression that evaluates to valid transitions for a request and user
     */
    private Expression<Func<ServiceRequest, Guid, ServiceStateTransition[]>> GetValidTransitions(bool withUserCheck)
    {
        // Create parameters for request and user
        var requestParam = Expression.Parameter(typeof(ServiceRequest), "request");
        var userParam = Expression.Parameter(typeof(Guid), "user");

        // Create parameter for the JSON string representing a transition
        var transitionParam = Expression.Parameter(typeof(ServiceStateTransition), "transition");

        // Method to invoke guard conditions
        var invokeMethodInfoRequestBool =
            Utils.GetInvokeMethodInfo(typeof(Func<,>), typeof(ServiceRequest), typeof(bool));
        var invokeMethodInfoRequestGuidBool =
            Utils.GetInvokeMethodInfo(typeof(Func<,,>), typeof(ServiceRequest), typeof(Guid), typeof(bool));

        // Extract JSON fields using SQL-compatible functions
        var fromProperty = Expression.Property(transitionParam, nameof(ServiceStateTransition.FromState));
        var toProperty = Expression.Property(transitionParam, nameof(ServiceStateTransition.ToState));
        var actionProperty = Expression.Property(transitionParam, nameof(ServiceStateTransition.Action));

        // Build predicate for filtering transitions
        var filterPredicate = Expression.Lambda<Func<ServiceRequest, Guid, ServiceStateTransition, bool>>(
            BuildTransitionConditions(StateTransitions),
            requestParam,
            userParam,
            transitionParam
        );

        // Final expression that filters and reconstructs transitions
        return (r, u) => r.Service.Transitions
            .Where(t => t.FromState == r.State)
            .Where(t => filterPredicate.Invoke(r, u, t))
            .ToArray();

        // Helper method to build combined conditions for all transitions
        Expression BuildTransitionConditions(ServiceStateTransitionDefinition[] transitions)
        {
            // Exit condition for recursion
            if (transitions.Length == 0)
            {
                return Expression.Constant(false);
            }

            // Process the current transition
            var transition = transitions[0];

            // Create constants for comparison
            var transitionFrom = Expression.Constant(transition.FromState);
            var transitionTo = Expression.Constant(transition.ToState);
            var transitionAction = Expression.Constant(transition.Action);

            // Add checks for transition properties matching JSON fields
            var check = Expression.Equal(fromProperty, transitionFrom);
            check = Expression.AndAlso(check, Expression.Equal(toProperty, transitionTo));
            check = Expression.AndAlso(check, Expression.Equal(actionProperty, transitionAction));

            Expression aggregatedCondition = Expression.Constant(true);

            // Add guard condition if present
            if (transition.Guard != null)
            {
                aggregatedCondition = Expression.And(
                    aggregatedCondition,
                    Expression.Call(null, invokeMethodInfoRequestBool, transition.Guard, requestParam)
                );
            }

            // Add user check if enabled and present
            if (withUserCheck &&
                transition.UserCheck != null)
            {
                aggregatedCondition = Expression.AndAlso(
                    aggregatedCondition,
                    Expression.Call(
                        null,
                        invokeMethodInfoRequestGuidBool,
                        transition.UserCheck,
                        requestParam,
                        userParam
                    )
                );
            }

            // Combine with conditions for remaining transitions
            return Expression.Condition(check, aggregatedCondition, BuildTransitionConditions(transitions[1..]));
        }
    }
}
