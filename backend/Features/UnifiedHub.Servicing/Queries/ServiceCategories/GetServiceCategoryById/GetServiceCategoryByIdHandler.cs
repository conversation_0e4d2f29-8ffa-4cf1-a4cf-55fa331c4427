using ErrorOr;
using LinqKit.Core;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Dtos.Features.Servicing.ServiceCategories;
using UnifiedHub.Model.Entities.Servicing;
using UnifiedHub.Servicing.Persistence;

namespace UnifiedHub.Servicing.Queries.ServiceCategories.GetServiceCategoryById;

public sealed class GetServiceCategoryByIdHandler(
    IServicingDataSource dataSource) : IRequestHandler<GetServiceCategoryByIdQuery, ErrorOr<ServiceCategoryGetDto>>
{
    public async Task<ErrorOr<ServiceCategoryGetDto>> Handle(
        GetServiceCategoryByIdQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var item = dataSource
            .Items<ServiceCategory>()
            .AsNoTracking()
            .AsExpandable()
            .Select(ServiceCategoryGetDto.Mapper())
            .SingleOrDefault(x => x.Id == query.Id);

        return item == null ? Error.NotFound() : (ErrorOr<ServiceCategoryGetDto>) item;
    }
}
