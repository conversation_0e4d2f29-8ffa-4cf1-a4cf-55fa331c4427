using ErrorOr;
using MediatR;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Servicing.Persistence;

namespace UnifiedHub.Servicing.Commands.ServiceUserFavorites.AddServiceToUserFavorite;

public sealed class AddServiceToUserFavoriteHandler(
    IServicingDataSource dataSource,
    IStringLocalizer<SharedResource> l) : IRequestHandler<AddServiceToUserFavoriteCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        AddServiceToUserFavoriteCommand command,
        CancellationToken cancellationToken)
    {
        var item =
            await dataSource.FindItemAsync<Model.Entities.Servicing.ServiceUserFavorite>([command.ServiceId, command.UserId], cancellationToken);

        if (item != null)
        {
            return Error.Failure(description: l["translate_service_is_already_in_favorites"]);
        }

        item = new()
        {
            ServiceId = command.ServiceId,
            UserId = command.UserId
        };

        await dataSource.AddItemAsync(item, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
