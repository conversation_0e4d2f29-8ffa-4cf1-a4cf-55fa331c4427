using ErrorOr;
using LinqKit.Core;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Features.Servicing.ServiceRequestTransactions;
using UnifiedHub.Model.Entities.Servicing;
using UnifiedHub.Servicing.Abstraction;
using UnifiedHub.Servicing.Persistence;

namespace UnifiedHub.Servicing.Commands.ServiceRequests.InvokeActionOnServiceRequest;

public sealed class InvokeActionOnServiceRequestHandler(
    IServicingDataSource dataSource,
    IEnumerable<IServiceConfig> serviceConfigs,
    IIdentityService identityService)
    : IRequestHandler<InvokeActionOnServiceRequestCommand, ErrorOr<ServiceRequestTransactionDto>>
{
    public async Task<ErrorOr<ServiceRequestTransactionDto>> Handle(
        InvokeActionOnServiceRequestCommand command,
        CancellationToken cancellationToken)
    {
        var service = dataSource
            .Items<ServiceRequest>()
            .Where(x => x.Id == command.Id)
            .Select(x => new { x.Service.BuiltInId })
            .SingleOrDefault();

        if (service == null)
        {
            return Error.NotFound();
        }

        var config = serviceConfigs.Single(x => x.ServiceBuiltInId == service.BuiltInId);
        var userId = identityService.User!.Id;
        var invocationResult = await config.InvokeAsync(
            command.Id,
            userId,
            command.Action,
            command.TransactionData,
            cancellationToken);

        if (invocationResult.IsError)
        {
            return ErrorOr<ServiceRequestTransactionDto>.From(invocationResult.Errors);
        }

        return dataSource
            .Items<ServiceRequestTransaction>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => x.Id == invocationResult.Value.Id)
            .Select(ServiceRequestTransactionDto.Mapper())
            .Single();
    }
}
