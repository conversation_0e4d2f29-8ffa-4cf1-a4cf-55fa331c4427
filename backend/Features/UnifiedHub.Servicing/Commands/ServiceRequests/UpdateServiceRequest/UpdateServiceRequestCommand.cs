using System.Text.Json.Nodes;
using ErrorOr;
using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Features.Servicing.Requests;

namespace UnifiedHub.Servicing.Commands.ServiceRequests.UpdateServiceRequest;

public sealed class UpdateServiceRequestCommand : IRequest<ErrorOr<ServiceRequestGetDto>>
{
    public Guid Id { get; set; }

    public JsonNode? Data { get; set; }

    public InvocationSource InvocationSource { get; set; } = InvocationSource.External;
}
