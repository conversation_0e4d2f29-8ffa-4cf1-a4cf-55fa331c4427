using System.Text.Json.Nodes;
using ErrorOr;
using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Features.Servicing.Requests;

namespace UnifiedHub.Servicing.Commands.Services.CreateServiceRequest;

public sealed class CreateServiceRequestCommand : IRequest<ErrorOr<ServiceRequestGetDto>>
{
    public Guid ServiceId { get; set; }

    public Guid? UserId { get; set; }

    public JsonNode? Data { get; set; }

    public InvocationSource InvocationSource { get; set; } = InvocationSource.External;
}
