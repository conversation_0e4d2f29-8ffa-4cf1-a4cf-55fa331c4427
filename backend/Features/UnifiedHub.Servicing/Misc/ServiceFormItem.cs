using UnifiedHub.Common.Misc;
using UnifiedHub.Servicing.Misc.Enums;

namespace UnifiedHub.Servicing.Misc;

public sealed class ServiceFormItem
{
    public string Id { get; set; } = null!;

    public MultilingualString Label { get; set; } = null!;

    public ServiceFormItemType Type { get; set; }

    public bool IsRequired { get; set; }

    public bool IsHidden { get; set; }

    public bool IsDisabled { get; set; }

    public object? DefaultValue { get; set; }

    public MultilingualString? Note { get; set; }

    public IEnumerable<Option>? Options { get; set; }

    /// <summary>
    /// Normally, when the form is submitted, the servicing backend
    /// ensures that all selected items exist int he `Options` list.
    /// If it is not, then the value of the form item will be
    /// reset. If you don't want this behavior and would like to
    /// retain the values even if they do not exist in `Options` set
    /// this parameter to `true`.
    /// </summary>
    public bool DisableSelectionInOptionsCheck { get; set; }

    public ServiceFormItemRepeatConfig? RepeatConfig { get; set; }

    public bool RefreshForm { get; set; }

    public bool RefreshStateList { get; set; }

    public record Option(string Id, MultilingualString Name);

    public record ServiceFormItemRepeatConfig(
        ServiceFormItem[] Items,
        bool IsAdditionDisabled = false);
}
