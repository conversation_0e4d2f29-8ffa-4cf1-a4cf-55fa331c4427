using System.Reflection;
using Microsoft.Extensions.DependencyInjection;
using UnifiedHub.Flows.Abstraction;

namespace UnifiedHub.Flows.Extensions;

public static class ServiceCollectionExtension
{
    public static IServiceCollection AddFlowConfigs(
        this IServiceCollection services,
        Assembly assembly)
    {
        assembly.GetTypes()
            .Where(x => x is { IsAbstract: false, IsClass: true })
            .Where(x => x.BaseType is { IsGenericType: true } &&
                        x.BaseType.GetGenericTypeDefinition().IsAssignableTo(typeof(FlowConfig<>)))
            .Select(x => new
            {
                FlowConfigType = x,
                EntityType = x.BaseType!.GetGenericArguments()[0],
            })
            .ToList()
            .ForEach(data =>
            {
                // Since we do not enforce an interface over flow entities, we need
                // to ensure that some properties exist in entity instances. Here we
                // check a head of time for that.
                var missingProperties = new[] { "Id", "FlowState" }
                    .Where(x => data.EntityType.GetProperty(x) == null).ToArray();
                if (missingProperties.Any())
                {
                    throw new Exception(
                        $"The entity `{data.EntityType.Name}` is missing properties: {string.Join(",", missingProperties.Select(x => $"`{x}`"))} to be used in a flow config class.");
                }

                services.AddScoped(typeof(FlowConfig<>).MakeGenericType(data.EntityType), data.FlowConfigType);
                services.AddScoped(typeof(IFlowConfig<>).MakeGenericType(data.EntityType), data.FlowConfigType);
                services.AddScoped(typeof(IFlowConfig), data.FlowConfigType);
                services.AddScoped(data.FlowConfigType);
            });

        return services;
    }
}
