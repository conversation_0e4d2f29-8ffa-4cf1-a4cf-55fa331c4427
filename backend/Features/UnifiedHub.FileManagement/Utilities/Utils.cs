using MapsterMapper;
using MediatR;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.FileManagement.Commands.UploadAndReplace;

namespace UnifiedHub.FileManagement.Utilities;

public static class Utils
{
    public static async Task<Guid?> UploadFile(FileUploadAndReplaceDto? fileDto,
        Guid? previousId,
        ISender mediator,
        IMapper mapper,
        CancellationToken cancellationToken = default)
    {
        var command = new UploadAndReplaceCommand();
        mapper.Map(fileDto, command);
        command.PreviousId = previousId;
        return (await mediator.Send(command, cancellationToken)).Value?.Id;
    }
}
