using MediatR;
using UnifiedHub.Emailing.Commands.SendEmail;
using UnifiedHub.Emailing.Interfaces;
using UnifiedHub.Emailing.Misc;

namespace UnifiedHub.Emailing.Commands.SendDefaultEmail;

public class SendDefaultEmailHandler(
    IEmailRenderer renderer,
    ISender mediator) : IRequestHandler<SendDefaultEmailCommand>
{
    public async Task Handle(SendDefaultEmailCommand command, CancellationToken cancellationToken)
    {
        await mediator.Send(new SendEmailCommand
        {
            Subject = command.Subject,
            To = command.To,
            UserIds = command.UserIds,
            Attachments = command.Attachments,
            Body = await renderer.RenderAsync(
                "Default",
                new DefaultViewModel(command.Body),
                cancellationToken)
        }, cancellationToken);
    }
}
