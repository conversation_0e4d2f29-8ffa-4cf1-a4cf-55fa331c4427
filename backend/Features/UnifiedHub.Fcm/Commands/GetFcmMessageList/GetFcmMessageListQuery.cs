using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Features.Fcm.FcmMessages;

namespace UnifiedHub.Fcm.Commands.GetFcmMessageList;

public sealed class GetFcmMessageListQuery : IRequest<PaginatedResult<FcmMessageListDto>>
{
    public DateTime? From { get; set; }

    public DateTime? To { get; set; }

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;
}
