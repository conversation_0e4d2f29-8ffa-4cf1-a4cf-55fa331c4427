using System.Text.Json.Nodes;
using FirebaseAdmin.Messaging;
using MapsterMapper;
using MediatR;
using UnifiedHub.Common.Extensions;
using UnifiedHub.Fcm.Persistence;
using UnifiedHub.Fcm.Services;
using UnifiedHub.Model.Entities.Fcm;

namespace UnifiedHub.Fcm.Commands.PushFcmMessageToUser;

public sealed class PushFcmMessageHandler(
    IFcmDataSource dataSource,
    IFcmService fcmService,
    IMapper mapper) : IRequestHandler<PushFcmMessageCommand, Unit>
{
    public async Task<Unit> Handle(
        PushFcmMessageCommand command,
        CancellationToken cancellationToken)
    {
        var tokensAndUserIds = dataSource
            .Items<FcmToken>()
            .Where(x => command.UserIds.Contains(x.UserId)).Select(x => new
            {
                x.UserId,
                x.Token
            })
            .ToList();

        if (tokensAndUserIds.Count == 0 || fcmService.Messaging == null)
        {
            return Unit.Value;
        }

        // Save the message first once for every user (not for every token).
        var messages = tokensAndUserIds.GroupBy(x => x.UserId).Select(x =>
        {
            var message = mapper.Map<FcmMessage>(command);
            message.UserId = x.Key;
            message.Data = DictionaryToJsonNode(command.Data);
            return message;
        });
        await dataSource.AddItemsAsync(messages, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);


        // Push the messages to the tokens.
        await fcmService.Messaging.SendEachAsync(tokensAndUserIds.Select(token => new Message
        {
            Token = token.Token,
            Data = command.Data,
            Notification = new Notification
            {
                Title = command.Title.SerializeToJsonString(),
                Body = command.Body.SerializeToJsonString(),
            },
            Android = new AndroidConfig
            {
                Priority = command.IsHighPriority ? Priority.High : Priority.Normal,
                TimeToLive = command.IsShortTtl ? TimeSpan.Zero : null,
            },
        }), cancellationToken);

        return Unit.Value;
    }

    private static JsonNode DictionaryToJsonNode(Dictionary<string, string> dictionary)
    {
        var jsonObject = new JsonObject();

        foreach (var kvp in dictionary)
        {
            jsonObject[kvp.Key] = JsonValue.Create(kvp.Value);
        }

        return jsonObject;
    }
}
