using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Misc;

namespace UnifiedHub.Model.Entities.Servicing;

public sealed class ServiceSubcategory : AuditableWithSoftDeleteEntity
{
    public Guid Id { get; set; }

    public string BuiltInId { get; set; } = null!;

    public Guid CategoryId { get; set; }
    public ServiceCategory Category { get; set; } = null!;

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? Description { get; set; }

    public ICollection<Service> Services { get; set; } = null!;
}
