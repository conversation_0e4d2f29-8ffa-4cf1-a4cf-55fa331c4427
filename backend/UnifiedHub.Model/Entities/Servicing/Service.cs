using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Misc;

namespace UnifiedHub.Model.Entities.Servicing;

public sealed class Service : AuditableWithSoftDeleteEntity
{
    public Guid Id { get; set; }

    public string BuiltInId { get; set; } = null!;

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? Description { get; set; }

    public Guid? SubcategoryId { get; set; }

    public ServiceSubcategory? Subcategory { get; set; }

    public bool IsProxyApplicationEnabled { get; set; }

    public bool IsArchived { get; set; }

    public bool IsSystemManaged { get; set; }

    public string[] Tags { get; set; } = [];

    public ICollection<ServiceState> States { get; set; } = null!;

    public ICollection<ServiceAction> Actions { get; set; } = null!;

    public ICollection<ServiceRequirement> Requirements { get; set; } = null!;

    public ICollection<ServiceRequest> Requests { get; set; } = [];

    public ICollection<ServiceUserFavorite> UserFavorites { get; set; } = [];
    
    public ICollection<ServiceStateTransition> Transitions { get; set; } = [];
}

public sealed class ServiceState
{
    public string Value { get; init; } = null!;

    public MultilingualString Name { get; init; } = null!;

    public MultilingualString? Description { get; init; }


    public override bool Equals(object? obj)
    {
        return obj is ServiceState other &&
               other.Value == Value &&
               other.Name == Name &&
               other.Description == Description;
    }

    public override int GetHashCode()
    {
        var hash = 17;
        hash = (hash * 23) + Value.GetHashCode();
        hash = (hash * 23) + Name.GetHashCode();
        hash = (hash * 23) + (Description?.GetHashCode() ?? 0);
        return hash;
    }
}

public sealed class ServiceAction
{
    public string Key { get; init; } = null!;

    public MultilingualString Name { get; init; } = null!;


    public override bool Equals(object? obj)
    {
        return obj is ServiceState other &&
               other.Value == Key &&
               other.Name == Name;
    }

    public override int GetHashCode()
    {
        var hash = 17;
        hash = (hash * 23) + Key.GetHashCode();
        hash = (hash * 23) + Name.GetHashCode();
        return hash;
    }
}

public sealed class ServiceRequirement
{
    public MultilingualString Name { get; init; } = null!;

    public override bool Equals(object? obj)
    {
        return obj is ServiceRequirement other && other.Name == Name;
    }

    public override int GetHashCode()
    {
        return Name.GetHashCode();
    }
}
