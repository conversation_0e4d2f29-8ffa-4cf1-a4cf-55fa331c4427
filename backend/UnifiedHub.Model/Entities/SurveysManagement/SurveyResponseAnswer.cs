using UnifiedHub.Model.Misc;

namespace UnifiedHub.Model.Entities.SurveysManagement;

public sealed class SurveyResponseAnswer : AuditableEntity, ILoggableEntity
{
    public Guid QuestionId { get; set; }
    public SurveyQuestion Question { get; set; } = null!;

    public Guid ResponseId { get; set; }
    public SurveyResponse Response { get; set; } = null!;

    public string? Value { get; set; }
    
    // For checkbox questions that can have multiple values
    public string[] Values { get; set; } = [];
}
