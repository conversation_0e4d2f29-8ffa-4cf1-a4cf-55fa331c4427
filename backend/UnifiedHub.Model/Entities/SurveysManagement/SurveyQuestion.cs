using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Misc;

namespace UnifiedHub.Model.Entities.SurveysManagement;

public sealed class SurveyQuestion : AuditableWithSoftDeleteEntity, ILoggableEntity
{
    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? Description { get; set; }

    public QuestionType Type { get; set; } = QuestionType.Text;

    public int Order { get; set; }

    public bool IsRequired { get; set; }

    public SurveyQuestionOption[] Options { get; set; } = new SurveyQuestionOption[0];

    public Guid SurveyId { get; set; }
    public Survey Survey { get; set; } = null!;

    public ICollection<SurveyResponseAnswer> Answers { get; set; } = new List<SurveyResponseAnswer>();

    public enum QuestionType
    {
        Text,
        Radio,
        Checkbox,
        Select
    }
}

public sealed class SurveyQuestionOption
{
    public string Value { get; init; } = null!;

    public MultilingualString Label { get; init; } = null!;

    public override bool Equals(object? obj)
    {
        return obj is SurveyQuestionOption other &&
               other.Value == Value &&
               other.Label == Label;
    }

    public override int GetHashCode()
    {
        var hash = 17;
        hash = (hash * 23) + Value.GetHashCode();
        hash = (hash * 23) + Label.GetHashCode();
        return hash;
    }
}
