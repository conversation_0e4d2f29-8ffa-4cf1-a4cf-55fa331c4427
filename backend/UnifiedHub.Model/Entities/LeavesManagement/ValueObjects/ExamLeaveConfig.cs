namespace UnifiedHub.Model.Entities.LeavesManagement.ValueObjects;

public sealed class ExamLeaveConfig
{
    public int? OnshoreMaxPerYearDurationInDays { get; init; }

    public int? OffshoreMaxPerYearDurationInDays { get; init; }

    public override bool Equals(object? obj)
    {
        return obj is ExamLeaveConfig other &&
               other.OnshoreMaxPerYearDurationInDays == OnshoreMaxPerYearDurationInDays &&
               other.OffshoreMaxPerYearDurationInDays == OffshoreMaxPerYearDurationInDays;
    }

    public override int GetHashCode()
    {
        var hash = 17;
        hash = (hash * 23) + (OnshoreMaxPerYearDurationInDays?.GetHashCode() ?? 0);
        hash = (hash * 23) + (OffshoreMaxPerYearDurationInDays?.GetHashCode() ?? 0);
        return hash;
    }

    public ExamLeaveConfig Clone()
    {
        return new ExamLeaveConfig
        {
            OnshoreMaxPerYearDurationInDays = OnshoreMaxPerYearDurationInDays,
            OffshoreMaxPerYearDurationInDays = OffshoreMaxPerYearDurationInDays,
        };
    }
}
