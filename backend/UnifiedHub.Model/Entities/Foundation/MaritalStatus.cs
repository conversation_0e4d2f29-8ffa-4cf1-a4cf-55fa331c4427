using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Misc;

namespace UnifiedHub.Model.Entities.Foundation;

public sealed class MaritalStatus : AuditableWithSoftDeleteEntity, ILoggableEntity
{
    public Guid Id { get; set; }

    public string? BuiltInId { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? Description { get; set; }

    public const string BuiltInIdSingle = "single";
    public const string BuiltInIdMarried = "married";

    // TODO: RMV
    public int OldId { get; set; }
}
