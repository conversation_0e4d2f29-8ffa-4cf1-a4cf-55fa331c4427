using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Misc;

namespace UnifiedHub.Model.Entities.CoursesManagement;

public sealed class CourseStatusType : AuditableWithSoftDeleteEntity, ILoggableEntity
{
    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? Description { get; set; }

    // TODO: RMV
    public int? OldId { get; set; }
}
