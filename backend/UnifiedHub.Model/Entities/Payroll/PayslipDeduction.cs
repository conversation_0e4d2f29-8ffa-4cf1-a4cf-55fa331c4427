using UnifiedHub.Model.Entities.Payroll.Misc;
using UnifiedHub.Model.Misc;

namespace UnifiedHub.Model.Entities.Payroll;

public sealed class PayslipDeduction : AuditableEntity, ISalaryAllowanceOrDeduction
{
    public Guid Id { get; set; }

    public Guid PayslipId { get; set; }
    public Payslip Payslip { get; set; } = null!;

    public Guid TypeId { get; set; }
    public SalaryDeductionType Type { get; set; } = null!;

    public DateOnly From { get; set; }

    public DateOnly? To { get; set; }

    public decimal? Amount { get; set; }

    public string? Notes { get; set; }
}
