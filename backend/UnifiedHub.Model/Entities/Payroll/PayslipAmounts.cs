namespace UnifiedHub.Model.Entities.Payroll;

/// <summary>
/// This is a read-only entity mapped to a sql view.
/// </summary>
public sealed class PayslipAmounts
{
    public Guid PayslipId { get; set; }

    public decimal Amount { get; set; }

    public decimal BaseAmount { get; set; }

    public decimal TotalAllowanceAmount { get; set; }

    public decimal TotalDeductionAmount { get; set; }

    public decimal TotalBonusAmount { get; set; }

    public decimal TotalReductionAmount { get; set; }

    public decimal? PensionDeductionAmount { get; set; }

    public decimal TotalAmount { get; set; }
}
