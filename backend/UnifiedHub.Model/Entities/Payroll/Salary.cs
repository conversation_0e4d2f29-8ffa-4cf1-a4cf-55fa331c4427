using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Misc;

namespace UnifiedHub.Model.Entities.Payroll;

public sealed class Salary : AuditableWithSoftDeleteEntity, ILoggableEntity
{
    public Guid Id { get; set; }

    public Guid EmployeeId { get; set; }
    public Employee Employee { get; set; } = null!;

    public decimal Amount { get; set; }

    public decimal? PensionDeductionAmount { get; set; }

    public Guid? CurrencyId { get; set; }
    public Currency? Currency { get; set; }

    public DateTime? ClosureDate { get; set; }

    public ICollection<SalaryAllowance> Allowances { get; set; } = [];
    public ICollection<SalaryDeduction> Deductions { get; set; } = [];

    public ICollection<SalaryBonusOrReduction> BonusOrReductions { get; set; } = [];

    public ICollection<SalaryChangeTransaction> ChangeTransactions { get; set; } = [];
}
