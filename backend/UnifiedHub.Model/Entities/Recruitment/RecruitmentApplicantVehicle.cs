using UnifiedHub.Model.Misc;

namespace UnifiedHub.Model.Entities.Recruitment;

public sealed class RecruitmentApplicantVehicle : AuditableEntity, ILoggableEntity
{
    public Guid Id { get; set; }

    public Guid ApplicantId { get; set; }
    public RecruitmentApplicant Applicant { get; set; } = null!;

    public string? Name { get; set; }

    public int? Year { get; set; }

    public string? PlateNumber { get; set; }

    public string? IssueAuthority { get; set; }

    public string? Color { get; set; }

    public string? PlateCategory { get; set; }

    public string? TrafficNumber { get; set; }

    public string? Notes { get; set; }
}
