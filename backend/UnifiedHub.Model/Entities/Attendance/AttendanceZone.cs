using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Misc;

namespace UnifiedHub.Model.Entities.Attendance;

public sealed class AttendanceZone : AuditableWithSoftDeleteEntity, ILoggableEntity
{
    public Guid Id { get; set; }

    public MultilingualString Name { get; set; } = null!;

    public double Longitude { get; set; }

    public double Latitude { get; set; }

    public double RadiusInMeters { get; set; }

    public ICollection<AttendanceZoneEmployeeLink> EmployeeLinks { get; set; } = [];
}
