using UnifiedHub.Model.Misc;
using File = UnifiedHub.Model.Entities.Files.File;

namespace UnifiedHub.Model.Entities.Employment;

public sealed class EmployeeMedicalExemption : AuditableWithSoftDeleteEntity, ILoggableEntity
{
    public Guid Id { get; set; }

    public Guid EmployeeId { get; set; }
    public Employee Employee { get; set; } = null!;

    public DateTime StartDate { get; set; }

    public DateTime EndDate { get; set; }

    public double DayCount { get; set; }

    public Guid? AttachmentFileId { get; set; }
    public File? AttachmentFile { get; set; }

    public Guid? TypeId { get; set; }
    public EmployeeMedicalExemptionType? Type { get; set; }
}
