using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Misc;

namespace UnifiedHub.Model.Entities.Employment;

public sealed class EmployeeInsurance : AuditableWithSoftDeleteEntity, ILoggableEntity
{
    public Guid Id { get; set; }

    public Guid EmployeeId { get; set; }
    public Employee Employee { get; set; } = null!;

    public MultilingualString InsuranceCompanyName { get; set; } = null!;

    public InsuranceBeneficiaryType BeneficiaryType { get; set; } = InsuranceBeneficiaryType.Self;

    public MultilingualString? BeneficiaryName { get; set; }

    public string? BeneficiaryGender { get; set; }

    public DateTime? BeneficiaryDateOfBirth { get; set; }

    public string? BeneficiaryRelation { get; set; }

    public string? UaeId { get; set; }

    public InsuranceState State { get; set; } = InsuranceState.Active;

    public string? Notes { get; set; }

    public enum InsuranceBeneficiaryType
    {
        Self,
        Other
    }

    public enum InsuranceState
    {
        Active,
        Inactive
    }
}
