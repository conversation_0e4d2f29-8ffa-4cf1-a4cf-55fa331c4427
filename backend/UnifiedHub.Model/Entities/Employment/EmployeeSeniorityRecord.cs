using UnifiedHub.Model.Misc;
using File = UnifiedHub.Model.Entities.Files.File;

namespace UnifiedHub.Model.Entities.Employment;

public class EmployeeSeniorityRecord : AuditableWithSoftDeleteEntity, ILoggableEntity
{
    public Guid Id { get; set; }
    
    public Guid EmployeeId { get; set; }
    public Employee Employee { get; set; } = null!;
    
    public DateTime RecordDate { get; set; }

    public string Type { get; set; } = null!;
    
    public int PeriodInMonths { get; set; }
    
    public string? Reason { get; set; }
    
    public string? ReferenceBookNumber { get; set; }
    
    public string? Notes { get; set; }

    public Guid? FileId { get; set; }
    public File? File { get; set; }

    public const string TypeAdd = "add";
    public const string TypeDeduct = "deduct";

}
