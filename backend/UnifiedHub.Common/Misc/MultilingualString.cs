using System.Globalization;
using System.Text.Json.Serialization;

namespace UnifiedHub.Common.Misc;

public sealed class MultilingualString : IComparable
{
    public string Ar { get; init; } = "";

    public string En { get; init; } = "";

    [JsonIgnore] public string Value => CultureInfo.CurrentUICulture.Name.StartsWith("ar") ? Ar : En;

    public override bool Equals(object? obj)
    {
        return obj is MultilingualString other && other.Ar == Ar && other.En == En;
    }

    public override int GetHashCode()
    {
        var hash = 17;
        hash = (hash * 23) + Ar.GetHashCode();
        hash = (hash * 23) + En.GetHashCode();
        return hash;
    }

    public int CompareTo(object? obj)
    {
        return obj is MultilingualString other
            ? string.Compare(En + Ar, other.En + other.Ar, StringComparison.Ordinal)
            : -1;
    }

    public MultilingualString Clone()
    {
        return new MultilingualString { Ar = Ar, En = En };
    }
}
