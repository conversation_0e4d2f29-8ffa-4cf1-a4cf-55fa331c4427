namespace UnifiedHub.Common.Misc;

public sealed class ObjectChangeSnapshot
{
    public string Type { get; set; } = null!;

    public object?[]? Id { get; set; }

    public IEnumerable<ObjectChange> Changes { get; set; } = [];

    public sealed class ObjectChange
    {
        public string PropertyName { get; set; } = null!;

        public object? OldValue { get; set; }

        public object? NewValue { get; set; }
    }
}
