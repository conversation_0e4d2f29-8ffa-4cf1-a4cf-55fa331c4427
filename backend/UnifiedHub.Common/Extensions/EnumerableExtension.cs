namespace UnifiedHub.Common.Extensions;

public static class EnumerableExtension
{
    public static IEnumerable<TSource> Diff<TSource>(this IEnumerable<TSource> source, IEnumerable<TSource> other)
    {
        var otherHashSet = other.ToHashSet();
        return source.Where(x => !otherHashSet.Contains(x));
    }

    public static IEnumerable<TSource> DiffBy<TSource, TOther, TKey>(
        this IEnumerable<TSource> source,
        IEnumerable<TOther> other,
        Func<TSource, TKey> sourceKeySelector,
        Func<TOther, TKey> otherKeySelector)
    {
        var otherHashSet = other.Select(otherKeySelector).ToHashSet();
        return source.Where(x => !otherHashSet.Contains(sourceKeySelector(x)));
    }

    public static IEnumerable<TSource> IntersectBy<TSource, TOther, TKey>(
        this IEnumerable<TSource> source,
        IEnumerable<TOther> other,
        Func<TSource, TKey> sourceKeySelector,
        Func<TOther, TKey> otherKeySelector)
    {
        var otherHashSet = other.Select(otherKeySelector).ToHashSet();
        return source.Where(x => otherHashSet.Contains(sourceKeySelector(x)));
    }

    public static int FindIndex<T>(this IEnumerable<T> source, Func<T, bool> predicate)
    {
        return source
            .Select((item, index) => new { Item = item, Index = index })
            .Where(x => predicate(x.Item))
            .Select(x => x.Index)
            .FirstOrDefault(-1);
    }
}
