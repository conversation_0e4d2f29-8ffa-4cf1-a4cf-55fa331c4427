using System.Text.Json;
using System.Text.Json.Nodes;

namespace UnifiedHub.Common.Extensions;

public static class StringExtension
{
    private static readonly JsonSerializerOptions JsonSerializerOptions = new()
    {
        PropertyNameCaseInsensitive = true
    };

    public static string Last(this string str, int count)
    {
        return str.Length < count ? str : str[^count..];
    }

    public static T? DeserializeFromJsonString<T>(this string? str) where T : class
    {
        return string.IsNullOrEmpty(str) ? null : SafeDeserialize<T>(str, JsonSerializerOptions);
    }

    public static JsonNode ToJson(this string str)
    {
        return JsonNode.Parse(str)!;
    }

    private static T? SafeDeserialize<T>(string json, JsonSerializerOptions options)
    {
        try
        {
            return JsonSerializer.Deserialize<T>(json, options);
        }
        catch (JsonException)
        {
            return default; // Returns null for reference types.
        }
    }
}
