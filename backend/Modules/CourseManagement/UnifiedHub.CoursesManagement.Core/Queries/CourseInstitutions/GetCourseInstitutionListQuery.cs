using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.CoursesManagement.CourseInstitutions;

namespace UnifiedHub.CoursesManagement.Core.Queries.CourseInstitutions;

public sealed class GetCourseInstitutionListQuery : IRequest<PaginatedResult<CourseInstitutionListDto>>
{
    public string? Keyword { get; set; }

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;
}
