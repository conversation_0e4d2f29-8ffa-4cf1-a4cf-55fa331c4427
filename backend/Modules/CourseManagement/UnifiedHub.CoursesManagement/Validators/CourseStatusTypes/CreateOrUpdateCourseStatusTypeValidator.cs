using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.CoursesManagement.Core.Commands.CourseStatusTypes;

namespace UnifiedHub.CoursesManagement.Validators.CourseStatusTypes;

public sealed class CreateCourseStatusTypeValidator : AbstractValidator<CreateCourseStatusTypeCommand>
{
    public CreateCourseStatusTypeValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public sealed class UpdateCourseStatusTypeValidator : AbstractValidator<UpdateCourseStatusTypeCommand>
{
    public UpdateCourseStatusTypeValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public static class AbstractValidatorApply
{
    public static void Apply<TCommand>(
        this AbstractValidator<TCommand> validator,
        IStringLocalizer<SharedResource> l)
        where TCommand : CreateOrUpdateCourseStatusTypeCommand
    {
        validator.RuleFor(x => x.Name)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_name"]]);
    }
}
