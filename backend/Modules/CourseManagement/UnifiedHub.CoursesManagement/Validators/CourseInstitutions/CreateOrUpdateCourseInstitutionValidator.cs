using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.CoursesManagement.Core.Commands.CourseInstitutions;

namespace UnifiedHub.CoursesManagement.Validators.CourseInstitutions;

public sealed class CreateCourseInstitutionValidator : AbstractValidator<CreateCourseInstitutionCommand>
{
    public CreateCourseInstitutionValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public sealed class CreateOrUpdateCourseInstitutionValidator : AbstractValidator<UpdateCourseInstitutionCommand>
{
    public CreateOrUpdateCourseInstitutionValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public static class AbstractValidatorApply
{
    public static void Apply<TCommand>(
        this AbstractValidator<TCommand> validator,
        IStringLocalizer<SharedResource> l)
        where TCommand : CreateOrUpdateCourseInstitutionCommand
    {
        validator.RuleFor(x => x.Name)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_name"]]);
    }
}
