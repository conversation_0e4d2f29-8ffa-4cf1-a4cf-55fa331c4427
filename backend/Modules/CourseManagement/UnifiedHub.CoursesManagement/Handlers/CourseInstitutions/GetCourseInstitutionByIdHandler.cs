using ErrorOr;
using LinqKit.Core;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.CoursesManagement.Core.Queries.CourseInstitutions;
using UnifiedHub.CoursesManagement.Persistence;
using UnifiedHub.Dtos.Modules.CoursesManagement.CourseInstitutions;
using UnifiedHub.Model.Entities.CoursesManagement;

namespace UnifiedHub.CoursesManagement.Handlers.CourseInstitutions;

public sealed class GetCourseInstitutionByIdHandler(
    ICoursesManagementDataSource dataSource)
    : IRequestHandler<GetCourseInstitutionByIdQuery, ErrorOr<CourseInstitutionGetDto>>
{
    public async Task<ErrorOr<CourseInstitutionGetDto>> Handle(
        GetCourseInstitutionByIdQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var item = dataSource
            .Items<CourseInstitution>()
            .AsNoTracking()
            .AsExpandable()
            .Select(CourseInstitutionGetDto.Mapper())
            .SingleOrDefault(x => x.Id == query.Id);

        return item == null ? Error.NotFound() : (ErrorOr<CourseInstitutionGetDto>) item;
    }
}
