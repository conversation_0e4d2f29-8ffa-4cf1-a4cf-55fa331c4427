using ErrorOr;
using MediatR;
using UnifiedHub.CoursesManagement.Core.Commands.CourseTypes;
using UnifiedHub.CoursesManagement.Persistence;
using UnifiedHub.Model.Entities.CoursesManagement;

namespace UnifiedHub.CoursesManagement.Handlers.CourseTypes;

public sealed class DeleteCourseTypeHandler(
    ICoursesManagementDataSource dataSource) : IRequestHandler<DeleteCourseTypeCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        DeleteCourseTypeCommand command,
        CancellationToken cancellationToken)
    {
        var item = dataSource
            .Items<CourseType>()
            .SingleOrDefault(x => x.Id == command.Id);

        if (item == null)
        {
            return Error.NotFound();
        }

        await dataSource.RemoveItemAsync(item, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
