using ErrorOr;
using LinqKit.Core;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.CoursesManagement.Core.Queries.CourseResults;
using UnifiedHub.CoursesManagement.Persistence;
using UnifiedHub.Dtos.Modules.CoursesManagement.CourseResults;
using UnifiedHub.Model.Entities.CoursesManagement;

namespace UnifiedHub.CoursesManagement.Handlers.CourseResults;

public sealed class GetCourseResultByIdHandler(
    ICoursesManagementDataSource dataSource)
    : IRequestHandler<GetCourseResultByIdQuery, ErrorOr<CourseResultGetDto>>
{
    public async Task<ErrorOr<CourseResultGetDto>> Handle(
        GetCourseResultByIdQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var item = dataSource
            .Items<CourseResult>()
            .AsNoTracking()
            .AsExpandable()
            .Select(CourseResultGetDto.Mapper())
            .SingleOrDefault(x => x.Id == query.Id);

        return item == null ? Error.NotFound() : (ErrorOr<CourseResultGetDto>) item;
    }
}
