using System.Text.Json.Nodes;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Extensions;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeChildren;
using UnifiedHub.EmployeesManagement.Core.Rbac;
using UnifiedHub.EmployeesManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Entities.Foundation;
using UnifiedHub.Model.Entities.Servicing;
using UnifiedHub.Servicing.Misc;
using UnifiedHub.Servicing.Misc.Enums;
using UnifiedHub.Shared.Servicing;
using UnifiedHub.Shared.Servicing.Abstraction;

namespace UnifiedHub.EmployeesManagement.Servicing;

public sealed class SocialAllowanceAdditionServiceConfig(
    IServiceProvider serviceProvider,
    IEmployeesDataSource dataSource,
    ISender mediator)
    : SupervisorTwoPermissionServiceConfig(serviceProvider)
{
    public override string ServiceBuiltInId => "services:social_allowance_addition";

    public override ServiceDefinition ServiceDefinition => new(
        ServiceBuiltInId,
        new() { Ar = "طلب إضافة العلاوة الاجتماعية", En = "Request addition social allowance" },
        ServiceSubcategoryDefinitions.Employees.Id);

    protected override string FirstPermission => P.Employees.Approve;

    protected override string SecondPermission => P.Employees.GeneralManagerApprove;

    public override async Task<ServiceFormItem[]> GetRequestFormItemsAsync(ServiceRequest? request,
        Guid? userId,
        JsonNode? data, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;
        return
        [
            new()
            {
                Id = "name",
                Type = ServiceFormItemType.Text,
                Label = new() { Ar = "اسم المولود", En = "New born name" },
                IsRequired = true
            },

            new()
            {
                Id = "order",
                Type = ServiceFormItemType.Number,
                Label = new() { Ar = "ترتيب المولود", En = "New born order" },
                IsRequired = true
            },

            new()
            {
                Id = "gender",
                Type = ServiceFormItemType.Option,
                Label = new() { Ar = "الجنس", En = "Gender" },
                IsRequired = true,
                Options =
                [
                    new ServiceFormItem.Option("male", new() { Ar = "ذكر", En = "Male" }),
                    new ServiceFormItem.Option("female", new() { Ar = "أنثى", En = "Female" })
                ]
            },

            new()
            {
                Id = "dateOfBirth",
                Type = ServiceFormItemType.Date,
                Label = new() { Ar = "تاريخ الميلاد", En = "Date of birth" },
                IsRequired = true
            },

            new()
            {
                Id = "placeOfBirth",
                Type = ServiceFormItemType.Text,
                Label = new() { Ar = "مكان الولادة", En = "Place of birth" },
                IsRequired = true
            },


            new()
            {
                Id = "nationality",
                Label = new() { Ar = "جنسية المولود", En = "New born nationality" },
                Type = ServiceFormItemType.Option,
                IsRequired = true,
                Options = dataSource.Items<Nationality>()
                    .AsNoTracking()
                    .OrderBy(x => x.Order)
                    .Select(x => new ServiceFormItem.Option(x.Id.ToString(), x.Name))
                    .ToArray()
            },

            new()
            {
                Id = "nationalIdNumber",
                Label = new() { Ar = "رقم الهوية", En = "National ID number" },
                Type = ServiceFormItemType.Text,
                IsRequired = true
            },

            new()
            {
                Id = "nationalIdExpirationDate",
                Label = new() { Ar = "تاريخ انتهاء الهوية", En = "National ID expiration date" },
                Type = ServiceFormItemType.Date,
                IsRequired = true
            },

            new()
            {
                Id = "motherName",
                Label = new() { Ar = "اسم الأم", En = "Mother's name" },
                Type = ServiceFormItemType.Text,
                IsRequired = true
            },

            new()
            {
                Id = "birthCertificateAttachment",
                Type = ServiceFormItemType.File,
                Label = new() { Ar = "مرفق شهادة الميلاد", En = "Birth certificate attachment" },
                IsRequired = true
            },

            new()
            {
                Id = "childIdAttachment",
                Type = ServiceFormItemType.File,
                Label = new() { Ar = "مرفق هوية الابن/الابنة", En = "Child ID attachment" },
                IsRequired = true
            },

            new()
            {
                Id = "childPassportAttachment",
                Type = ServiceFormItemType.File,
                Label = new() { Ar = "مرفق صورة جواز الابن/الابنة", En = "Child passport attachment" },
                IsRequired = true
            },

            new()
            {
                Id = "allowanceAdditionForm",
                Type = ServiceFormItemType.File,
                Label = new() { Ar = "نموذج طلب صرف العلاوة الإجتماعية", En = "Allowance addition form" },
                IsRequired = true
            }
        ];
    }

    protected override async Task OnActionInvokedAsync(
        ServiceRequest request,
        ServiceRequestTransaction transaction,
        CancellationToken cancellationToken = default)
    {
        if (transaction.ToState != States.Finalized || transaction.Action != Actions.Approve)
        {
            await base.OnActionInvokedAsync(request, transaction, cancellationToken);
            return;
        }

        var employeeId = dataSource.Items<Employee>().SingleOrDefault(x => x.UserId == request.UserId)?.Id;
        if (employeeId == null)
        {
            await base.OnActionInvokedAsync(request, transaction, cancellationToken);
            return;
        }

        // First we need to create the child:
        var createCommand = new CreateEmployeeChildCommand
        {
            EmployeeId = employeeId,
            Name = new()
            {
                Ar = request.Data["name"]?.GetValueOrDefault<string>() ?? "",
                En = request.Data["name"]?.GetValueOrDefault<string>() ?? ""
            },
            Order = request.Data["order"]?.GetValueOrDefault<int>() ?? 0,
            Gender = request.Data["gender"]?["id"]?.GetValueOrDefault<string>(),
            DateOfBirth = request.Data["dateOfBirth"]?.GetValueOrDefault<DateTime>(),
            PlaceOfBirth = request.Data["placeOfBirth"]?.GetValueOrDefault<string>(),
            BirthCertificateFile = request.Data["birthCertificateAttachment"] == null
                ? null
                : new FileUploadAndReplaceDto
                {
                    Id = request.Data["birthCertificateAttachment"]?["id"]?.GetValueOrDefault<Guid>()
                },
            NationalIdFile = request.Data["childIdAttachment"] == null
                ? null
                : new FileUploadAndReplaceDto
                {
                    Id = request.Data["childIdAttachment"]?["id"]?.GetValueOrDefault<Guid>()
                },
            PassportFile = request.Data["childPassportAttachment"] == null
                ? null
                : new FileUploadAndReplaceDto
                {
                    Id = request.Data["childPassportAttachment"]?["id"]?.GetValueOrDefault<Guid>()
                },
            AllowanceFormFile = request.Data["allowanceAdditionForm"] == null
                ? null
                : new FileUploadAndReplaceDto
                {
                    Id = request.Data["allowanceAdditionForm"]?["id"]?.GetValueOrDefault<Guid>()
                },
            NationalityId = request.Data["nationality"]?["id"]?.GetValueOrDefault<Guid>(),
            NationalIdNumber = request.Data["nationalIdNumber"]?.GetValueOrDefault<string>(),
            MotherName = request.Data["motherName"]?.GetValueOrDefault<string>(),
            AllowanceStatus = EmployeeChild.AllowanceStatusActive,
            ServiceRequestId = request.Id
        };

        var creationResult = await mediator.Send(createCommand, cancellationToken);
        if (creationResult.IsError)
        {
            await base.OnActionInvokedAsync(request, transaction, cancellationToken);
            return;
        }


        // And then we need set the allowance status to active
        var childId = creationResult.Value.Id;
        var updateStatusCommand = new UpdateEmployeeChildAllowanceStatusCommand
        {
            Id = childId,
            AllowanceStatus = EmployeeChild.AllowanceStatusActive
        };
        await mediator.Send(updateStatusCommand, cancellationToken);


        await base.OnActionInvokedAsync(request, transaction, cancellationToken);
    }
}
