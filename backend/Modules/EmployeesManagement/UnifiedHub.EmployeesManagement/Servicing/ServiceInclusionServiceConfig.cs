using System.Text.Json.Nodes;
using UnifiedHub.Common.Misc;
using UnifiedHub.EmployeesManagement.Core.Rbac;
using UnifiedHub.Model.Entities.Servicing;
using UnifiedHub.Servicing.Misc;
using UnifiedHub.Servicing.Misc.Enums;
using UnifiedHub.Shared.Servicing;
using UnifiedHub.Shared.Servicing.Abstraction;

namespace UnifiedHub.EmployeesManagement.Servicing;

// TODO: implement the rest of the functionality later.
public sealed class ServiceInclusionServiceConfig(IServiceProvider serviceProvider)
    : SupervisorTwoPermissionServiceConfig(serviceProvider)
{
    public override string ServiceBuiltInId => "services:service_inclusion";

    public override ServiceDefinition ServiceDefinition => new(
        ServiceBuiltInId,
        new MultilingualString { Ar = "طلب ضم خدمة", En = "Request service inclusion" },
        ServiceSubcategoryDefinitions.Employees.Id);

    protected override string FirstPermission => P.Employees.Approve;

    protected override string SecondPermission => P.Employees.GeneralManagerApprove;

    public override async Task<ServiceFormItem[]> GetRequestFormItemsAsync(ServiceRequest? request,
        Guid? userId,
        JsonNode? data, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;
        return
        [
            new ServiceFormItem
            {
                Id = "previousWorkEntityName",
                Type = ServiceFormItemType.Text,
                Label = new MultilingualString { Ar = "اسم جهة العمل السابقة", En = "Previous work entity name" },
                IsRequired = true
            },


            new ServiceFormItem
            {
                Id = "previousEmploymentTitle",
                Type = ServiceFormItemType.Text,
                Label = new MultilingualString { Ar = "المسمى الوظيفي السابق", En = "Previous employment title" },
                IsRequired = true
            },

            new ServiceFormItem
            {
                Id = "startDate",
                Type = ServiceFormItemType.Date,
                Label = new MultilingualString { Ar = "تاريخ بداية الخدمة", En = "Start of service date" },
                IsRequired = true
            },


            new ServiceFormItem
            {
                Id = "endDate",
                Type = ServiceFormItemType.Date,
                Label = new MultilingualString { Ar = "تاريخ نهاية الخدمة", En = "End of service date" },
                IsRequired = true
            },

            new ServiceFormItem
            {
                Id = "endOfServiceAmount",
                Type = ServiceFormItemType.Text,
                Label = new MultilingualString { Ar = "مبلغ نهاية الخدمة", En = "End of service amount" },
                IsRequired = true
            },


            new ServiceFormItem
            {
                Id = "previousEntityAttachment",
                Type = ServiceFormItemType.File,
                Label = new MultilingualString { Ar = "مرفق من الجهه السابقة", En = "Previous entity attachment" }
            }
        ];
    }
}
