using System.Text.Json.Nodes;
using UnifiedHub.Common.Misc;
using UnifiedHub.EmployeesManagement.Core.Rbac;
using UnifiedHub.Model.Entities.Servicing;
using UnifiedHub.Servicing.Misc;
using UnifiedHub.Servicing.Misc.Enums;
using UnifiedHub.Shared.Servicing;
using UnifiedHub.Shared.Servicing.Abstraction;

namespace UnifiedHub.EmployeesManagement.Servicing;

// TODO: implement the rest of the functionality later.
public sealed class AdditionOrWithholdingAllowanceServiceConfig(IServiceProvider serviceProvider)
    : SupervisorTwoPermissionServiceConfig(serviceProvider)
{
    public override string ServiceBuiltInId => "services:addition_or_withholding_allowance";

    public override ServiceDefinition ServiceDefinition => new(
        ServiceBuiltInId,
        new MultilingualString { Ar = "طلب إضافة وحجب العلاوة", En = "Request addition or withholding allowance" },
        ServiceSubcategoryDefinitions.Employees.Id);

    protected override string FirstPermission => P.Employees.Approve;

    protected override string SecondPermission => P.Employees.GeneralManagerApprove;

    public override async Task<ServiceFormItem[]> GetRequestFormItemsAsync(
        ServiceRequest? request,
        Guid? userId,
        JsonNode? data,
        CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;
        return
        [
            new ServiceFormItem
            {
                Id = "requestType",
                Type = ServiceFormItemType.Option,
                Label = new MultilingualString { Ar = "نوع الطلب", En = "Request type" },
                IsRequired = true,
                Options =
                [
                    new ServiceFormItem.Option("withhold",
                        new MultilingualString { Ar = "حجب علاوة", En = "Withhold allowance" }),
                    new ServiceFormItem.Option("add",
                        new MultilingualString { Ar = "إضافة علاوة", En = "Add allowance" })
                ]
            },

            new ServiceFormItem
            {
                Id = "allowanceType",
                Type = ServiceFormItemType.Text,
                Label = new MultilingualString { Ar = "نوع العلاوة", En = "Allowance type" },
                IsRequired = true
            },


            new ServiceFormItem
            {
                Id = "attachment",
                Type = ServiceFormItemType.File,
                Label = new MultilingualString { Ar = "المرفق", En = "Attachment" },
                IsRequired = true
            }
        ];
    }
}
