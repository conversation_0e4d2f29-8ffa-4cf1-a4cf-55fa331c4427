using System.Linq.Expressions;
using UnifiedHub.Dtos.SqlFunctions.Misc;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Entities.Servicing;
using UnifiedHub.Servicing.Abstraction;
using LinqKit;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.EmployeesManagement.Core.Rbac;
using UnifiedHub.Model.Entities.Foundation;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.EmployeesManagement.Servicing.TerminationServiceConfigPartials;

public sealed partial class TerminationServiceConfig
{
    public override ServiceStateTransitionDefinition[] StateTransitions
    {
        get
        {
            var employees = dataSource.Items<Employee>();

            // For guards
            Expression<Func<ServiceRequest, bool>> isResignation = r =>
                GetJsonValueAsString.Call(r.Data, "type.id") == "resignation";
            Expression<Func<ServiceRequest, bool>> isRetirement = r =>
                GetJsonValueAsString.Call(r.Data, "type.id") == "retirement";
            Expression<Func<ServiceRequest, bool>> isTermination = r =>
                GetJsonValueAsString.Call(r.Data, "type.id") == "termination";
            Expression<Func<ServiceRequest, bool>> isCivil = r =>
                employees.SingleOrDefault(x => x.UserId == r.UserId)!.JobClassification!.Type ==
                JobClassification.TypeCivil;
            Expression<Func<ServiceRequest, bool>> isSworn = r =>
                employees.SingleOrDefault(x => x.UserId == r.UserId)!.JobClassification!.Type ==
                JobClassification.TypeSworn;
            Expression<Func<ServiceRequest, bool>> isCitizen = r =>
                employees.SingleOrDefault(x => x.UserId == r.UserId)!.Nationality!.Category ==
                Nationality.CategoryCitizen ||
                employees.SingleOrDefault(x => x.UserId == r.UserId)!.Nationality!.Category ==
                Nationality.CategoryArabianGulf;
            Expression<Func<ServiceRequest, bool>> isResident = r =>
                employees.SingleOrDefault(x => x.UserId == r.UserId)!.Nationality!.Category ==
                Nationality.CategoryArab ||
                employees.SingleOrDefault(x => x.UserId == r.UserId)!.Nationality!.Category ==
                Nationality.CategoryForeign;

            // For user checks
            var users = dataSource.Items<User>();
            var managerExp = E.Employees.Manager(dateTimeProvider.UtcNow);

            Expression<Func<Guid, string, bool>> hasPermission = (u, p) => users
                .First(x => x.Id == u)
                .RoleLinks
                .Any(x => x.Role.Permissions.Any(y => y == p));
            Expression<Func<ServiceRequest, Guid, bool>> isManager = (r, u) =>
                managerExp.Invoke(employees.SingleOrDefault(e => e.UserId == r.UserId)!)!.Any(m =>
                    m.Employee.UserId == u);

            return
            [
                new(
                    States.Draft,
                    States.Supervisor,
                    Actions.Submit,
                    r => isResignation.Invoke(r) || isRetirement.Invoke(r),
                    (r, u) => r.UserId == u || r.ApplyingUserId == u
                ),

                new(
                    States.Draft,
                    States.LengthOfService,
                    Actions.Submit,
                    isTermination,
                    (r, u) => r.UserId == u || r.ApplyingUserId == u
                ),

                new(
                    States.Supervisor,
                    States.Draft,
                    Actions.Return,
                    null,
                    isManager
                ),

                new(
                    States.Supervisor,
                    States.Finalized,
                    Actions.Reject,
                    null,
                    isManager
                ),

                new(
                    States.Supervisor,
                    States.LengthOfService,
                    Actions.Approve,
                    null,
                    isManager
                ),

                new(
                    States.LengthOfService,
                    States.GeneralManager,
                    Actions.Transfer,
                    null,
                    (r, u) => hasPermission.Invoke(u, P.Employees.Approve)
                ),

                new(
                    States.GeneralManager,
                    States.LengthOfService,
                    Actions.Return,
                    null,
                    (r, u) => hasPermission.Invoke(u, P.Employees.GeneralManagerApprove)
                ),

                new(
                    States.GeneralManager,
                    States.Armory,
                    Actions.Approve,
                    isSworn,
                    (r, u) => hasPermission.Invoke(u, P.Employees.GeneralManagerApprove)
                ),

                new(
                    States.GeneralManager,
                    States.CardDelivering,
                    Actions.Approve,
                    isCivil,
                    (r, u) => hasPermission.Invoke(u, P.Employees.GeneralManagerApprove)
                ),

                new(
                    States.GeneralManager,
                    States.Finalized,
                    Actions.Reject,
                    null,
                    (r, u) => hasPermission.Invoke(u, P.Employees.GeneralManagerApprove)
                ),

                new(
                    States.Armory,
                    States.CardDelivering,
                    Actions.Transfer,
                    null,
                    (r, u) => hasPermission.Invoke(u, P.Employees.ArmoryApprove)
                ),

                new(
                    States.CardDelivering,
                    States.It,
                    Actions.Transfer,
                    null,
                    (r, u) => hasPermission.Invoke(u, P.Employees.Approve)
                ),

                new(
                    States.It,
                    States.Warehouse,
                    Actions.Transfer,
                    isSworn,
                    (r, u) => hasPermission.Invoke(u, P.Employees.ItApprove)
                ),

                new(
                    States.It,
                    States.Finance,
                    Actions.Transfer,
                    r => isCivil.Invoke(r) && isCitizen.Invoke(r),
                    (r, u) => hasPermission.Invoke(u, P.Employees.ItApprove)
                ),

                new(
                    States.It,
                    States.Residency,
                    Actions.Transfer,
                    r => isCivil.Invoke(r) && isResident.Invoke(r),
                    (r, u) => hasPermission.Invoke(u, P.Employees.ItApprove)
                ),

                new(
                    States.Warehouse,
                    States.Finance,
                    Actions.Transfer,
                    isCitizen,
                    (r, u) => hasPermission.Invoke(u, P.Employees.WarehouseApprove)
                ),

                new(
                    States.Warehouse,
                    States.Residency,
                    Actions.Transfer,
                    isResident,
                    (r, u) => hasPermission.Invoke(u, P.Employees.WarehouseApprove)
                ),

                new(
                    States.Residency,
                    States.Finance,
                    Actions.Transfer,
                    null,
                    (r, u) => hasPermission.Invoke(u, P.Employees.AffairsApprove)
                ),

                new(
                    States.Finance,
                    States.Clearance,
                    Actions.Transfer,
                    null,
                    (r, u) => hasPermission.Invoke(u, P.Employees.FinanceApprove)
                ),

                new(
                    States.Clearance,
                    States.Benefits,
                    Actions.Transfer,
                    isResignation,
                    (r, u) => hasPermission.Invoke(u, P.Employees.Approve)
                ),

                new(
                    States.Clearance,
                    States.Hr,
                    Actions.Transfer,
                    r => !isResignation.Invoke(r),
                    (r, u) => hasPermission.Invoke(u, P.Employees.Approve)
                ),

                new(
                    States.Benefits,
                    States.Hr,
                    Actions.Transfer,
                    null,
                    (r, u) => hasPermission.Invoke(u, P.Employees.Approve)
                ),

                new(
                    States.Hr,
                    States.Finalized,
                    Actions.Finalize,
                    null,
                    (r, u) => hasPermission.Invoke(u, P.Employees.Approve)
                ),
            ];
        }
    }
}
