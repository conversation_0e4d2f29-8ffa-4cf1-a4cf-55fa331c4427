using System.Text.Json.Nodes;
using UnifiedHub.Common.Extensions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeTerminations;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Entities.Servicing;

namespace UnifiedHub.EmployeesManagement.Servicing.TerminationServiceConfigPartials;

public sealed partial class TerminationServiceConfig
{
    protected override async Task OnActionInvokedAsync(
        ServiceRequest request,
        ServiceRequestTransaction transaction,
        CancellationToken cancellationToken = default)
    {
        if (transaction.ToState != States.Finalized || transaction.Action == Actions.Reject)
        {
            await base.OnActionInvokedAsync(request, transaction, cancellationToken);
            return;
        }

        var employee = dataSource.Items<Employee>()
            .SingleOrDefault(x => x.UserId == request.UserId);

        if (employee == null)
        {
            return;
        }

        var benefitsTxData = request.Transactions
            .OrderByDescending(x => x.CreatedTime)
            .FirstOrDefault(x => x.FromState == States.Benefits)?
            .Data;

        var lengthOfServiceTxData = request.Transactions
            .OrderByDescending(x => x.CreatedTime)
            .FirstOrDefault(x => x.FromState == States.LengthOfService)?
            .Data;

        var txData =
            // Let's get the periods from the benefits step because it has more info.
            benefitsTxData ??

            // Some requests may not go through the benefits step only period calculation, 
            // so we'll fall back to that.
            lengthOfServiceTxData;

        var lengthOfService = ComputeLengthOfService(txData?["servicePeriodTable"]) +
                              ComputeServiceAdditionOrDeduction(txData?["servicePeriodAdditionTable"]) -
                              ComputeServiceAdditionOrDeduction(txData?["servicePeriodDeductionTable"]);

        var benefits = ComputeBenefit(txData?["servicePeriodTable"]) +
                       ComputeBenefit(txData?["servicePeriodAdditionTable"]) -
                       ComputeBenefit(txData?["servicePeriodDeductionTable"]);

        var command = new CreateEmployeeTerminationCommand
        {
            EmployeeId = employee.Id,
            LastWorkDate = request.Data["lastWorkDate"]?.GetValueOrDefault<DateTime>(),
            TerminationReasonId = lengthOfServiceTxData?["reason"]?["id"]?.GetValueOrDefault<Guid>(),
            Details = "Via termination service request.",
            SignedRequestFile =
                new FileUploadAndReplaceDto
                {
                    Id = request.Data["signedRequestAttachment"]?["id"]?.GetValueOrDefault<Guid>()
                },
            FinanceNotes = request.Transactions
                .FirstOrDefault(x => x.FromState == States.Finance)?
                .Data["notes"]?
                .GetValueOrDefault<string>(),
            WarehouseNotes = request.Transactions
                .FirstOrDefault(x => x.FromState == States.Warehouse)?
                .Data["notes"]?
                .GetValueOrDefault<string>(),
            ArmoryNotes = request.Transactions
                .FirstOrDefault(x => x.FromState == States.Armory)?
                .Data["notes"]?
                .GetValueOrDefault<string>(),
            IsResidencyCanceled = request.Transactions
                .FirstOrDefault(x => x.FromState == States.Residency)?
                .Data["isResidencyCancelled"]?
                .GetValueOrDefault<bool>(),
            IsHealthInsuranceCanceled = request.Transactions
                .FirstOrDefault(x => x.FromState == States.Residency)?
                .Data["isHealthInsuranceCancelled"]?
                .GetValueOrDefault<bool>(),
            ResidencyAndHealthInsuranceCancellationNotes = request.Transactions
                .FirstOrDefault(x => x.FromState == States.Residency)?
                .Data["cancellationNotes"]?
                .GetValueOrDefault<string>(),
            LengthOfServiceInDays = lengthOfService.TotalDays,
            EndOfServiceBenefits = benefits,
            ServiceRequestId = request.Id
        };

        await mediator.Send(command, cancellationToken);

        await base.OnActionInvokedAsync(request, transaction, cancellationToken);
    }

    private static TimeSpan ComputeLengthOfService(JsonNode? data)
    {
        return data?
            .AsArray()
            .Select(x => new
            {
                From = x?["fromDate"]?.GetValueOrDefault<DateTime>(),
                To = x?["toDate"]?.GetValueOrDefault<DateTime>(),
            })
            .Where(x => x.From != null && x.To != null && x.From <= x.To)
            .Aggregate(TimeSpan.Zero, (s, x) => (x.To - x.From)!.Value + s) ?? TimeSpan.Zero;
    }

    private static TimeSpan ComputeServiceAdditionOrDeduction(JsonNode? data)
    {
        return data?
            .AsArray()
            .Select(x => new
            {
                Days = x?["days"]?.GetValueOrDefault<int>() ?? 0,
                Months = x?["months"]?.GetValueOrDefault<int>() ?? 0,
                Years = x?["years"]?.GetValueOrDefault<int>() ?? 0,
            })
            .Select(x =>
                TimeSpan.FromDays(Math.Ceiling((x.Years * Constants.Time.DaysPerYear) +
                                               (x.Months * Constants.Time.DaysPerMonth) +
                                               x.Days)))
            .Aggregate(TimeSpan.Zero, (s, x) => x + s) ?? TimeSpan.Zero;
    }

    private static decimal ComputeBenefit(JsonNode? data)
    {
        return data?
            .AsArray()
            .Select(x => new
            {
                Amount = x?["amount"]?.GetValueOrDefault<decimal>() ?? 0
            })
            .Sum(x => x.Amount) ?? 0;
    }
}
