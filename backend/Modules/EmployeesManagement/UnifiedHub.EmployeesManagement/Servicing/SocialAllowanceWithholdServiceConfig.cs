using System.Text.Json.Nodes;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeChildren;
using UnifiedHub.EmployeesManagement.Core.Rbac;
using UnifiedHub.EmployeesManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Entities.Servicing;
using UnifiedHub.Servicing.Misc;
using UnifiedHub.Servicing.Misc.Enums;
using UnifiedHub.Shared.Servicing;
using UnifiedHub.Shared.Servicing.Abstraction;

namespace UnifiedHub.EmployeesManagement.Servicing;

public sealed class SocialAllowanceWithholdServiceConfig(
    IServiceProvider serviceProvider,
    IIdentityService identityService,
    IEmployeesDataSource dataSource,
    ISender mediator)
    : SupervisorTwoPermissionServiceConfig(serviceProvider)
{
    public override string ServiceBuiltInId => "services:social_allowance_withholding";


    public override ServiceDefinition ServiceDefinition => new(
        ServiceBuiltInId,
        new() { Ar = "طلب حجب العلاوة الاجتماعية", En = "Request withholding social allowance" },
        ServiceSubcategoryDefinitions.Employees.Id);

    protected override string FirstPermission => P.Employees.Approve;

    protected override string SecondPermission => P.Employees.GeneralManagerApprove;

    public override async Task<ServiceFormItem[]> GetRequestFormItemsAsync(
        ServiceRequest? request,
        Guid? userId,
        JsonNode? data, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;
        userId ??= identityService.User!.Id;

        return
        [
            new()
            {
                Id = "reason",
                Type = ServiceFormItemType.Option,
                Label = new() { Ar = "السبب", En = "Reason" },
                IsRequired = true,
                Options =
                [
                    new ServiceFormItem.Option("death", new() { Ar = "وفاة", En = "Death" }),
                    new ServiceFormItem.Option("threshold",
                        new() { Ar = "تجاوز ال 24 سنة", En = "Older than 24 years old" }),
                    new ServiceFormItem.Option("marriage",
                        new() { Ar = "تزوج", En = "Married" }),
                    new ServiceFormItem.Option("employment",
                        new() { Ar = "التحق بوظيفة", En = "Employed" })
                ]
            },

            new()
            {
                Id = "child",
                Type = ServiceFormItemType.Option,
                Label = new() { Ar = "الابن/الابنة", En = "Child" },
                IsRequired = true,
                RefreshForm = true,

                Options = dataSource.Items<EmployeeChild>().Where(x => x.Employee.UserId == userId)
                    .AsNoTracking()
                    .Where(x => x.AllowanceStatus != EmployeeChild.AllowanceStatusInactive)
                    .Select(x => new ServiceFormItem.Option(x.Id.ToString(), x.Name))
                    .ToArray()
            },

            new()
            {
                Id = "allowanceWithholdForm",
                Type = ServiceFormItemType.File,
                Label = new() { Ar = "نموذج حجب صرف العلاوة الإجتماعية", En = "Allowance withhold form" },
                IsRequired = true
            }
        ];
    }

    protected override async Task OnActionInvokedAsync(
        ServiceRequest request,
        ServiceRequestTransaction transaction,
        CancellationToken cancellationToken = default)
    {
        if (transaction.ToState != States.Finalized || transaction.Action != Actions.Approve)
        {
            await base.OnActionInvokedAsync(request, transaction, cancellationToken);
            return;
        }

        var childId = request.Data["child"]!["id"]?.GetValue<Guid?>();
        if (childId == null)
        {
            return;
        }

        var command = new UpdateEmployeeChildAllowanceStatusCommand
        {
            Id = childId.Value,
            AllowanceStatus = EmployeeChild.AllowanceStatusInactive
        };
        await mediator.Send(command, cancellationToken);
        await base.OnActionInvokedAsync(request, transaction, cancellationToken);
    }
}
