using System.Text.Json.Nodes;
using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Services;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeTransfers;
using UnifiedHub.EmployeesManagement.Core.Rbac;
using UnifiedHub.EmployeesManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.Model.Entities.Servicing;
using UnifiedHub.Servicing.Misc;
using UnifiedHub.Servicing.Misc.Enums;
using UnifiedHub.Shared.Servicing;
using UnifiedHub.Shared.Servicing.Abstraction;

namespace UnifiedHub.EmployeesManagement.Servicing;

public sealed class InternalTransferServiceConfig(
    IServiceProvider serviceProvider,
    IEmployeesDataSource dataSource,
    IDateTimeProvider dateTimeProvider,
    ISender mediator)
    : SupervisorPermissionServiceConfig(serviceProvider)
{
    public override string ServiceBuiltInId => "services:internal_transfer";

    public override ServiceDefinition ServiceDefinition => new(
        ServiceBuiltInId,
        new MultilingualString { Ar = "طلب نقل داخلي", En = "Request internal transfer" },
        ServiceSubcategoryDefinitions.Employees.Id);

    protected override string FinalApprovePermission => P.Employees.Approve;

    public override async Task<ServiceFormItem[]> GetRequestFormItemsAsync(ServiceRequest? request,
        Guid? userId,
        JsonNode? data, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;
        return
        [
            new ServiceFormItem
            {
                Id = "employee",
                Type = ServiceFormItemType.Option,
                Label = new MultilingualString { Ar = "˙الموظف البديل", En = "Replacement employee" },
                IsRequired = true,
                Options = dataSource
                    .Items<Employee>()
                    .AsNoTracking()
                    .Where(x => x.State == Employee.StateActive)
                    .Select(x => new ServiceFormItem.Option(x.Id.ToString(), x.Name))
                    .ToArray()
            },

            new ServiceFormItem
            {
                Id = "department",
                Type = ServiceFormItemType.Option,
                Label = new MultilingualString { Ar = "˙الوحدة التنظيمية الجديدة", En = "New department" },
                IsRequired = true,
                Options = dataSource
                    .Items<Department>()
                    .AsNoTracking()
                    .Select(x => new ServiceFormItem.Option(x.Id.ToString(), x.Name))
                    .ToArray()
            }
        ];
    }

    protected override async Task OnActionInvokedAsync(
        ServiceRequest request,
        ServiceRequestTransaction transaction,
        CancellationToken cancellationToken = default)
    {
        if (transaction is { ToState: States.Finalized, Action: Actions.Approve })
        {
            var employeeDepartmentExp = E.Employees.PrimaryDepartment();
            var data = dataSource
                .Items<Employee>()
                .AsExpandable()
                .Where(x => x.UserId == request.UserId)
                .Select(x => new
                {
                    Item = x,
                    Department = employeeDepartmentExp.Invoke(x)
                })
                .SingleOrDefault();

            if (data == null)
            {
                return;
            }

            var departmentId = request.Data["department"]?["id"]?.GetValue<Guid>();

            if (departmentId == null)
            {
                return;
            }

            await mediator.Send(new CreateEmployeeTransferCommand
            {
                FromDepartmentId = data.Department?.Id,
                ToDepartmentId = departmentId.Value,
                EmployeeId = data.Item.Id,
                ServiceRequestId = request.Id,
                TransferFromDate = dateTimeProvider.UtcNow,
            }, cancellationToken);
        }
    }
}
