using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Extensions;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeEvents;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.EmployeesManagement.Validators.EmployeeEvents;

public sealed class CreateEmployeeEventValidator : AbstractValidator<CreateEmployeeEventCommand>
{
    public CreateEmployeeEventValidator(
        IStringLocalizer<SharedResource> l)
    {
        RuleFor(x => x.EmployeeId)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_employee"]]);

        this.Apply(l);
    }
}

public sealed class UpdateEmployeeEventValidator : AbstractValidator<UpdateEmployeeEventCommand>
{
    public UpdateEmployeeEventValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public static class CreateOrUpdateEmployeeEventValidator
{
    // nationalities
    public static void Apply<TCommand>(
        this AbstractValidator<TCommand> validator,
        IStringLocalizer<SharedResource> l)
        where TCommand : CreateOrUpdateEmployeeEventCommand
    {
        validator.RuleFor(x => x.Type)
            .NotEmpty()
            .WithMessage(l["translate_0_is_required", l["translate_type"]])
            .In([
                EmployeeEvent.TypeNationalityChange,
                EmployeeEvent.TypeJobLevelChange,
                EmployeeEvent.TypeTermination,
                EmployeeEvent.TypeOldTransferChange,
                EmployeeEvent.TypeNameChange,
                EmployeeEvent.TypeSuspension,
                EmployeeEvent.TypeActivation,
                EmployeeEvent.TypeUnknown
            ])
            .WithMessage(l["translate_0_is_invalid", l["translate_type"]]);
    }
}
