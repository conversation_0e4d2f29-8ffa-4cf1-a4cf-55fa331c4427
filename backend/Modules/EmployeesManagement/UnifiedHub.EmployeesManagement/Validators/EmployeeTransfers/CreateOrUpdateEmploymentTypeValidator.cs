using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeTransfers;

namespace UnifiedHub.EmployeesManagement.Validators.EmployeeTransfers;

public sealed class CreateEmployeeTransferValidator : AbstractValidator<CreateEmployeeTransferCommand>
{
    public CreateEmployeeTransferValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
        
        RuleFor(x => x.EmployeeId)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_employee"]]);
    }
}

public sealed class UpdateEmployeeTransferValidator : AbstractValidator<UpdateEmployeeTransferCommand>
{
    public UpdateEmployeeTransferValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public static class AbstractValidatorApply
{
    public static void Apply<TCommand>(
        this AbstractValidator<TCommand> validator,
        IStringLocalizer<SharedResource> l)
        where TCommand : CreateOrUpdateEmployeeTransferCommand
    {
        validator.RuleFor(x => x.ToDepartmentId)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_new_department"]]);

        validator.RuleFor(x => x.TransferFromDate)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_transfer_time"]]);
    }
}
