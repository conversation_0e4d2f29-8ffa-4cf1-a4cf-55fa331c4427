using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Extensions;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.EmployeesManagement.Core.Commands.JobClassifications;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.EmployeesManagement.Validators.JobClassifications;

public sealed class CreateJobClassificationValidator : AbstractValidator<CreateJobClassificationCommand>
{
    public CreateJobClassificationValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public sealed class UpdateJobClassificationValidator : AbstractValidator<UpdateJobClassificationCommand>
{
    public UpdateJobClassificationValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public static class AbstractValidatorApply
{
    public static void Apply<TCommand>(
        this AbstractValidator<TCommand> validator,
        IStringLocalizer<SharedResource> l)
        where TCommand : CreateOrUpdateJobClassificationCommand
    {
        validator.RuleFor(x => x.Name)
            .NotEmptyMultilingualString()
            .WithMessage(l["translate_0_is_required", l["translate_name"]]);

        validator.RuleFor(x => x.Type)
            .NotEmpty()
            .WithMessage(l["translate_0_is_required", l["translate_type"]])
            .In([JobClassification.TypeCivil, JobClassification.TypeSworn])
            .WithMessage(l["translate_0_is_invalid", l["translate_type"]]);
    }
}
