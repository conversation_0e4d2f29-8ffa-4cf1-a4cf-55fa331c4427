using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeMedicalExemptions;

namespace UnifiedHub.EmployeesManagement.Validators.EmployeeMedicalExemptions;

public sealed class CreateEmployeeMedicalExemptionValidator : AbstractValidator<CreateEmployeeMedicalExemptionCommand>
{
    public CreateEmployeeMedicalExemptionValidator(IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);

        RuleFor(x => x.EmployeeId)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_employee"]]);
    }
}

public sealed class UpdateEmployeeMedicalExemptionValidator : AbstractValidator<CreateEmployeeMedicalExemptionCommand>
{
    public UpdateEmployeeMedicalExemptionValidator(IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public static class CreateOrUpdateEmployeeMedicalExemptionValidator
{
    public static void Apply<TCommand>(
        this AbstractValidator<TCommand> validator,
        IStringLocalizer<SharedResource> l)
        where TCommand : CreateOrUpdateEmployeeMedicalExemptionCommand
    {
        validator.RuleFor(x => x.StartDate)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_start_date"]]);

        validator.RuleFor(x => x.EndDate)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_end_date"]]);

        validator.RuleFor(x => x.DayCount)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_day_count"]]);
    }
}
