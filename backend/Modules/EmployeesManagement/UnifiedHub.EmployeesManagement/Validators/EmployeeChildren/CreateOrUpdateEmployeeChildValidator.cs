using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Extensions;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeChildren;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.EmployeesManagement.Validators.EmployeeChildren;

public class CreateEmployeeChildValidator : AbstractValidator<CreateEmployeeChildCommand>
{
    public CreateEmployeeChildValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public class UpdateEmployeeChildValidator : AbstractValidator<UpdateEmployeeChildCommand>
{
    public UpdateEmployeeChildValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public static class CreateOrUpdateEmployeeChildValidator
{
    public static void Apply<TCommand>(
        this AbstractValidator<TCommand> validator,
        IStringLocalizer<SharedResource> l)
        where TCommand : CreateOrUpdateEmployeeChildCommand
    {
        validator.RuleFor(x => x.Name)
            .NotEmptyMultilingualString()
            .WithMessage(l["translate_0_is_required", l["translate_name"]]);

        validator.RuleFor(x => x.EmployeeId)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_employee_id"]]);

        validator.RuleFor(x => x.AllowanceStatus)
            .NotEmpty()
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_allowance_status"]])
            .In([
                EmployeeChild.AllowanceStatusActive,
                EmployeeChild.AllowanceStatusInactive,
                EmployeeChild.AllowanceStatusUnknown,
            ])
            .WithMessage(l["0_is_invalid", l["translate_allowance_status"]]);
    }
}
