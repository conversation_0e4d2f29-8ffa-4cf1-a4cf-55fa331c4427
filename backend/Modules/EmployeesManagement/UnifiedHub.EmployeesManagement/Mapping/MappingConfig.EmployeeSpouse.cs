using Mapster;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeSpouses;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.EmployeesManagement.Mapping;

public static partial class MappingConfig
{
    private static void ApplyEmployeeSpouseConfig()
    {
        var mappingConfig = TypeAdapterConfig.GlobalSettings;

        mappingConfig.ForType<CreateEmployeeSpouseCommand, EmployeeSpouse>().Ignore(x => x.MarriageCertificateFile!, x => x.MarriageCertificateFileId!);
        mappingConfig.ForType<CreateEmployeeSpouseCommand, EmployeeSpouse>().Ignore(x => x.WorkNocFile!, x => x.WorkNocFileId!);
        mappingConfig.ForType<UpdateEmployeeSpouseCommand, EmployeeSpouse>().Ignore(x => x.MarriageCertificateFile!, x => x.MarriageCertificateFileId!);
        mappingConfig.ForType<UpdateEmployeeSpouseCommand, EmployeeSpouse>().Ignore(x => x.WorkNocFile!, x => x.WorkNocFileId!);
    }
}
