using ErrorOr;
using LinqKit;
using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.DepartmentsManagement.Core.Commands.DepartmentEmployeeLinks;
using UnifiedHub.DepartmentsManagement.Core.Commands.DepartmentUserLinks;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeTransfers;
using UnifiedHub.Emailing.Commands.SendEmail;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeTransfers;
using UnifiedHub.EmployeesManagement.Persistence;
using UnifiedHub.FileManagement.Utilities;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.RolesManagement.Core.Commands.Roles;

namespace UnifiedHub.EmployeesManagement.Handlers.EmployeeTransfers;

public sealed class CreateOrUpdateEmployeeTransferHandler(
    IEmployeesDataSource dataSource,
    IMapper mapper,
    IStringLocalizer<SharedResource> l,
    ISender mediator)
    : IRequestHandler<CreateEmployeeTransferCommand, ErrorOr<EmployeeTransferGetDto>>
        , IRequestHandler<UpdateEmployeeTransferCommand, ErrorOr<EmployeeTransferGetDto>>
{
    public async Task<ErrorOr<EmployeeTransferGetDto>> Handle(
        CreateEmployeeTransferCommand command,
        CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    public async Task<ErrorOr<EmployeeTransferGetDto>> Handle(
        UpdateEmployeeTransferCommand command,
        CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    private async Task<ErrorOr<EmployeeTransferGetDto>> CreateOrUpdate(
        CreateOrUpdateEmployeeTransferCommand command,
        CancellationToken cancellationToken)
    {
        var employeeDepartmentExp = E.Employees.PrimaryDepartment();

        Employee? employee = null;
        Department? employeeDepartment = null;

        EmployeeTransfer? item = null;

        if (command is UpdateEmployeeTransferCommand updatedCommand)
        {
            var data = dataSource.Items<EmployeeTransfer>()
                .AsExpandable()
                .Where(x => x.Id == updatedCommand.Id)
                .Select(x => new
                {
                    Item = x,
                    x.Employee,
                    EmployeeDepartment = employeeDepartmentExp.Invoke(x.Employee)
                })
                .SingleOrDefault();

            if (data == null)
            {
                return Error.NotFound();
            }

            item = data.Item;
            employee = data.Employee;
            employeeDepartment = data.EmployeeDepartment;

            mapper.Map(updatedCommand, item);
        }

        else if (command is CreateEmployeeTransferCommand createCommand)
        {
            var employeeData = dataSource.Items<Employee>()
                .AsExpandable()
                .Where(x => x.Id == createCommand.EmployeeId)
                .Select(x => new
                {
                    Item = x,
                    Department = employeeDepartmentExp.Invoke(x)
                })
                .SingleOrDefault();

            employee = employeeData?.Item;
            employeeDepartment = employeeData?.Department;

            item = new EmployeeTransfer();
            mapper.Map(createCommand, item);
            await dataSource.AddItemAsync(item, cancellationToken);
        }

        if (employee == null)
        {
            return Error.NotFound(description: l["translate_employee_not_found"]);
        }

        item!.FileId = await Utils.UploadFile(
            command.File,
            item.FileId,
            mediator,
            mapper,
            cancellationToken);

        await dataSource.CommitAsync(cancellationToken);

        if (command.ShouldUpdateEmployee)
        {
            await UpdateEmployee(employee, employeeDepartment, command, cancellationToken);
        }

        return dataSource
            .Items<EmployeeTransfer>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => x.Id == item.Id)
            .Select(EmployeeTransferGetDto.Mapper())
            .Single();
    }

    private async Task UpdateEmployee(
        Employee employee,
        Department? employeeDepartment,
        CreateOrUpdateEmployeeTransferCommand command,
        CancellationToken cancellationToken)
    {
        await mediator.Send(new AddEmployeeListToDepartmentCommand
        {
            EmployeeIds = [employee.Id],
            DepartmentId = command.ToDepartmentId!.Value
        }, cancellationToken);

        if (employee.UserId != null)
        {
            await mediator.Send(new ResetUserListRoleListCommand
            {
                UserIds = [employee.UserId.Value],
            }, cancellationToken);

            await mediator.Send(new ResetUserListDepartmentListCommand
            {
                UserIds = [employee.UserId.Value]
            }, cancellationToken);
        }

        // Notify the new department
        var managerExp = E.Departments.Manager();
        var departmentData = dataSource.Items<Department>()
            .AsExpandable()
            .Where(x => x.Id == command.ToDepartmentId)
            .Select(x => new
            {
                Item = x,
                Manager = managerExp.Invoke(x)
            })
            .SingleOrDefault();
        var manager = departmentData?.Manager;
        if (departmentData != null && manager != null && (manager.UserId != null ||
                                                          !string.IsNullOrEmpty(manager.Email)))
        {
            var arSubject = command.Type != EmployeeTransfer.TypeTransfer
                ? "إخطار بعملية انتداب أو إعارة لموظف"
                : "إخطار بعملية نقل داخلي لموظف";
            var enSubject = command.Type != EmployeeTransfer.TypeTransfer
                ? "Notification on employee secondment / delegation"
                : "Notification on employee internal transfer";

            await mediator.Send(new SendEmailCommand
            {
                Subject = new()
                    { Ar = arSubject, En = enSubject },
                Body = GetEmailView(employee, employeeDepartment, departmentData.Item),
                UserIds = manager.UserId == null ? [] : [manager.UserId.Value],
                To = manager.UserId == null && !string.IsNullOrEmpty(manager.Email) ? [manager.Email] : []
            }, cancellationToken);
        }
    }


    private string GetEmailView(
        Employee transferredEmployee,
        Department? currentDepartment,
        Department newDepartment)
    {
        return $$$"""
                  {% section ContentAr %}

                  {% partial 'Spacing', Value: 50 %}

                  <!-- Title (Ar) -->
                  {% partial 'Heading1', Text: 'مرحباً!', IsRtl: true %}
                  {% partial 'Spacing', Value: 20 %}

                  <!-- Message (Ar) -->
                  {% partial 'Paragraph', Text: 'تم نقل الموظف {{{transferredEmployee.Name.Ar}}}{{{(currentDepartment == null ? "" : " من " + currentDepartment.Name.Ar)}}} إلى {{ newDepartment.Name.Ar }}.', IsRtl: true %}
                  {% partial 'Spacing', Value: 100 %}

                  {% endsection %}

                  {% section ContentEn %}

                  {% partial 'Spacing', Value: 50 %}

                  <!-- Title (En) -->
                  {% partial 'Heading1', Text: 'Hello!', IsRtl: false %}
                  {% partial 'Spacing', Value: 20 %}

                  <!-- Message (En) -->
                  {% partial 'Paragraph', Text: 'The employee {{{transferredEmployee.Name.En}}} has been transferred{{{(currentDepartment == null ? "" : " From " + currentDepartment.Name.En)}}} to {{{newDepartment.Name.En}}}.', IsRtl: false %}
                  {% partial 'Spacing', Value: 100 %}

                  {% endsection %}
                  """;
    }
}
