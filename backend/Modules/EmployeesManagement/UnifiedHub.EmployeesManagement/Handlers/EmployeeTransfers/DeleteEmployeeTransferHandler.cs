using ErrorOr;
using MediatR;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeTransfers;
using UnifiedHub.EmployeesManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.EmployeesManagement.Handlers.EmployeeTransfers;

public sealed class DeleteEmployeeTransferHandler(
    IEmployeesDataSource dataSource) : IRequestHandler<DeleteEmployeeTransferCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        DeleteEmployeeTransferCommand command,
        CancellationToken cancellationToken)
    {
        var item = dataSource
            .Items<EmployeeTransfer>()
            .SingleOrDefault(x => x.Id == command.Id);

        if (item == null)
        {
            return Error.NotFound();
        }

        await dataSource.RemoveItemAsync(item, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
