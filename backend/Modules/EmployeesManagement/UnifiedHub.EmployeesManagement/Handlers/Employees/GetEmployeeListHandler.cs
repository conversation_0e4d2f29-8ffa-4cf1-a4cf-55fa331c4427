using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Services;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.Dtos.Modules.EmployeesManagement.Employees;
using UnifiedHub.EmployeesManagement.Core.Queries.Employees;
using UnifiedHub.EmployeesManagement.Extensions;
using UnifiedHub.EmployeesManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.Persistence.Extensions;

namespace UnifiedHub.EmployeesManagement.Handlers.Employees;

public sealed class GetEmployeeListHandler(
    IEmployeesDataSource dataSource,
    IIdentityService identityService,
    ILanguageService languageService,
    IDateTimeProvider dateTimeProvider)
    : IRequestHandler<GetEmployeeListQuery, PaginatedResult<EmployeeListDto>>
{
    public async Task<PaginatedResult<EmployeeListDto>> Handle(
        GetEmployeeListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var items = dataSource
            .Items<Employee>()
            .AsNoTracking()
            .AsExpandable()
            .Scoped(identityService, allowOwner: true);

        var count = items.Count();

        items = Filter(items, dataSource, dateTimeProvider.UtcNow, query);

        var filteredCount = items.Count();

        if (query.OrderByProperties == null || query.OrderByProperties.Length == 0)
        {
            items = items
                .OrderBy(x => x.JobClassification!.Order)
                .ThenBy(x => x.JobLevel!.Order);
        }
        else
        {
            items = items.OrderByProperties(query.OrderByProperties, languageService);
        }

        if (query.PageSize != -1)
        {
            items = items
                .Skip(query.PageNumber * query.PageSize)
                .Take(query.PageSize);
        }

        return new PaginatedResult<EmployeeListDto>
        {
            Items = items.Select(EmployeeListDto.Mapper()),
            Count = count,
            FilteredCount = filteredCount,
        };
    }

    public static IQueryable<Employee> Filter(
        IQueryable<Employee> items,
        IEmployeesDataSource dataSource,
        DateTime now,
        GetEmployeeListFilter filter)
    {
        if (!string.IsNullOrEmpty(filter.Keyword))
        {
            items = int.TryParse(filter.Keyword, out var employeeNumberAsInt)
                ? items.Where(x => x.Number == employeeNumberAsInt.ToString())
                : items.SearchMultilingualString(x => x.Name, filter.Keyword);
        }

        if (!string.IsNullOrEmpty(filter.Number))
        {
            items = items.Where(x => x.Number == filter.Number);
        }

        if (filter.DepartmentIds != null && filter.DepartmentIds.Length != 0)
        {
            var primaryDepartment = E.Employees.PrimaryDepartment();

            if (filter.DisplayAllUnderHierarchy)
            {
                var hierarchyCodes = dataSource.Items<Department>()
                    .Where(x => filter.DepartmentIds.Contains(x.Id))
                    .Select(x => x.HierarchyCode)
                    .ToArray();
                items = items.Where(e =>
                    hierarchyCodes.Any(c => primaryDepartment.Invoke(e)!.HierarchyCode.StartsWith(c)));
            }
            else
            {
                items = items.Where(x => filter.DepartmentIds!.Contains(primaryDepartment.Invoke(x)!.Id));
            }
        }

        if (filter.DateOfBirthFrom != null)
        {
            items = items.Where(x => filter.DateOfBirthFrom <= x.DateOfBirth);
        }

        if (filter.DateOfBirthTo != null)
        {
            items = items
                .Where(x => x.DateOfBirth < filter.DateOfBirthTo);
        }

        if (filter.JobTitleIds != null && filter.JobTitleIds.Length != 0)
        {
            items = items.Where(x => filter.JobTitleIds!.Contains(x.JobTitleId!.Value));
        }

        if (filter.JobCategoryIds != null && filter.JobCategoryIds.Length != 0)
        {
            items = items.Where(x => filter.JobCategoryIds!.Contains(x.JobCategoryId!.Value));
        }

        if (filter.MilitaryStatusIds != null && filter.MilitaryStatusIds.Length != 0)
        {
            items = items.Where(x => filter.MilitaryStatusIds!.Contains(x.MilitaryStatusId!.Value));
        }

        if (filter.QualificationIds != null && filter.QualificationIds.Length != 0)
        {
            items = items.Where(x =>
                filter.QualificationIds!.Any(y => x.QualificationLinks.Any(z => z.QualificationId == y)));
        }

        if (filter.NationalityIds != null && filter.NationalityIds.Length != 0)
        {
            items = items.Where(x => filter.NationalityIds!.Contains(x.NationalityId!.Value));
        }

        if (filter.CityIds != null && filter.CityIds.Length != 0)
        {
            items = items.Where(x => filter.CityIds!.Contains(x.CityId!.Value));
        }

        if (filter.JobClassificationIds != null && filter.JobClassificationIds.Length != 0)
        {
            items = items.Where(x => filter.JobClassificationIds!.Contains(x.JobClassificationId!.Value));
        }

        if (filter.JobLevelIds != null && filter.JobLevelIds.Length != 0)
        {
            items = items.Where(x => filter.JobLevelIds!.Contains(x.JobLevelId!.Value));
        }

        if (filter.HireDateFrom != null)
        {
            items = items.Where(x => filter.HireDateFrom <= x.HireDate);
        }

        if (filter.HireDateTo != null)
        {
            items = items.Where(x => x.HireDate < filter.HireDateTo);
        }

        var lengthOfServiceInDaysExp = E.Employees.LengthOfServiceInDays(now);
        if (filter.LengthOfServiceFromInYears != null)
        {
            items = items.Where(x =>
                filter.LengthOfServiceFromInYears <= lengthOfServiceInDaysExp.Invoke(x) / Constants.Time.DaysPerYear);
        }

        if (filter.LengthOfServiceToInYears != null)
        {
            items = items.Where(x =>
                lengthOfServiceInDaysExp.Invoke(x) / Constants.Time.DaysPerYear < filter.LengthOfServiceToInYears);
        }

        if (filter.DateOfTerminationFrom != null)
        {
            items = items.Where(x => x.Terminations.Any(y => filter.DateOfTerminationFrom <= y.LastWorkDate));
        }

        if (filter.DateOfTerminationTo != null)
        {
            items = items.Where(x => x.Terminations.Any(y => y.LastWorkDate < filter.DateOfTerminationTo));
        }

        if (!string.IsNullOrEmpty(filter.PhoneNumber))
        {
            items = items.Where(x => x.PhoneNumber!.Contains(filter.PhoneNumber));
        }

        if (filter.Genders != null && filter.Genders.Length != 0)
        {
            items = items.Where(x => filter.Genders != null && filter.Genders.Contains(x.Gender));
        }

        var nextPromotionExp = E.Employees.NextPromotionDueDate();
        if (filter.NextPromotionFrom != null)
        {
            items = items.Where(x => filter.NextPromotionFrom <= nextPromotionExp.Invoke(x));
        }

        if (filter.NextPromotionTo != null)
        {
            items = items.Where(x => nextPromotionExp.Invoke(x) <= filter.NextPromotionTo);
        }

        if (filter.MaritalStatusIds is { Length: > 0 })
        {
            items = items.Where(x =>
                x.MaritalStatusId != null && filter.MaritalStatusIds.Contains(x.MaritalStatusId.Value));
        }


        if (filter.States.Length != 0)
        {
            items = items.Where(x => filter.States.Contains(x.State));
        }

        if (filter.IsMotherCitizen)
        {
            items = items.Where(x => x.IsMotherCitizen == true);
        }

        if (filter.DoesNotHaveSignature)
        {
            items = items.Where(x => x.Documents.All(y => y.Type != EmployeeDocument.TypeSignature));
        }

        if (filter.DoesNotHavePassport)
        {
            items = items.Where(x => x.Documents.All(y => y.Type != EmployeeDocument.TypePassport));
        }

        if (filter.DoesNotHaveNationalId)
        {
            items = items.Where(x => x.Documents.All(y => y.Type != EmployeeDocument.TypeNationalId));
        }

        return items;
    }
}
