using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Core.Services;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Modules.EmployeesManagement.Employees;
using UnifiedHub.EmployeesManagement.Core.Queries.Employees;
using UnifiedHub.EmployeesManagement.Core.Rbac;
using UnifiedHub.EmployeesManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.EmployeesManagement.Handlers.Employees;

public sealed class GetEmployeeByCurrentUserHandler(
    IIdentityService identityService,
    IEmployeesDataSource dataSource,
    IDateTimeProvider dateTimeProvider) : IRequestHandler<GetEmployeeByCurrentUserQuery, EmployeeGetDto?>
{
    public async Task<EmployeeGetDto?> Handle(
        GetEmployeeByCurrentUserQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var userId = identityService.User?.Id;

        if (userId == null)
        {
            return null;
        }

        var hasReadSensitivePermission = identityService.Permissions.Contains(P.Employees.ReadSensitive);
        return dataSource
            .Items<Employee>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => x.UserId == userId)
            .Select(EmployeeGetDto.Mapper(
                hasReadSensitivePermission,
                dateTimeProvider.UtcNow))
            .SingleOrDefault();
    }
}
