using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeJobLevelChanges;
using UnifiedHub.EmployeesManagement.Core.Expressions;
using UnifiedHub.EmployeesManagement.Core.Queries.EmployeeJobLevelChanges;
using UnifiedHub.EmployeesManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Persistence.Extensions;

namespace UnifiedHub.EmployeesManagement.Handlers.EmployeeJobLevelChanges;

public sealed class GetEmployeeJobLevelChangeListHandler(
    IEmployeesDataSource dataSource,
    IIdentityService identityService)
    : IRequestHandler<GetEmployeeJobLevelChangeListQuery, PaginatedResult<EmployeeJobLevelChangeListDto>>
{
    public async Task<PaginatedResult<EmployeeJobLevelChangeListDto>> Handle(
        GetEmployeeJobLevelChangeListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var scopeExpression = ScopeExpression.Get(identityService, allowOwner: true);

        var items = dataSource
            .Items<EmployeeJobLevelChange>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => scopeExpression.Invoke(x.Employee));

        if (
            query.FilterCountExclusions.Contains(GetEmployeeJobLevelChangeListQuery.FilterCountExclusion.EmployeeIds)
            && query.EmployeeIds != null && query.EmployeeIds.Length != 0)
        {
            items = items.Where(x => query.EmployeeIds.Contains(x.EmployeeId));
        }

        var count = items.Count();

        if (!string.IsNullOrEmpty(query.Keyword))
        {
            items = items.SearchMultilingualString(x => x.Employee.Name, query.Keyword);
        }

        if (!string.IsNullOrEmpty(query.Number))
        {
            items = items.Where(x => x.Employee.Number == query.Number);
        }

        if (query.Types is { Length: > 0 })
        {
            items = items.Where(x => query.Types.Contains(x.Type));
        }

        if (query.From != null)
        {
            items = items.Where(x => query.From <= x.ChangeDate);
        }

        if (query.To != null)
        {
            items = items.Where(x => x.ChangeDate < query.To);
        }

        if (query.EmployeeIds is { Length: > 0 })
        {
            items = items.Where(x => query.EmployeeIds.Contains(x.EmployeeId));
        }

        if (query.PromotionTypeIds is { Length: > 0 })
        {
            items = items.Where(x =>
                x.PromotionTypeId != null && query.PromotionTypeIds.Contains(x.PromotionTypeId.Value));
        }

        if (query.CurrentJobLevelIds is { Length: > 0 })
        {
            items = items.Where(x => query.CurrentJobLevelIds.Contains(x.CurrentJobLevelId));
        }

        if (query.NewJobLevelIds is { Length: > 0 })
        {
            items = items.Where(x => query.NewJobLevelIds.Contains(x.NewJobLevelId));
        }

        if (!string.IsNullOrEmpty(query.ReferenceNumber))
        {
            items = items.Where(x => x.ReferenceNumber == query.ReferenceNumber);
        }

        if (query.DepartmentIds is { Length: > 0 })
        {
            var primaryDepartmentExp = E.Employees.PrimaryDepartment();
            items = items.Where(x => query.DepartmentIds.Contains(primaryDepartmentExp.Invoke(x.Employee)!.Id));
        }


        items = items
            .OrderByDescending(x => x.CreatedTime)
            .ThenBy(x => x.CurrentJobLevel.Order);

        var filteredCount = items.Count();

        if (query.PageSize != -1)
        {
            items = items
                .Skip(query.PageNumber * query.PageSize)
                .Take(query.PageSize);
        }

        return new PaginatedResult<EmployeeJobLevelChangeListDto>
        {
            Items = items.Select(EmployeeJobLevelChangeListDto.Mapper()),
            Count = count,
            FilteredCount = filteredCount,
        };
    }
}
