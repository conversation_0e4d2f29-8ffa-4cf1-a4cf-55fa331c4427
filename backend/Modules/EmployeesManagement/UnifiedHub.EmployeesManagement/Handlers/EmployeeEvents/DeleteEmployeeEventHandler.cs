using ErrorOr;
using MediatR;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeEvents;
using UnifiedHub.EmployeesManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.EmployeesManagement.Handlers.EmployeeEvents;

public sealed class DeleteEmployeeEventHandler(
    IEmployeesDataSource dataSource) : IRequestHandler<DeleteEmployeeEventCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        DeleteEmployeeEventCommand command,
        CancellationToken cancellationToken)
    {
        var item = dataSource
            .Items<EmployeeEvent>()
            .SingleOrDefault(x => x.Id == command.Id);

        if (item == null)
        {
            return Error.NotFound();
        }

        await dataSource.RemoveItemAsync(item, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
