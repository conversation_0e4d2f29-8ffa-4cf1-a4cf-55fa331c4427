using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeEvents;
using UnifiedHub.EmployeesManagement.Core.Expressions;
using UnifiedHub.EmployeesManagement.Core.Queries.EmployeeEvents;
using UnifiedHub.EmployeesManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Persistence.Extensions;

namespace UnifiedHub.EmployeesManagement.Handlers.EmployeeEvents;

public sealed class GetEmployeeEventListHandler(
    IEmployeesDataSource dataSource,
    IIdentityService identityService)
    : IRequestHandler<GetEmployeeEventListQuery, PaginatedResult<EmployeeEventListDto>>
{
    public async Task<PaginatedResult<EmployeeEventListDto>> Handle(
        GetEmployeeEventListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var scopeExpression = ScopeExpression.Get(identityService, allowOwner: true);

        var items = dataSource
            .Items<EmployeeEvent>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => scopeExpression.Invoke(x.Employee));

        if (
            query.FilterCountExclusions.Contains(GetEmployeeEventListQuery.FilterCountExclusion.EmployeeIds)
            && query.EmployeeIds != null && query.EmployeeIds.Length != 0)
        {
            items = items.Where(x => query.EmployeeIds.Contains(x.EmployeeId));
        }

        var count = items.Count();

        if (!string.IsNullOrEmpty(query.Keyword))
        {
            items = items.SearchMultilingualString(x => x.Employee.Name, query.Keyword);
        }

        if (query.From != null)
        {
            items = items.Where(x => query.From <= x.Time);
        }

        if (query.To != null)
        {
            items = items.Where(x => x.Time < query.To);
        }

        if (!query.FilterCountExclusions.Contains(GetEmployeeEventListQuery.FilterCountExclusion.EmployeeIds) &&
            query.EmployeeIds is { Length: > 0 })
        {
            items = items.Where(x => query.EmployeeIds.Contains(x.EmployeeId));
        }

        if (query.Types is { Length: > 0 })
        {
            items = items.Where(x => query.Types.Contains(x.Type));
        }

        items = items.OrderByDescending(x => x.Time);

        var filteredCount = items.Count();

        if (query.PageSize != -1)
        {
            items = items
                .Skip(query.PageNumber * query.PageSize)
                .Take(query.PageSize);
        }

        return new PaginatedResult<EmployeeEventListDto>
        {
            Items = items.Select(EmployeeEventListDto.Mapper()),
            Count = count,
            FilteredCount = filteredCount,
        };
    }
}
