using ErrorOr;
using LinqKit;
using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Core.Services;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeEvents;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeEvents;
using UnifiedHub.EmployeesManagement.Persistence;
using UnifiedHub.FileManagement.Utilities;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.EmployeesManagement.Handlers.EmployeeEvents;

public sealed class CreateOrUpdateEmployeeEventHandler(
    IMapper mapper,
    IEmployeesDataSource dataSource,
    ISender mediator,
    IDateTimeProvider dateTimeProvider)
    : IRequestHandler<CreateEmployeeEventCommand, ErrorOr<EmployeeEventGetDto>>,
        IRequestHandler<UpdateEmployeeEventCommand, ErrorOr<EmployeeEventGetDto>>
{
    public async Task<ErrorOr<EmployeeEventGetDto>> Handle(
        CreateEmployeeEventCommand command,
        CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    public async Task<ErrorOr<EmployeeEventGetDto>> Handle(
        UpdateEmployeeEventCommand command,
        CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    private async Task<ErrorOr<EmployeeEventGetDto>> CreateOrUpdate(
        CreateOrUpdateEmployeeEventCommand command,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        EmployeeEvent? item = null!;

        if (command is UpdateEmployeeEventCommand updateCommand)
        {
            item = dataSource
                .Items<EmployeeEvent>()
                .AsExpandable()
                .SingleOrDefault(x => x.Id == updateCommand.Id);

            if (item == null)
            {
                return Error.NotFound();
            }

            mapper.Map(updateCommand, item);
        }

        else if (command is CreateEmployeeEventCommand createCommand)
        {
            item = new EmployeeEvent
            {
                Time = command.Time ?? dateTimeProvider.UtcNow
            };
            mapper.Map(createCommand, item);
            await dataSource.AddItemAsync(item, cancellationToken);
        }

        item.FileId = await Utils.UploadFile(command.File, item.FileId, mediator, mapper, cancellationToken);

        await dataSource.CommitAsync(cancellationToken);

        return dataSource
            .Items<EmployeeEvent>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => x.Id == item.Id)
            .Select(EmployeeEventGetDto.Mapper())
            .Single();
    }
}
