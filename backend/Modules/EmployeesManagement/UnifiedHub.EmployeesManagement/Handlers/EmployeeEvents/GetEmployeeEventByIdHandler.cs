using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeEvents;
using UnifiedHub.EmployeesManagement.Core.Expressions;
using UnifiedHub.EmployeesManagement.Core.Queries.EmployeeEvents;
using UnifiedHub.EmployeesManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;
using LinqKit;

namespace UnifiedHub.EmployeesManagement.Handlers.EmployeeEvents;

public sealed class GetEmployeeEventByIdHandler(
    IEmployeesDataSource dataSource,
    IIdentityService identityService) : IRequestHandler<GetEmployeeEventByIdQuery, ErrorOr<EmployeeEventGetDto>>
{
    public async Task<ErrorOr<EmployeeEventGetDto>> Handle(
        GetEmployeeEventByIdQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var scopeExpression = ScopeExpression.Get(identityService, allowOwner: true);

        var item = dataSource
            .Items<EmployeeEvent>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => scopeExpression.Invoke(x.Employee))
            .Select(EmployeeEventGetDto.Mapper())
            .SingleOrDefault(x => x.Id == query.Id);

        return item == null ? Error.NotFound() : (ErrorOr<EmployeeEventGetDto>) item;
    }
}
