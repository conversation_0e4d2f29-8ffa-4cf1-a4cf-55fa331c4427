using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Services;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeBankAccounts;
using UnifiedHub.EmployeesManagement.Core.Expressions;
using UnifiedHub.EmployeesManagement.Core.Queries.EmployeeBankAccounts;
using UnifiedHub.EmployeesManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Persistence.Extensions;

namespace UnifiedHub.EmployeesManagement.Handlers.EmployeeBankAccounts;

public sealed class GetEmployeeBankAccountListHandler(
    IEmployeesDataSource dataSource,
    ILanguageService languageService,
    IIdentityService identityService)
    : IRequestHandler<GetEmployeeBankAccountListQuery, PaginatedResult<EmployeeBankAccountListDto>>
{
    public async Task<PaginatedResult<EmployeeBankAccountListDto>> Handle(
        GetEmployeeBankAccountListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var scopeExp = ScopeExpression.Get(identityService, allowOwner: true);

        var items = dataSource
            .Items<EmployeeBankAccount>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => scopeExp.Invoke(x.Employee));

        if (
            query.FilterCountExclusions.Contains(GetEmployeeBankAccountListQuery.FilterCountExclusion.EmployeeIds)
            && query.EmployeeIds != null && query.EmployeeIds.Length != 0)
        {
            items = items.Where(x => query.EmployeeIds.Contains(x.EmployeeId));
        }

        if (
            query.FilterCountExclusions.Contains(GetEmployeeBankAccountListQuery.FilterCountExclusion.BankIds)
            && query.BankIds != null && query.BankIds.Length != 0)
        {
            items = items.Where(x => query.BankIds.Contains(x.BankId));
        }

        var count = items.Count();

        var filteredCount = items.Count();

        if (!string.IsNullOrEmpty(query.Keyword))
        {
            items = items.SearchMultilingualString(x => x.Bank.Name, query.Keyword);
        }

        if (!query.FilterCountExclusions.Contains(GetEmployeeBankAccountListQuery.FilterCountExclusion.EmployeeIds) &&
            query.EmployeeIds is { Length: > 0 })
        {
            items = items.Where(x => query.EmployeeIds.Contains(x.EmployeeId));
        }

        if (!query.FilterCountExclusions.Contains(GetEmployeeBankAccountListQuery.FilterCountExclusion.BankIds) &&
            query.BankIds is { Length: > 0 })
        {
            items = items.Where(x => query.BankIds.Contains(x.BankId));
        }

        items = items.OrderBy(x => x.Employee.JobClassification)
            .ThenBy(x => x.Employee.JobLevel)
            .MultilingualThenBy(x => x.Bank.Name, languageService);

        if (query.PageSize != -1)
        {
            items = items
                .Skip(query.PageNumber * query.PageSize)
                .Take(query.PageSize);
        }

        return new PaginatedResult<EmployeeBankAccountListDto>
        {
            Items = items.Select(EmployeeBankAccountListDto.Mapper()),
            Count = count,
            FilteredCount = filteredCount,
        };
    }
}
