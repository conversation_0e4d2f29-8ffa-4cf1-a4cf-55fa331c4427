<Project Sdk="Microsoft.NET.Sdk">

    <ItemGroup>
        <PackageReference Include="ClosedXML"/>
    </ItemGroup>


    <ItemGroup>
        <ProjectReference Include="..\..\..\Features\UnifiedHub.Emailing\UnifiedHub.Emailing.csproj"/>
        <ProjectReference Include="..\..\..\Features\UnifiedHub.Servicing\UnifiedHub.Servicing.csproj"/>
        <ProjectReference Include="..\..\..\UnifiedHub.Module\UnifiedHub.Module.csproj"/>
        <ProjectReference Include="..\..\..\UnifiedHub.Shared\UnifiedHub.Shared.csproj"/>
        <ProjectReference Include="..\..\DepartmentsManagement\UnifiedHub.DepartmentsManagement.Core\UnifiedHub.DepartmentsManagement.Core.csproj"/>
        <ProjectReference Include="..\..\RolesManagement\UnifiedHub.RolesManagement.Core\UnifiedHub.RolesManagement.Core.csproj" />
        <ProjectReference Include="..\..\UsersManagement\UnifiedHub.UsersManagement.Core\UnifiedHub.UsersManagement.Core.csproj"/>
        <ProjectReference Include="..\UnifiedHub.EmployeesManagement.Core\UnifiedHub.EmployeesManagement.Core.csproj"/>
    </ItemGroup>

</Project>
