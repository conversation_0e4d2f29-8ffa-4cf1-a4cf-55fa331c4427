using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Web;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmploymentTypes;
using UnifiedHub.EmployeesManagement.Core.Commands.EmploymentTypes;
using UnifiedHub.EmployeesManagement.Core.Queries.EmploymentTypes;
using UnifiedHub.EmployeesManagement.Core.Rbac;
using UnifiedHub.Rbac.Attributes;

namespace UnifiedHub.EmployeesManagement.Controllers;

[Authorize]
[ApiController]
[Route("/employees/employment-types")]
public sealed class EmploymentTypesController(
    ISender mediator) : BaseApiController
{
    /// <summary>
    /// Returns a list of employment types.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<EmploymentTypeListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List([FromQuery] GetEmploymentTypeListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Returns an employmentType given an id.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:guid}")]
    [Rbac(P.Employees.Read)]
    [Produces("application/json")]
    [ProducesResponseType<EmploymentTypeGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Get(Guid id)
    {
        var query = new GetEmploymentTypeByIdQuery { Id = id };
        var result = await mediator.Send(query);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Creates an employmentType.
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPost]
    [Rbac(P.Employees.Write)]
    [Produces("application/json")]
    [ProducesResponseType<EmploymentTypeGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Create(CreateEmploymentTypeCommand command)
    {
        var result = await mediator.Send(command);
        var controllerRoute = RouteData.Values["controller"]!.ToString()!.ToLower();
        return result.Match(item => Created($"/{controllerRoute}/{item.Id}", item), Problem);
    }

    /// <summary>
    /// Updates an employmentType.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPut("{id:guid}")]
    [Rbac(P.Employees.Write)]
    [ProducesResponseType<EmploymentTypeGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Update(Guid id, UpdateEmploymentTypeCommand command)
    {
        command.Id = id;
        var result = await mediator.Send(command);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Deletes an employmentType.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id:guid}")]
    [Rbac(P.Employees.Delete)]
    [Produces("application/octet-stream")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Delete(Guid id)
    {
        var command = new DeleteEmploymentTypeCommand { Id = id };
        var result = await mediator.Send(command);
        return result.Match(_ => NoContent(), Problem);
    }
}
