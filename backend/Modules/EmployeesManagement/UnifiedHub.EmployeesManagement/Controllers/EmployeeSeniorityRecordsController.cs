using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Web;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeSeniorityRecords;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeSeniorityRecords;
using UnifiedHub.EmployeesManagement.Core.Queries.EmployeeSeniorityRecords;
using UnifiedHub.EmployeesManagement.Core.Rbac;
using UnifiedHub.Rbac.Attributes;

namespace UnifiedHub.EmployeesManagement.Controllers;

[Authorize]
[ApiController]
[Route("/employees/seniority-records")]
public sealed class EmployeeSeniorityRecordsController(
    ISender mediator) : BaseApiController
{
    /// <summary>
    /// Returns a list of employee seniority records.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet]
    [Rbac(P.Employees.ReadSensitive)]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<EmployeeSeniorityRecordListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List([FromQuery] GetEmployeeSeniorityRecordListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Returns an employee seniority records given an id.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:guid}")]
    [Rbac(P.Employees.ReadSensitive)]
    [Produces("application/json")]
    [ProducesResponseType<EmployeeSeniorityRecordGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Get(Guid id)
    {
        var query = new GetEmployeeSeniorityRecordByIdQuery { Id = id };
        var result = await mediator.Send(query);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Creates an employee seniority records.
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPost]
    [Rbac(P.Employees.Write)]
    [Produces("application/json")]
    [ProducesResponseType<EmployeeSeniorityRecordGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Create([FromBody] CreateEmployeeSeniorityRecordCommand command)
    {
        var result = await mediator.Send(command);
        var controllerRoute = RouteData.Values["controller"]!.ToString()!.ToLower();
        return result.Match(item => Created($"/{controllerRoute}/{item.Id}", item), Problem);
    }

    /// <summary>
    /// Updates an employee seniority records.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPut("{id:guid}")]
    [Rbac(P.Employees.Write)]
    [ProducesResponseType<EmployeeSeniorityRecordGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateEmployeeSeniorityRecordCommand command)
    {
        command.Id = id;
        var result = await mediator.Send(command);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Deletes an employee seniority records.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id:guid}")]
    [Rbac(P.Employees.Write)]
    [Produces("application/octet-stream")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Delete(Guid id)
    {
        var command = new DeleteEmployeeSeniorityRecordCommand { Id = id };
        var result = await mediator.Send(command);
        return result.Match(_ => NoContent(), Problem);
    }


    /// <summary>
    /// Download  file
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:guid}/download")]
    [Rbac(P.Employees.ReadSensitive)]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DownloadFile(Guid id)
    {
        var query = new GetEmployeeSeniorityRecordFileDataQuery
        {
            Id = id,
        };
        var result = await mediator.Send(query);
        return result.Match(x => File(x.Stream, x.ContentType), Problem);
    }
}
