using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Web;
using UnifiedHub.Dtos.Modules.EmployeesManagement.MilitaryStatuses;
using UnifiedHub.EmployeesManagement.Core.Commands.MilitaryStatuses;
using UnifiedHub.EmployeesManagement.Core.Queries.MilitaryStatuses;
using UnifiedHub.EmployeesManagement.Core.Rbac;
using UnifiedHub.Rbac.Attributes;

namespace UnifiedHub.EmployeesManagement.Controllers;

[Authorize]
[ApiController]
[Route("/employees/military-statuses")]
public sealed class MilitaryStatusesController(
    ISender mediator) : BaseApiController
{
    /// <summary>
    /// Returns a list of educational qualifications.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<MilitaryStatusListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List([FromQuery] GetMilitaryStatusListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }
    
    /// <summary>
    /// Returns an militaryStatus given an id.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:guid}")]
    [Rbac(P.Employees.Read)]
    [Produces("application/json")]
    [ProducesResponseType<MilitaryStatusGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Get(Guid id)
    {
        var query = new GetMilitaryStatusByIdQuery { Id = id };
        var result = await mediator.Send(query);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Creates an militaryStatus.
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPost]
    [Rbac(P.Employees.Write)]
    [Produces("application/json")]
    [ProducesResponseType<MilitaryStatusGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Create(CreateMilitaryStatusCommand command)
    {
        var result = await mediator.Send(command);
        var controllerRoute = RouteData.Values["controller"]!.ToString()!.ToLower();
        return result.Match(item => Created($"/{controllerRoute}/{item.Id}", item), Problem);
    }

    /// <summary>
    /// Updates an militaryStatus.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPut("{id:guid}")]
    [Rbac(P.Employees.Write)]
    [ProducesResponseType<MilitaryStatusGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Update(Guid id, UpdateMilitaryStatusCommand command)
    {
        command.Id = id;
        var result = await mediator.Send(command);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Deletes an militaryStatus.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id:guid}")]
    [Rbac(P.Employees.Delete)]
    [Produces("application/octet-stream")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Delete(Guid id)
    {
        var command = new DeleteMilitaryStatusCommand { Id = id };
        var result = await mediator.Send(command);
        return result.Match(_ => NoContent(), Problem);
    }
}
