using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Web;
using UnifiedHub.Dtos.Misc;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeEvents;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeJobLevelChanges;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeTransfers;
using UnifiedHub.EmployeesManagement.Core.Commands.EmployeeEvents;
using UnifiedHub.EmployeesManagement.Core.Queries.EmployeeEvents;
using UnifiedHub.EmployeesManagement.Core.Queries.EmployeeJobLevelChanges;
using UnifiedHub.EmployeesManagement.Core.Queries.EmployeeTransfers;
using UnifiedHub.EmployeesManagement.Core.Rbac;
using UnifiedHub.Rbac.Attributes;

namespace UnifiedHub.EmployeesManagement.Controllers;

[Authorize]
[ApiController]
[Route("/employees/events")]
public sealed class EmployeeEventsController(
    ISender mediator) : BaseApiController
{
    /// <summary>
    /// Returns a list of employee events.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<EmployeeEventListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List([FromQuery] GetEmployeeEventListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Returns an employee event given an id.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:guid}")]
    [Produces("application/json")]
    [ProducesResponseType<EmployeeEventGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Get(Guid id)
    {
        var query = new GetEmployeeEventByIdQuery { Id = id };
        var result = await mediator.Send(query);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Creates an employee event.
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPost]
    [Rbac(P.Employees.Write)]
    [Produces("application/json")]
    [ProducesResponseType<EmployeeEventGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Create(CreateEmployeeEventCommand command)
    {
        var result = await mediator.Send(command);

        var controllerRoute = RouteData.Values["controller"]!.ToString()!.ToLower();
        return result.Match(item => Created($"/{controllerRoute}/{item.Id}", item), Problem);
    }

    /// <summary>
    /// Updates an employee event.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPut("{id:guid}")]
    [Rbac(P.Employees.Write)]
    [ProducesResponseType<EmployeeEventGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Update(Guid id, UpdateEmployeeEventCommand command)
    {
        command.Id = id;
        var result = await mediator.Send(command);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Deletes an employee event.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id:guid}")]
    [Rbac(P.Employees.Write)]
    [Produces("application/octet-stream")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Delete(Guid id)
    {
        var command = new DeleteEmployeeEventCommand { Id = id };
        var result = await mediator.Send(command);
        return result.Match(_ => NoContent(), Problem);
    }

    /// <summary>
    /// Returns a list of employee promotion events.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet("promotions")]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<EmployeeJobLevelChangeListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> ListPromotionEvents([FromQuery] GetEmployeeJobLevelChangeListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Returns a list of employee transfer events.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet("transfers")]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<EmployeeTransferListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List([FromQuery] GetEmployeeTransferListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Returns a list of employee violation events.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet("violations")]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<EmployeeViolationEventListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List([FromQuery] GetEmployeeViolationEventListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Returns a list of event types.
    /// </summary>
    /// <returns></returns>
    [HttpGet("types")]
    [Produces("application/json")]
    [ProducesResponseType<IEnumerable<ItemDto<string>>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> EventTypes()
    {
        var query = new GetEmployeeEventTypeListQuery();
        var result = await mediator.Send(query);
        return Ok(result);
    }
}
