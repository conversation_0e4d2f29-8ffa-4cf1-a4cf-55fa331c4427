using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Web;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeTransferReasons;
using UnifiedHub.EmployeesManagement.Core.Queries.EmployeeTransferReasons;

namespace UnifiedHub.EmployeesManagement.Controllers;

[Authorize]
[ApiController]
[Route("/employees/transfers/reasons")]
public class EmployeeTransferReasonsController (ISender mediator) : BaseApiController
{
    /// <summary>
    /// Returns types of transfer reasons.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<EmployeeTransferReasonListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List([FromQuery] GetEmployeeTransferReasonListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }
}
