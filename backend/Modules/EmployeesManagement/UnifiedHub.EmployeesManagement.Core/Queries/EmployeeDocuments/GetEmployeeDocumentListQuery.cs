using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeDocuments;

namespace UnifiedHub.EmployeesManagement.Core.Queries.EmployeeDocuments;

public sealed class GetEmployeeDocumentListQuery : IRequest<PaginatedResult<EmployeeDocumentListDto>>
{
    public string? Keyword { get; set; }

    public Guid[]? EmployeeIds { get; set; }

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;

    public FilterCountExclusion[] FilterCountExclusions { get; set; } = [];

    public enum FilterCountExclusion
    {
        EmployeeIds
    }
};
