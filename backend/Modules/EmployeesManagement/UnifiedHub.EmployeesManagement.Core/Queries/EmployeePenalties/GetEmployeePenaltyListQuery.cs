using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeePenalties;

namespace UnifiedHub.EmployeesManagement.Core.Queries.EmployeePenalties;

public sealed class GetEmployeePenaltyListQuery : IRequest<PaginatedResult<EmployeePenaltyListDto>>
{
    public string? Keyword { get; set; }

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;
}

