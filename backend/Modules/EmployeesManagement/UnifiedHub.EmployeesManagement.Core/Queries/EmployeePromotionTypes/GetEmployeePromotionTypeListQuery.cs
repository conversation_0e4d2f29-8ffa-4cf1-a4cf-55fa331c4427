using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeePromotionTypes;

namespace UnifiedHub.EmployeesManagement.Core.Queries.EmployeePromotionTypes;

public sealed class GetEmployeePromotionTypeListQuery : IRequest<PaginatedResult<EmployeePromotionTypeListDto>>
{
    public string? Keyword { get; set; }

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;
}
