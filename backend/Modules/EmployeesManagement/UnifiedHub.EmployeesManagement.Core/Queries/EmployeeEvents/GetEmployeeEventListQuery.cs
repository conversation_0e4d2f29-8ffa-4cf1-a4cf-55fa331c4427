using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeEvents;

namespace UnifiedHub.EmployeesManagement.Core.Queries.EmployeeEvents;

public sealed class GetEmployeeEventListQuery : IRequest<PaginatedResult<EmployeeEventListDto>>
{
    public string? Keyword { get; set; }

    public DateTime? From { get; set; }

    public DateTime? To { get; set; }

    public Guid[]? EmployeeIds { get; set; }

    public string[]? Types { get; set; }

    public FilterCountExclusion[] FilterCountExclusions = [];

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;


    public enum FilterCountExclusion
    {
        EmployeeIds
    }
}
