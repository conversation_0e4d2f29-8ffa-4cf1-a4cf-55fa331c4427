using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.EmployeesManagement.JobClassifications;

namespace UnifiedHub.EmployeesManagement.Core.Queries.JobClassifications;

public sealed class GetJobClassificationListQuery : IRequest<PaginatedResult<JobClassificationListDto>>
{
    public string? Keyword { get; set; }

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;
}
