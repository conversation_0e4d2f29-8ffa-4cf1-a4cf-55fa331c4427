using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeBankAccounts;

namespace UnifiedHub.EmployeesManagement.Core.Queries.EmployeeBankAccounts;

public sealed class GetEmployeeBankAccountListQuery : IRequest<PaginatedResult<EmployeeBankAccountListDto>>
{
    public string? Keyword { get; set; }

    public Guid[]? EmployeeIds { get; set; }

    public Guid[]? BankIds { get; set; }

    public FilterCountExclusion[] FilterCountExclusions { get; set; } = [];

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;

    public enum FilterCountExclusion
    {
        EmployeeIds,
        BankIds
    }
}
