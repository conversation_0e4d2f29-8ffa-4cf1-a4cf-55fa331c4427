using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeJobLevelChanges;

namespace UnifiedHub.EmployeesManagement.Core.Queries.EmployeeJobLevelChanges;

public sealed class GetEmployeeJobLevelChangeListQuery : IRequest<PaginatedResult<EmployeeJobLevelChangeListDto>>
{
    public string? Keyword { get; set; }

    public string? Number { get; set; }

    public string[]? Types { get; set; }

    public DateTime? From { get; set; }

    public DateTime? To { get; set; }

    public Guid[]? EmployeeIds { get; set; }

    public Guid[]? PromotionTypeIds { get; set; }

    public Guid[]? CurrentJobLevelIds { get; set; }

    public Guid[]? NewJobLevelIds { get; set; }

    public string? ReferenceNumber { get; set; }

    public Guid[]? DepartmentIds { get; set; }

    public FilterCountExclusion[] FilterCountExclusions { get; set; } = [];

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;

    public enum FilterCountExclusion
    {
        EmployeeIds
    }
}
