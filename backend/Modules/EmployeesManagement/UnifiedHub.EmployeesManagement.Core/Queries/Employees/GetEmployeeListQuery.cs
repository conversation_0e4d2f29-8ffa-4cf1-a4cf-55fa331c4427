using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.EmployeesManagement.Employees;

namespace UnifiedHub.EmployeesManagement.Core.Queries.Employees;

public abstract class GetEmployeeListFilter
{
    public string? Keyword { get; set; }

    public string? Number { get; set; }

    public Guid[]? DepartmentIds { get; set; }

    public DateTime? DateOfBirthFrom { get; set; }

    public DateTime? DateOfBirthTo { get; set; }

    public int? AgeFrom { get; set; }

    public int? AgeTo { get; set; }

    public Guid[]? JobTitleIds { get; set; }

    public Guid[]? JobCategoryIds { get; set; }

    public Guid[]? MilitaryStatusIds { get; set; }

    public Guid[]? QualificationIds { get; set; }

    public Guid[]? NationalityIds { get; set; }

    public Guid[]? CityIds { get; set; }

    public Guid[]? JobClassificationIds { get; set; }

    public Guid[]? JobLevelIds { get; set; }

    public DateTime? HireDateFrom { get; set; }

    public DateTime? HireDateTo { get; set; }

    public int? LengthOfServiceFromInYears { get; set; }

    public int? LengthOfServiceToInYears { get; set; }

    public DateTime? DateOfTerminationFrom { get; set; }

    public DateTime? DateOfTerminationTo { get; set; }

    public string? PhoneNumber { get; set; }

    public string[]? Genders { get; set; }

    public Guid[]? MaritalStatusIds { get; set; }

    public bool DisplayAllUnderHierarchy { get; set; }

    public bool IsMotherCitizen { get; set; }

    public bool DoesNotHaveSignature { get; set; }

    public bool DoesNotHavePassport { get; set; }

    public bool DoesNotHaveNationalId { get; set; }

    public DateTime? NextPromotionFrom { get; set; }

    public DateTime? NextPromotionTo { get; set; }

    public string[] States { get; set; } = [];
}

public sealed class GetEmployeeListQuery : GetEmployeeListFilter, IRequest<PaginatedResult<EmployeeListDto>>
{
    public OrderByProperty[]? OrderByProperties { get; set; }

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;
}
