using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Features.Servicing.Requests;
using UnifiedHub.Servicing.Queries.ServiceRequests.GetServiceRequestList;

namespace UnifiedHub.EmployeesManagement.Core.Queries.Employees;

public sealed class GetEmployeeServiceRequestListQuery : IRequest<PaginatedResult<ServiceRequestListDto>>
{
    public Guid EmployeeId { get; set; }

    public Guid[]? ServiceIds { get; set; }

    public DateTime? From { get; set; }

    public DateTime? To { get; set; }

    public GetServiceRequestListQuery.SearchScope Scope { get; set; } = GetServiceRequestListQuery.SearchScope.All;

    public string[]? Statuses { get; set; }

    public string[]? States { get; set; }

    public int PageNumber { get; set; }

    public int PageSize { get; set; } = -1;
}
