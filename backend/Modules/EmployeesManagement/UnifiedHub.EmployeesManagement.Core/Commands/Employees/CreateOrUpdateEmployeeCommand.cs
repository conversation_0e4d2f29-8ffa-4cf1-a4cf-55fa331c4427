using ErrorOr;
using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.Dtos.Modules.EmployeesManagement.Employees;

namespace UnifiedHub.EmployeesManagement.Core.Commands.Employees;

public sealed class CreateEmployeeCommand : CreateOrUpdateEmployeeCommand;

public sealed class UpdateEmployeeCommand : CreateOrUpdateEmployeeCommand
{
    public Guid Id { get; set; }
}

public abstract class CreateOrUpdateEmployeeCommand : IRequest<ErrorOr<EmployeeGetDto>>
{
    public MultilingualString Name { get; set; } = null!;

    public MultilingualString? OldName { get; set; }

    public string? Gender { get; set; }

    public Guid? NationalityId { get; set; }

    public string? OldNationalityName { get; set; }

    public Guid? OldNationalityId { get; set; }

    public DateTime? NewNationalityChangeDate { get; set; }

    public DateTime? DateOfBirth { get; set; }

    public string? PlaceOfBirth { get; set; }

    public Guid? MaritalStatusId { get; set; }

    public FileUploadAndReplaceDto? PhotoFile { get; set; }

    public string? NationalIdNumber { get; set; }

    public DateTime? NationalIdExpirationDate { get; set; }

    public string? PassportNumber { get; set; }

    public DateTime? PassportExpirationDate { get; set; }

    public string? PassportUnifiedNumber { get; set; }

    public string? PassportIssueAuthority { get; set; }

    public string? FamilyBookNumber { get; set; }

    public Guid? FamilyBookIssuingCityId { get; set; }

    public Guid? FamilyBookIssuingSuburbId { get; set; }

    public string? ResidencyNumber { get; set; }

    public DateTime? ResidencyExpirationDate { get; set; }

    public Guid? CityId { get; set; }

    public string? Address { get; set; }

    public string? PhoneNumber { get; set; }

    public string? Email { get; set; }

    public string? Number { get; set; }

    public Guid? DepartmentId { get; set; }

    public Guid? JobClassificationId { get; set; }

    public Guid? JobLevelId { get; set; }

    public Guid? JobTitleId { get; set; }

    public Guid? JobCategoryId { get; set; }

    public DateTime? HireDate { get; set; }

    public Guid? EmploymentTypeId { get; set; }

    public string? WorkLocation { get; set; }

    public Guid? MilitaryStatusId { get; set; }

    public double? HeightInCentimeters { get; set; }

    public double? WeightInKilograms { get; set; }

    public string? BloodType { get; set; }

    public Guid? ReligionId { get; set; }

    public string? NationalityAcquisitionMethod { get; set; }

    public DateTime? NationalityAcquisitionDate { get; set; }

    public Guid? UserId { get; set; }

    public IEnumerable<Guid>? QualificationIds { get; set; }

    public string? MotherName { get; set; }

    public DateTime? MotherDateOfBirth { get; set; }

    public string? MotherCitizenPassportIssuePlace { get; set; }

    public string? MotherPlaceOfBirth { get; set; }

    public Guid? MotherNationalityId { get; set; }

    public Guid? QualificationId { get; set; }

    public bool IsMotherCitizen { get; set; }

    public Property[] PatchedProperties { get; set; } = [];

    public InvocationSource InvocationSource { get; set; } = InvocationSource.External;

    public enum Property
    {
        Name,
        Number,
        JobLevelId,
        PhotoFile,
        JobClassificationId,
        DepartmentId
    }
}
