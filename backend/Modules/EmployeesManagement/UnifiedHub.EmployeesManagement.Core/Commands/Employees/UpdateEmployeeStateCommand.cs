using ErrorOr;
using MediatR;
using UnifiedHub.Dtos.Features.FileManagement.Files;

namespace UnifiedHub.EmployeesManagement.Core.Commands.Employees;

public sealed class UpdateEmployeeStateCommand : IRequest<ErrorOr<Unit>>
{
    public Guid? EmployeeId { get; set; }

    public string? State { get; set; }

    public DateTime? Date { get; set; }

    public string? Details { get; set; }

    public FileUploadAndReplaceDto? File { get; set; }
}
