using ErrorOr;
using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.EmployeesManagement.JobClassifications;
using UnifiedHub.Model.Entities.LeavesManagement.ValueObjects;

namespace UnifiedHub.EmployeesManagement.Core.Commands.JobClassifications;

public sealed class CreateJobClassificationCommand : CreateOrUpdateJobClassificationCommand;

public sealed class UpdateJobClassificationCommand : CreateOrUpdateJobClassificationCommand
{
    public Guid Id { get; set; }
}

public abstract class CreateOrUpdateJobClassificationCommand : IRequest<ErrorOr<JobClassificationGetDto>>
{
    public MultilingualString? Name { get; set; }

    public MultilingualString? Description { get; set; }

    public string? Type { get; set; }

    public LeaveConfig? LeaveConfig { get; set; }
}
