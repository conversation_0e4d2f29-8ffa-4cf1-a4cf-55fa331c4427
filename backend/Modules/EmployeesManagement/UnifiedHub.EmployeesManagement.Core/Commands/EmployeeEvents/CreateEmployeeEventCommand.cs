using ErrorOr;
using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.Dtos.Modules.EmployeesManagement.EmployeeEvents;

namespace UnifiedHub.EmployeesManagement.Core.Commands.EmployeeEvents;

public sealed class UpdateEmployeeEventCommand : CreateOrUpdateEmployeeEventCommand
{
    public Guid Id { get; set; }
}

public sealed class CreateEmployeeEventCommand : CreateOrUpdateEmployeeEventCommand
{
    public Guid? EmployeeId { get; set; }
}

public abstract class CreateOrUpdateEmployeeEventCommand : IRequest<ErrorOr<EmployeeEventGetDto>>
{
    public string? Type { get; set; } = null!;

    public DateTime? Time { get; set; }

    public MultilingualString? Details { get; set; }

    public string? ReferenceNumber { get; set; }

    public Guid? ServiceRequestId { get; set; }

    public FileUploadAndReplaceDto? File { get; set; }
}
