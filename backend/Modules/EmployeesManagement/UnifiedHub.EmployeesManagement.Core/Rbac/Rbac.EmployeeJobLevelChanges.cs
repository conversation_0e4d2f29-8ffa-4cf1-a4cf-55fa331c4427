using UnifiedHub.Rbac.Interfaces;

namespace UnifiedHub.EmployeesManagement.Core.Rbac;

public static partial class P
{
    public static class EmployeeJobLevelChanges
    {
        public const string Read = "employees:job_level_changes:read";
        public const string Write = "employees:job_level_changes:write";
        public const string Delete = "employees:job_level_changes:delete";

        public static readonly string[] All = [Read, Write, Delete];
    }
}

public static partial class Roles
{
    public static class EmployeeJobLevelChanges
    {
        public static readonly RbacRole[] All =
        [
            new("employees:job_level_changes", [
                P.EmployeeJobLevelChanges.Read,
                P.EmployeeJobLevelChanges.Write,
                P.EmployeeJobLevelChanges.Delete,
            ])
        ];
    }
}
