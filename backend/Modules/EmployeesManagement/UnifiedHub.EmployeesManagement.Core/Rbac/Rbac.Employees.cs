using UnifiedHub.Rbac.Interfaces;

namespace UnifiedHub.EmployeesManagement.Core.Rbac;

public static partial class P
{
    public static class Employees
    {
        public const string Read = "employees:read";
        public const string ReadSensitive = "employees:read_sensitive";
        public const string Write = "employees:write";
        public const string Delete = "employees:delete";
        public const string Approve = "employees:approve";
        public const string GeneralManagerApprove = "employees:higher_approve";
        public const string ManageLists = "employees:manage_lists";
        public const string Export = "employees:export";
        public const string ArmoryApprove = "employees:armory_approve";
        public const string ItApprove = "employees:it_approve";
        public const string WarehouseApprove = "employees:warehouse_approve";
        public const string AffairsApprove = "employees:affairs_approve";
        public const string FinanceApprove = "employees:finance_approve";

        public static readonly string[] All =
        [
            Read,
            ReadSensitive,
            Write,
            Delete,
            Approve,
            GeneralManagerApprove,
            ManageLists,
            Export,
            ArmoryApprove,
            ItApprove,
            WarehouseApprove,
            AffairsApprove,
            FinanceApprove
        ];
    }
}

public static partial class Roles
{
    public static class Employees
    {
        public static readonly RbacRole[] All =
        [
            new("employees",
            [
                P.Employees.Read,
                P.Employees.ReadSensitive,
                P.Employees.Write,
                P.Employees.Delete,
                P.Employees.Approve,
                P.Employees.ManageLists,
                P.Employees.Export,
            ])
        ];
    }
}
