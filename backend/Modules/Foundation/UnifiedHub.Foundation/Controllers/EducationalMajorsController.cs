using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Web;
using UnifiedHub.Dtos.Modules.Foundation.EducationalMajors;
using UnifiedHub.Foundation.Core.Commands.EducationalMajors;
using UnifiedHub.Foundation.Core.Queries.EducationalMajors;
using UnifiedHub.Foundation.Core.Rbac;
using UnifiedHub.Rbac.Attributes;

namespace UnifiedHub.Foundation.Controllers;

[Authorize]
[ApiController]
[Route("/foundation/educational-majors")]
public sealed class EducationalMajorsController(
    ISender mediator) : BaseApiController
{
    /// <summary>
    /// Returns a list of educational majors.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<EducationalMajorListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List([FromQuery] GetEducationalMajorListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Returns an educational major given an id.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:guid}")]
    [Produces("application/json")]
    [ProducesResponseType<EducationalMajorGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Get(Guid id)
    {
        var query = new GetEducationalMajorByIdQuery { Id = id };
        var result = await mediator.Send(query);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Creates an educational major.
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPost]
    [Rbac(P.Foundation.Write)]
    [Produces("application/json")]
    [ProducesResponseType<EducationalMajorGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Create(CreateEducationalMajorCommand command)
    {
        var result = await mediator.Send(command);
        var controllerRoute = RouteData.Values["controller"]!.ToString()!.ToLower();
        return result.Match(item => Created($"/{controllerRoute}/{item.Id}", item), Problem);
    }

    /// <summary>
    /// Updates an educational major.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPut("{id:guid}")]
    [Rbac(P.Foundation.Write)]
    [ProducesResponseType<EducationalMajorGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Update(Guid id, UpdateEducationalMajorCommand command)
    {
        command.Id = id;
        var result = await mediator.Send(command);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Deletes an educational major.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id:guid}")]
    [Rbac(P.Foundation.Write)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Delete(Guid id)
    {
        var command = new DeleteEducationalMajorCommand { Id = id };
        var result = await mediator.Send(command);
        return result.Match(_ => NoContent(), Problem);
    }
}
