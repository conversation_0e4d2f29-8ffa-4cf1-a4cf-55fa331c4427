using ErrorOr;
using MediatR;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Foundation.Core.Commands.Countries;
using UnifiedHub.Foundation.Persistence;
using UnifiedHub.Model.Entities.Foundation;

namespace UnifiedHub.Foundation.Handlers.Countries;

public sealed class DeleteCountryHandler(
    IFoundationDataSource dataSource,
    IStringLocalizer<SharedResource> l) : IRequestHandler<DeleteCountryCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        DeleteCountryCommand command,
        CancellationToken cancellationToken)
    {
        var data = dataSource
            .Items<Nationality>()
            .Where(x => x.Id == command.Id)
            .Select(x => new
            {
                Item = x,
                IsLinkedToOtherResources = x.Employees.Count != 0
            })
            .SingleOrDefault();

        if (data == null)
        {
            return Error.NotFound();
        }

        if (data.IsLinkedToOtherResources)
        {
            return Error.Failure(description: l["translate_item_is_linked_to_other_resources"]);
        }

        await dataSource.RemoveItemAsync(data.Item, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
