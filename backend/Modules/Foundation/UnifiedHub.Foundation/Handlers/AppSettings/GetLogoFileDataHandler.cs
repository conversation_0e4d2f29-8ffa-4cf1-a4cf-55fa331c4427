using MediatR;
using Microsoft.Extensions.Options;
using UnifiedHub.Core.Constants;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.Foundation.Core.Queries.AppSettings;

namespace UnifiedHub.Foundation.Handlers.AppSettings;

public sealed class GetLogoFileDataHandler(
    IOptions<UnifiedHub.Core.Misc.AppSettings> appSettingsOptions)
    : IRequestHandler<GetLogoFileDataQuery, FileStreamDto>
{
    private readonly UnifiedHub.Core.Misc.AppSettings _appSettings = appSettingsOptions.Value;

    public async Task<FileStreamDto> Handle(
        GetLogoFileDataQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var path = _appSettings.Static?.Logo;
        if (string.IsNullOrEmpty(path))
        {
            return new()
            {
                ContentType = "image/png",
                Stream = new MemoryStream(Convert.FromBase64String(Defaults.Logo))
            };
        }

        return new()
        {
            Stream = File.OpenRead(path),
            ContentType = "image/png"
        };
    }
}
