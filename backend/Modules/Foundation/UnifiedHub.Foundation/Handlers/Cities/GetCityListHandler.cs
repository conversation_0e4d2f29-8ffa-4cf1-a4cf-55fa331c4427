using LinqKit.Core;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.Foundation.Cities;
using UnifiedHub.Foundation.Core.Queries.Cities;
using UnifiedHub.Foundation.Persistence;
using UnifiedHub.Model.Entities.Foundation;
using UnifiedHub.Persistence.Extensions;

namespace UnifiedHub.Foundation.Handlers.Cities;

public sealed class GetCityListHandler(
    IFoundationDataSource dataSource)
    : IRequestHandler<GetCityListQuery, PaginatedResult<CityListDto>>
{
    public async Task<PaginatedResult<CityListDto>> Handle(
        GetCityListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var items = dataSource
            .Items<City>()
            .AsNoTracking()
            .AsExpandable();

        var count = items.Count();

        if (!string.IsNullOrEmpty(query.Keyword))
        {
            items = items.SearchMultilingualString(x => x.Name, query.Keyword);
        }

        items = items.OrderBy(x => x.Order);

        var filteredCount = items.Count();

        if (query.PageSize != -1)
        {
            items = items
                .Skip(query.PageNumber * query.PageSize)
                .Take(query.PageSize);
        }

        return new PaginatedResult<CityListDto>
        {
            Items = items.Select(CityListDto.Mapper()),
            Count = count,
            FilteredCount = filteredCount,
        };
    }
}
