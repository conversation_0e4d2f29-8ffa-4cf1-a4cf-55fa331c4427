using LinqKit.Core;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.Foundation.EducationalMajors;
using UnifiedHub.Foundation.Core.Queries.EducationalMajors;
using UnifiedHub.Foundation.Persistence;
using UnifiedHub.Model.Entities.Foundation;
using UnifiedHub.Persistence.Extensions;

namespace UnifiedHub.Foundation.Handlers.EducationalMajors;

public sealed class GetEducationalMajorListHandler(
    IFoundationDataSource dataSource)
    : IRequestHandler<GetEducationalMajorListQuery, PaginatedResult<EducationalMajorListDto>>
{
    public async Task<PaginatedResult<EducationalMajorListDto>> Handle(
        GetEducationalMajorListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var items = dataSource
            .Items<EducationalMajor>()
            .AsNoTracking()
            .AsExpandable();

        var count = items.Count();

        if (!string.IsNullOrEmpty(query.Keyword))
        {
            items = items.SearchMultilingualString(x => x.Name, query.Keyword);
        }

        var filteredCount = items.Count();

        items = items.OrderByDescending(x => x.CreatedTime);

        if (query.PageSize != -1)
        {
            items = items
                .Skip(query.PageNumber * query.PageSize)
                .Take(query.PageSize);
        }

        return new PaginatedResult<EducationalMajorListDto>
        {
            Items = items.Select(EducationalMajorListDto.Mapper()),
            Count = count,
            FilteredCount = filteredCount,
        };
    }
}
