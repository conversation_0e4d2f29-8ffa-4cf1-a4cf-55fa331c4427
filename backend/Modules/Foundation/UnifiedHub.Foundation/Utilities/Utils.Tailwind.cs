using System.Globalization;
using System.Text.RegularExpressions;

namespace UnifiedHub.Foundation.Utilities;

public static partial class Utils
{
    public static class Tailwind
    {
        private static (int Red, int Green, int Blue) HexToRgb(string hex)
        {
            const string pattern = "^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$";
            var match = Regex.Match(hex, pattern, RegexOptions.IgnoreCase);


            if (match.Groups.Count != 4)
            {
                throw new Exception($"Invalid color in hex: {hex}.");
            }

            return (
                int.Parse(match.Groups[1].Value, NumberStyles.HexNumber),
                int.Parse(match.Groups[2].Value, NumberStyles.HexNumber),
                int.Parse(match.Groups[3].Value, NumberStyles.HexNumber)
            );
        }

        private static string RgbToHex(int red, int green, int blue)
        {
            string ToHex(int number)
            {
                return $"0{number:X}"[^2..];
            }

            return $"#{ToHex(red)}{ToHex(green)}{ToHex(blue)}";
        }

        private static string Lighten(string hex, double intensity)
        {
            var color = HexToRgb(hex);

            var r = (int) Math.Round(color.Red + ((255 - color.Red) * intensity));
            var g = (int) Math.Round(color.Green + ((255 - color.Green) * intensity));
            var b = (int) Math.Round(color.Blue + ((255 - color.Blue) * intensity));

            return RgbToHex(r, g, b);
        }

        private static string Darken(string hex, double intensity)
        {
            var color = HexToRgb(hex);

            var r = (int) Math.Round(color.Red * intensity);
            var g = (int) Math.Round(color.Green * intensity);
            var b = (int) Math.Round(color.Blue * intensity);

            return RgbToHex(r, g, b);
        }


        public static string[] Generate(string baseColor)
        {
            var intensities = new[] { 0.95, 0.9, 0.75, 0.6, 0.3, 0.9, 0.75, 0.6, 0.49, 0.35 };

            var colors = new List<string>();

            intensities[..5].ToList().ForEach(x => colors.Add(Lighten(baseColor, x)));
            colors.Add(baseColor);
            intensities[5..].ToList().ForEach(x => colors.Add(Darken(baseColor, x)));

            return colors.ToArray();
        }
    }
}
