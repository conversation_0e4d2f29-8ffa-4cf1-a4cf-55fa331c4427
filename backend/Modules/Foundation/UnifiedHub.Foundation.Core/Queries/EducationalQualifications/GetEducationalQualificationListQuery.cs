using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.Foundation.EducationalQualifications;

namespace UnifiedHub.Foundation.Core.Queries.EducationalQualifications;

public sealed class GetEducationalQualificationListQuery : IRequest<PaginatedResult<EducationalQualificationListDto>>
{
    public string? Keyword { get; set; }

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;
}
