using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.Foundation.Suburbs;

namespace UnifiedHub.Foundation.Core.Queries.Suburbs;

public sealed class GetSuburbListQuery : IRequest<PaginatedResult<SuburbListDto>>
{
    public string? Keyword { get; set; }

    public Guid[]? CityIds { get; set; }

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;
}
