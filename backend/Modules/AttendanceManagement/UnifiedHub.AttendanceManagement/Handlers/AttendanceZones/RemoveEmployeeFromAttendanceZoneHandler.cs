using ErrorOr;
using MediatR;
using UnifiedHub.AttendanceManagement.Core.Commands.AttendanceZones;
using UnifiedHub.AttendanceManagement.Persistence;
using UnifiedHub.Model.Entities.Attendance;

namespace UnifiedHub.AttendanceManagement.Handlers.AttendanceZones;

public sealed class RemoveEmployeeFromAttendanceZoneHandler(
    IAttendanceDataSource dataSource)
    : IRequestHandler<RemoveEmployeeListFromAttendanceZoneListCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        RemoveEmployeeListFromAttendanceZoneListCommand command,
        CancellationToken cancellationToken)
    {
        var query = dataSource.Items<AttendanceZoneEmployeeLink>()
            .Where(x => command.ZoneIds.Contains(x.ZoneId) && command.EmployeeIds.Contains(x.EmployeeId));

        await dataSource.RemoveItemsAsync(query, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
