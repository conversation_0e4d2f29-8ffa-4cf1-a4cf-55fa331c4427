using LinqKit;
using MediatR;
using UnifiedHub.AttendanceManagement.Core.Queries.Misc;
using UnifiedHub.AttendanceManagement.Persistence;
using UnifiedHub.Core.Services;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.Dtos.Modules.AttendanceManagement.Misc;
using UnifiedHub.Dtos.SqlFunctions.Misc;
using UnifiedHub.Model.Entities.Attendance;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.AttendanceManagement.Handlers.Misc;

public sealed class GetAttendanceComplianceRateTrendHandler(
    IDateTimeProvider dateTimeProvider,
    IAttendanceDataSource dataSource)
    : IRequestHandler<GetAttendanceComplianceRateTrendQuery, IEnumerable<AttendanceComplianceRateTrendPointDto>>
{
    public async Task<IEnumerable<AttendanceComplianceRateTrendPointDto>> Handle(
        GetAttendanceComplianceRateTrendQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var now = dateTimeProvider.UtcNow;
        query.To ??= now;
        query.From ??= query.To.Value.AddDays(-30);


        var isAbsent = E.AttendanceTransactions.IsAbsent(now);
        return dataSource.Items<Employee>()
            .AsExpandable()
            .Select(_ => new
            {
                Results = GenerateDateTimeRangeSqlFunction.Call(query.From.Value, query.To.Value, 24 * 60 * 60)
                    .Select(x => new AttendanceComplianceRateTrendPointDto
                    {
                        Date = x.Value,
                        Value = dataSource.Items<Employee>()
                            .AsExpandable()
                            .Join(
                                dataSource.Items<AttendanceTransaction>(),
                                employee => employee.Id,
                                transaction => transaction.EmployeeId,
                                (employee, transaction) => new { employee, transaction }
                            )
                            .Where(ut =>
                                ut.transaction.OutTime >= x.Value && ut.transaction.OutTime < x.Value.AddDays(1) &&
                                ut.transaction.OutTime < now)
                            .GroupBy(ut => ut.employee.Id)
                            .Select(g => new
                            {
                                EmployeeId = g.Key,
                                TotalCount = g.Count(),
                                PresentCount = g.Count(t => !isAbsent.Invoke(t.transaction))
                            })
                            .Where(us => us.TotalCount > 0)
                            .Select(us => (double?) us.PresentCount / us.TotalCount)
                            .Average()
                    })
                    .ToList()
            })
            .Select(x => x.Results)
            .First();
    }
}
