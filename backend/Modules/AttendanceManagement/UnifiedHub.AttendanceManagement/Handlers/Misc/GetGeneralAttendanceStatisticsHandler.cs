using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.AttendanceManagement.Core.Queries.Misc;
using UnifiedHub.AttendanceManagement.Persistence;
using UnifiedHub.Core.Services;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.Dtos.Modules.AttendanceManagement.Misc;
using UnifiedHub.Model.Entities.Attendance;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.AttendanceManagement.Handlers.Misc;

public sealed class GetGeneralAttendanceStatisticsHandler(
    IAttendanceDataSource dataSource,
    IDateTimeProvider dateTimeProvider)
    : IRequestHandler<GetGeneralAttendanceStatisticsQuery, GeneralAttendanceStatisticsDto>
{
    public async Task<GeneralAttendanceStatisticsDto> Handle(
        GetGeneralAttendanceStatisticsQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var now = dateTimeProvider.UtcNow;
        query.To ??= now;
        query.From ??= query.To.Value.AddDays(-30);

        var items = dataSource.Items<AttendanceTransaction>()
            .Where(x => query.From <= x.InTime && x.InTime < query.To);

        var isLate = E.AttendanceTransactions.IsLate();
        var isEarly = E.AttendanceTransactions.IsEarly();
        var isAbsent = E.AttendanceTransactions.IsAbsent(dateTimeProvider.UtcNow);

        var result = dataSource.Items<User>()
            .AsNoTracking()
            .AsExpandable()
            .Select(_ => new GeneralAttendanceStatisticsDto
            {
                ActiveEmployeeCount = dataSource.Items<Employee>().Count(y => y.State == Employee.StateActive),
                EmployeeCount = dataSource.Items<User>().Count(),
                TransactionCount = items.Count(),
                CompliedCount = items.Count(y => !isLate.Invoke(y) && !isEarly.Invoke(y) && !isAbsent.Invoke(y)),
                LateInCount = items.Count(y => isLate.Invoke(y)),
                EarlyOutCount = items.Count(y => isEarly.Invoke(y)),
                AbsenceCount = items.Count(y => isAbsent.Invoke(y)),
                PunchCount = dataSource.Items<Punch>().Count(y => query.From <= y.Time && y.Time < query.To),
                ComplianceRate = dataSource.Items<Employee>()
                    .Join(
                        dataSource.Items<AttendanceTransaction>(),
                        employee => employee.Id,
                        transaction => transaction.EmployeeId,
                        (employee, transaction) => new { employee, transaction }
                    )
                    .Where(et =>
                        et.transaction.OutTime >= query.From && et.transaction.OutTime < query.To &&
                        et.transaction.OutTime < now)
                    .GroupBy(et => et.employee.Id)
                    .Select(g => new
                    {
                        EmployeeId = g.Key,
                        TotalCount = g.Count(),
                        PresentCount = g.Count(t => !isAbsent.Invoke(t.transaction))
                    })
                    .Where(es => es.TotalCount > 0)
                    .Select(es => (double?) es.PresentCount / es.TotalCount)
                    .Average()
            })
            .First();

        return result;
    }
}
