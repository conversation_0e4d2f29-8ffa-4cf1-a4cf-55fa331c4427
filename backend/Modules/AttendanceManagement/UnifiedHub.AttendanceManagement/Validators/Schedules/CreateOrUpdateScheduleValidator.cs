using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.AttendanceManagement.Core.Commands.Schedules;
using UnifiedHub.Core.Extensions;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Model.Entities.Attendance;

namespace UnifiedHub.AttendanceManagement.Validators.Schedules;

public class CreateScheduleValidator : AbstractValidator<CreateScheduleCommand>
{
    public CreateScheduleValidator(IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public class UpdateScheduleValidator : AbstractValidator<UpdateScheduleCommand>
{
    public UpdateScheduleValidator(IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public static class AbstractValidatorApply
{
    public static void Apply<TCommand>(
        this AbstractValidator<TCommand> validator,
        IStringLocalizer<SharedResource> l)
        where TCommand : CreateOrUpdateScheduleCommand
    {
        validator.RuleFor(x => x.Name)
            .NotEmptyMultilingualString()
            .WithMessage(l["translate_0_is_required", l["translate_name"]]);

        validator.RuleFor(x => x.StartTime)
            .NotNull()
            .WithMessage(l["translate_start_time_is_required"]);

        validator.RuleFor(x => x.Records).NotNull()
            .WithMessage(l["translate_records_is_required_required"])
            .NotEmpty().WithMessage(l["translate_there_should_be_at_least_one_record"]);

        validator.RuleForEach(x => x.Records)
            .Must(x => new[] { Schedule.Record.TypeOnDuty, Schedule.Record.TypeOffDuty }.Contains(x.Type))
            .WithMessage(l["translate_invalid_type_was_provided"])
            .ChildRules(x =>
            {
                x.RuleFor(y => y.Type)
                    .Must(y => new[] { Schedule.Record.TypeOnDuty, Schedule.Record.TypeOffDuty }.Contains(y))
                    .WithMessage(l["translate_invalid_type_was_provided"]);

                x.RuleFor(y => y.DurationInHours)
                    .NotNull()
                    .WithMessage(l["translate_duration_in_hours_is_required"])
                    .Must(y => y > 0)
                    .WithMessage(l["translate_duration_cannot_be_less_than_or_equal_0"]);
            });
    }
}
