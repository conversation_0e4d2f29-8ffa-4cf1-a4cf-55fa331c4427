using UnifiedHub.Dtos.Modules.AttendanceManagement.Schedules;
using UnifiedHub.Model.Entities.Attendance;

namespace UnifiedHub.AttendanceManagement.Utilities;

public static partial class Utils
{
    public static class Schedules
    {
        public static List<ScheduleRecordMaterializedDto> Materialize(
            List<Schedule.Record> records,
            DateTime scheduleStartTime,
            DateTime startTime,
            DateTime endTime)
        {
            // To eliminate rounding errors coming
            // from split seconds.
            startTime = new DateTime(
                startTime.Year,
                startTime.Month,
                startTime.Day,
                startTime.Hour,
                startTime.Minute,
                startTime.Second);

            var (recordIndex, recordDistanceInSeconds) = GetCurrentRecord(startTime, scheduleStartTime, records);

            if (recordDistanceInSeconds != 0)
            {
                startTime = startTime.AddSeconds(
                    (records[recordIndex].DurationInHours * 3600) - recordDistanceInSeconds);
                recordIndex = (recordIndex + 1) % records.Count;
            }

            var periods = new List<ScheduleRecordMaterializedDto>();

            var currentStartTime = startTime;
            var counter = recordIndex;

            while (currentStartTime < endTime)
            {
                var record = records[counter % records.Count];

                var nextStartTime = currentStartTime.AddHours(record.DurationInHours);

                if (record.Type == "on_duty")
                {
                    periods.Add(new() { InTime = currentStartTime, OutTime = nextStartTime });
                }

                currentStartTime = nextStartTime;
                counter++;
            }

            return periods;
        }


        private static (int RecordIndex, double RecordDistance) GetCurrentRecord(
            DateTime queryTime,
            DateTime startTime,
            List<Schedule.Record> records)
        {
            // The period of an entire cycle of the schedule.
            var totalPeriodInSeconds = records.Sum(r => r.DurationInHours * 3600);

            // An ordered list of numbers representing the cumulative sum of the durations
            // for the records in the schedule.
            var recordsCumSum = records
                .Select((_, index) => records.Take(index + 1).Sum(x => x.DurationInHours * 3600))
                .ToList();


            // How far is the query from the start time of the schedule.
            var distanceInSeconds = (queryTime - startTime).TotalSeconds;

            // How far along are we in the cycle.
            var localDistanceInSeconds = Math.Floor(distanceInSeconds % totalPeriodInSeconds);

            var recordIndex = recordsCumSum.FindIndex(x => x > localDistanceInSeconds);
            var record = records[recordIndex];

            var recordDistanceInSeconds =
                (record.DurationInHours * 3600) - (recordsCumSum[recordIndex] - localDistanceInSeconds);

            return (recordIndex, recordDistanceInSeconds);
        }
    }
}
