using ErrorOr;
using MediatR;
using UnifiedHub.Dtos.Modules.AttendanceManagement.AttendancePermissions;

namespace UnifiedHub.AttendanceManagement.Core.Commands.AttendancePermissions;

public sealed class CreateAttendancePermissionCommand : CreateOrUpdateAttendancePermissionCommand
{
    public Guid? EmployeeId { get; set; }
    
    public string? Type { get; set; }

    public Guid? ServiceRequestId { get; set; }
}

public sealed class UpdateAttendancePermissionCommand : CreateOrUpdateAttendancePermissionCommand
{
    public Guid Id { get; set; }

}

public abstract class CreateOrUpdateAttendancePermissionCommand : IRequest<ErrorOr<AttendancePermissionGetDto>>
{
    public DateTime? From { get; set; }

    public DateTime? To { get; set; }
}
