using UnifiedHub.Rbac.Interfaces;

namespace UnifiedHub.AttendanceManagement.Core.Rbac;

public static partial class P
{
    public static class Punches
    {
        public const string Read = "attendance:punches:read";

        public static readonly string[] All = [Read];
    }
}

public static partial class Roles
{
    public static class Punches
    {
        public static readonly RbacRole[] All = [
            new("punches", [
                P.Punches.Read,
            ])
        ];
    }
}
