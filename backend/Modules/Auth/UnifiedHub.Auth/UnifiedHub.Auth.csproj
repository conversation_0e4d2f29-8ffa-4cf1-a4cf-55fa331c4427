<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\..\..\UnifiedHub.Core\UnifiedHub.Core.csproj"/>
    <ProjectReference Include="..\..\..\UnifiedHub.Dtos\UnifiedHub.Dtos.csproj"/>
    <ProjectReference Include="..\..\..\UnifiedHub.Module\UnifiedHub.Module.csproj"/>
    <ProjectReference Include="..\..\..\Features\UnifiedHub.Emailing\UnifiedHub.Emailing.csproj"/>
    <ProjectReference Include="..\..\..\Features\UnifiedHub.FileManagement\UnifiedHub.FileManagement.csproj"/>
    <ProjectReference Include="..\..\..\Features\UnifiedHub.Rbac\UnifiedHub.Rbac.csproj"/>
    <ProjectReference Include="..\..\RolesManagement\UnifiedHub.RolesManagement.Core\UnifiedHub.RolesManagement.Core.csproj" />
    <ProjectReference Include="..\UnifiedHub.Auth.Core\UnifiedHub.Auth.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Notifications\RoleUpdate\" />
    <Folder Include="Notifications\UserPermissionUpdate\" />
  </ItemGroup>

</Project>
