using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using UnifiedHub.Core.Misc;
using UnifiedHub.Core.Services;
using UnifiedHub.Model.Entities.Identity;
using IdentityClaimNames = UnifiedHub.Auth.Constants.IdentityClaimNames;
using JwtRegisteredClaimNames = Microsoft.IdentityModel.JsonWebTokens.JwtRegisteredClaimNames;

namespace UnifiedHub.Auth.Services.JwtGenerator;

public sealed class JwtGenerator(
    IDateTimeProvider dateTimeProvider,
    IOptions<JwtSettings> jwtSettingsOptions)
    : IJwtGenerator
{
    private readonly JwtSettings _jwtSettings = jwtSettingsOptions.Value;

    public async Task<string> GenerateAsync(
        User user,
        Guid refreshTokenId,
        CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;

        var signingCredentials = new SigningCredentials(
            new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Secret)),
            SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
            new Claim(JwtRegisteredClaimNames.GivenName, $"{user.Name.En}|{user.Name.Ar}"),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
        };

        var securityToken = new JwtSecurityToken(
            issuer: _jwtSettings.Issuer,
            audience: _jwtSettings.Audience,
            expires: dateTimeProvider.UtcNow.AddSeconds(_jwtSettings.ExpirySeconds),
            claims: claims,
            signingCredentials: signingCredentials
        )
        {
            Payload =
            {
                [IdentityClaimNames.RefreshTokenId] = refreshTokenId.ToString(),
                [IdentityClaimNames.User] = new Dictionary<string, object>
                {
                    { "id", user.Id.ToString() },
                    { "name", new Dictionary<string, string> { { "ar", user.Name.Ar }, { "en", user.Name.En } } },
                    { "email", user.Email },
                },
                [IdentityClaimNames.Departments] = user.DepartmentLinks.Select(link =>
                    new Dictionary<string, object>
                    {
                        {
                            "id",
                            link.DepartmentId.ToString()
                        },
                        {
                            "name",
                            new Dictionary<string, string>
                            {
                                { "ar", link.Department.Name.Ar },
                                { "en", link.Department.Name.En }
                            }
                        },
                        { "hierarchyCode", link.Department.HierarchyCode }
                    }).ToArray()
            }
        };

        return new JwtSecurityTokenHandler().WriteToken(securityToken);
    }
}
