using Microsoft.AspNetCore.Http;
using UnifiedHub.Auth.Core.Rbac;
using UnifiedHub.Common.Extensions;
using UnifiedHub.Core.Services.IdentityService;
using IdentityClaimNames = UnifiedHub.Auth.Constants.IdentityClaimNames;

namespace UnifiedHub.Auth.Services;

public sealed class IdentityService : IIdentityService
{
    public IdentityService(
        IPermissionCache permissionCache,
        IHttpContextAccessor? httpContextAccessor = null)
    {
        var principal = httpContextAccessor?.HttpContext?.User;

        if (principal != null)
        {
            var userStr = principal.Claims.SingleOrDefault(x => x.Type == IdentityClaimNames.User)?.Value;
            if (userStr != null)
            {
                User = userStr.DeserializeFromJsonString<IdentityUser>();
            }

            var departmentStrings = principal.Claims.Where(x => x.Type == IdentityClaimNames.Departments);
            Departments = departmentStrings
                .Select(x => x.Value.DeserializeFromJsonString<IdentityDepartment>())
                .Where(x => x != null)
                .Select(x => x!)
                .ToArray();

            if (User != null)
            {
                var task = permissionCache.GetAsync(User.Id);
                task.Wait();

                Permissions = task.Result;
            }

            HasFullDepartmentScope = Permissions.Contains(P.Misc.FullDepartmentScope);
        }
    }

    public IdentityUser? User { get; }

    public IdentityDepartment[]? Departments { get; }

    public bool HasFullDepartmentScope { get; }

    public string[] Permissions { get; } = [];
}
