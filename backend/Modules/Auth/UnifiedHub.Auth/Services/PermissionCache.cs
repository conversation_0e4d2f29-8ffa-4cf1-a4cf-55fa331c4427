using UnifiedHub.Auth.Persistence;
using UnifiedHub.Core.Services.Caching;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Auth.Services;

public sealed class PermissionCache(
    IAuthDataSource dataSource,
    ICache cache) : IPermissionCache
{
    public const string CacheStoreName = "permission_cache";

    public async Task<string[]> GetAsync(Guid userId)
    {
        var permissions = await cache.GetAsync<string[]>(CacheStoreName, userId.ToString());
        if (permissions != null)
        {
            return permissions;
        }
        
        permissions = dataSource
            .Items<RoleUserLink>()
            .Where(x => x.UserId == userId)
            .SelectMany(x => x.Role.Permissions)
            .ToArray();
        
        await cache.SetAsync(CacheStoreName, userId.ToString(), permissions);
        
        return permissions;
    }

    public async Task ResetAsync(Guid userId)
    {
        await cache.RemoveAsync(CacheStoreName, userId.ToString());
    }

    public async Task ClearAsync()
    {
        await cache.ClearAsync(CacheStoreName);
    }
}
