using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Auth.Services.ResetPasswordTokenGenerator;

public interface IResetPasswordTokenGenerator
{
    public Task<string> GenerateAsync(User user, CancellationToken cancellationToken = default);

    public Task<bool> VerifyAsync(string token, User user, CancellationToken cancellationToken = default);

    public Task<Guid?> GetUserId(string token, CancellationToken cancellationToken = default);
}
