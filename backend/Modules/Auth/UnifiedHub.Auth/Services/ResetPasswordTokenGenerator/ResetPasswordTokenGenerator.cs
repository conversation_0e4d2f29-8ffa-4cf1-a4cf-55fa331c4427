using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using UnifiedHub.Common.Extensions;
using UnifiedHub.Core.Misc;
using UnifiedHub.Core.Services;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Auth.Services.ResetPasswordTokenGenerator;

public sealed class ResetPasswordTokenGenerator(
    IOptions<JwtSettings> jwtSettingsOptions,
    IDateTimeProvider dateTimeProvider) : IResetPasswordTokenGenerator
{
    private readonly JwtSettings _jwtSettings = jwtSettingsOptions.Value;

    public async Task<string> GenerateAsync(User user, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;

        var data = new ResetPasswordToken
        {
            UserId = user.Id,
            UserEmail = user.Email,
            ExpiredTime = dateTimeProvider.UtcNow.AddMinutes(15),
            SecurityStamp = user.SecurityStamp
        };

        var dataBytes = ConvertToBytes(data);

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Secret));
        using var hmac = new HMACSHA256(key.Key);
        var signature = hmac.ComputeHash(dataBytes);

        return $"{Convert.ToBase64String(dataBytes)}.{Convert.ToBase64String(signature)}";
    }

    public async Task<bool> VerifyAsync(string token, User user, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var splits = token.Split(".");
        if (splits.Length != 2)
        {
            return false;
        }

        var dataBase64 = splits[0];
        var signatureBase64 = splits[1];

        var dataBytes = Convert.FromBase64String(dataBase64);

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Secret));
        using var hmac = new HMACSHA256(key.Key);
        var toBeCheckedSignature = Convert.ToBase64String(hmac.ComputeHash(dataBytes));

        if (signatureBase64 != toBeCheckedSignature)
        {
            return false;
        }

        var data = ConvertToObject<ResetPasswordToken>(dataBytes);
        if (data == null)
        {
            return false;
        }

        if (data.UserId != user.Id)
        {
            return false;
        }

        if (data.UserEmail != user.Email)
        {
            return false;
        }

        if (data.ExpiredTime < dateTimeProvider.UtcNow)
        {
            return false;
        }

        if (data.SecurityStamp != user.SecurityStamp)
        {
            return false;
        }

        return true;
    }

    public async Task<Guid?> GetUserId(string token, CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;

        var splits = token.Split(".");
        if (splits.Length != 2)
        {
            return null;
        }

        var dataBase64 = splits[0];

        var dataBytes = Convert.FromBase64String(dataBase64);

        var data = ConvertToObject<ResetPasswordToken>(dataBytes);

        return data?.UserId;
    }

    private static byte[] ConvertToBytes<T>(T data)
    {
        return Encoding.UTF8.GetBytes(JsonSerializer.Serialize(data, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        }));
    }

    private static T? ConvertToObject<T>(byte[] bytes) where T : class
    {
        return Encoding.UTF8.GetString(bytes).DeserializeFromJsonString<T>();
    }
}
