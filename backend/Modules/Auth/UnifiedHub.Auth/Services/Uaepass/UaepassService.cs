using ErrorOr;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Options;
using UnifiedHub.Common.Extensions;
using UnifiedHub.Core.Resources.Shared;

namespace UnifiedHub.Auth.Services.Uaepass;

public sealed class UaepassService(
    IOptions<UaepassSettings> uaepassSettingsOptions,
    IStringLocalizer<SharedResource> l) : IUaepassService
{
    private readonly UaepassSettings _uaepassSettings = uaepassSettingsOptions.Value;


    public async Task<ErrorOr<UaepassUser>> GetUserAsync(
        string authorizationCode,
        string redirectUrl,
        CancellationToken cancellationToken = default)
    {
        var result = await GetAccessTokenAsync(authorizationCode, redirectUrl, cancellationToken);
        if (result.IsError)
        {
            return result.Errors.ToErrorOr<UaepassUser>();
        }

        var accessToken = result.Value;

        using var client = new HttpClient();
        client.DefaultRequestHeaders.Add("Authorization", $"Bearer {accessToken}");
        try
        {
            var jsonString = await client.GetStringAsync(_uaepassSettings.UserProfileEndpoint, cancellationToken);
            var user = jsonString.DeserializeFromJsonString<UaepassUser>()!;
            user.Uuid = user.Uuid.Trim().ToLower();
            user.Email = user.Email.Trim().ToLower();
            user.Idn = user.Idn?.Trim();
            return user;
        }
        catch
        {
            return Error.Failure(description: l["translate_failed_to_fetch_uae_pass_user_details"]);
        }
    }

    public string GetClient()
    {
        return _uaepassSettings.Client;
    }

    private async Task<ErrorOr<string>> GetAccessTokenAsync(
        string authorizationCode,
        string redirectUrl,
        CancellationToken cancellationToken = default)
    {
        using var client = new HttpClient();
        client.DefaultRequestHeaders.Add("Authorization", $"Basic {_uaepassSettings.TokenEndpointAuthCode}");

        var content = new FormUrlEncodedContent(new Dictionary<string, string>
        {
            { "grant_type", "authorization_code" },
            { "redirect_uri", redirectUrl },
            { "code", authorizationCode }
        });

        try
        {
            var response = await client.PostAsync(_uaepassSettings.TokenEndpoint, content, cancellationToken);
            var jsonString = await response.Content.ReadAsStringAsync(cancellationToken);

            return jsonString.ToJson()["access_token"]!.GetValue<string>();
        }
        catch (Exception e)
        {
            Console.WriteLine("Error parsing response from uaepass:");
            Console.WriteLine(e.Message);
            Console.WriteLine(e.StackTrace);

            return Error.Failure(
                description: l["translate_failed_to_authorize_vai_the_provided_authorization_code"]);
        }
    }
}
