// using Microsoft.EntityFrameworkCore;
// using UnifiedHub.Model.Entities.Identity;
// using UnifiedHub.Persistence.Interfaces;
// using UnifiedHub.Persistence.Misc;
// using File = UnifiedHub.Model.Entities.Files.File;

using Microsoft.EntityFrameworkCore;
using UnifiedHub.Persistence.Interfaces;
using UnifiedHub.Persistence.Misc;

namespace UnifiedHub.Auth.Persistence;

public interface IAuthDataSource : IDataSource;

public sealed class DataContext(DbContextOptions<DataContext> options) : BaseDataContext(options), IAuthDataSource
{
    // public DbSet<User> Users { get; set; } = null!;
    // public DbSet<Department> Departments { get; set; } = null!;
    // public DbSet<RefreshToken> RefreshTokens { get; set; } = null!;
    // public DbSet<DepartmentUserLink> DepartmentUserLinks { get; set; } = null!;
    // public DbSet<RoleUserLink> RoleUserLinks { get; set; }
    // public DbSet<Role> Roles { get; set; }
    // public DbSet<Permission> Permissions { get; set; }
    //
    // public DbSet<File> Files { get; set; }
}
