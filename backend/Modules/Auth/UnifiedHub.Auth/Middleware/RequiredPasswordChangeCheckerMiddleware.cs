using System.Text.Json;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Options;
using UnifiedHub.Auth.Extensions;
using UnifiedHub.Auth.Persistence;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Auth.Middleware;

public static class RequiredPasswordChangeCheckerApplicationBuilderExtensions
{
    public static IApplicationBuilder UseRequiredPasswordChangeChecker(this IApplicationBuilder app)
    {
        return app.UseMiddleware<RequiredPasswordChangeCheckerMiddleware>();
    }
}

public class RequiredPasswordChangeCheckerMiddleware(
    RequestDelegate requestDelegate)
{
    public async Task Invoke(
        HttpContext context,
        IIdentityService identityService,
        IAuthDataSource dataSource,
        IStringLocalizer<SharedResource> l,
        IOptions<AuthModuleOptions> options)
    {
        await Task.CompletedTask;

        var userId = identityService.User?.Id;
        if (userId == null)
        {
            await requestDelegate.Invoke(context);
            return;
        }

        var user = await dataSource.FindItemAsync<User>([userId]);
        if (user is not { IsPasswordChangeRequired: true })
        {
            await requestDelegate.Invoke(context);
            return;
        }

        var allowedPaths = new[]
        {
            "/auth/refresh",
            "/users/current",
            "/foundation"
        };

        if (options.Value.AllowedEndpointsDuringRequiredPasswordChange is { Length: > 0 })
        {
            allowedPaths = [..allowedPaths, ..options.Value.AllowedEndpointsDuringRequiredPasswordChange];
        }

        if (allowedPaths.Any(x => context.Request.Path.StartsWithSegments(x)))
        {
            await requestDelegate.Invoke(context);
            return;
        }

        context.Response.ContentType = "application/problem+json";
        context.Response.StatusCode = StatusCodes.Status500InternalServerError;

        var problemDetails = new ProblemDetails
        {
            Status = StatusCodes.Status403Forbidden,
            Title = l["translate_required_password_change"],
            Detail = l[
                "translate_your_password_has_been_reset_by_an_administrator_you_need_to_change_it_before_using_the_application"],
            Instance = context.Request.Path,
            Type = "https://unifiedhub.com/errors/required-password-change"
        };

        var json = JsonSerializer.Serialize(problemDetails, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        });
        context.Response.StatusCode = StatusCodes.Status403Forbidden;
        await context.Response.WriteAsync(json);
    }
}
