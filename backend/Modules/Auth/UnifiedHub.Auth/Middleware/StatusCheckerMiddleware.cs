using System.Text.Json;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Options;
using UnifiedHub.Auth.Extensions;
using UnifiedHub.Auth.Persistence;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Auth.Middleware;

public static class StatusChangeCheckerApplicationBuilderExtensions
{
    public static IApplicationBuilder UseStatusChangeChecker(this IApplicationBuilder app)
    {
        return app.UseMiddleware<StatusChangeCheckerMiddleware>();
    }
}

public class StatusChangeCheckerMiddleware(
    RequestDelegate requestDelegate)
{
    public async Task Invoke(
        HttpContext context,
        IIdentityService identityService,
        IAuthDataSource dataSource,
        IStringLocalizer<SharedResource> l,
        IOptions<AuthModuleOptions> options)
    {
        await Task.CompletedTask;

        var userId = identityService.User?.Id;
        if (userId == null)
        {
            await requestDelegate.Invoke(context);
            return;
        }

        var user = await dataSource.FindItemAsync<User>([userId]);
        if (user == null || user.State == User.StateActive)
        {
            await requestDelegate.Invoke(context);
            return;
        }

        var problemDetails = new ProblemDetails
        {
            Status = StatusCodes.Status403Forbidden,
            Title = l["translate_user_not_active"],
            Detail = l["translate_your_account_is_not_active_please_contact_the_administrator_to_activate_it"],
            Instance = context.Request.Path,
            Type = "https://unifiedhub.com/errors/user-not-active"
        };

        var json = JsonSerializer.Serialize(problemDetails, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        });
        context.Response.StatusCode = StatusCodes.Status403Forbidden;
        await context.Response.WriteAsync(json);
    }
}
