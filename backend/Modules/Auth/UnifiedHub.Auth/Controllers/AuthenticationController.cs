using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UnifiedHub.Auth.Core.Commands;
using UnifiedHub.Auth.Core.Queries;
using UnifiedHub.Core.Misc;
using UnifiedHub.Core.Web;
using UnifiedHub.Dtos.Features.Identity.Authentication;

namespace UnifiedHub.Auth.Controllers;

[ApiController]
[Route("/auth")]
public sealed class AuthenticationController(
    ISender mediator) : BaseApiController
{
    /// <summary>
    /// Gets the details of the currently signed in user.
    /// </summary>
    /// <returns></returns>
    [Authorize]
    [HttpGet("whoami")]
    [ProducesResponseType<WhoAmIDto>(StatusCodes.Status200OK)]
    [Produces("application/json")]
    public async Task<IActionResult> WhoAmI()
    {
        var query = new WhoAmIQuery();
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Logs a user in using email and password.
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPost("login")]
    [ProducesResponseType<LoginSuccessDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> Login(LoginCommand command)
    {
        var result = await mediator.Send(command);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Authenticate users using uaepass via access code.
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPost("uaepass")]
    [ProducesResponseType<LoginSuccessDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> LoginWithUaepass(LoginWithUaepassCommand command)
    {
        var result = await mediator.Send(command);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Generates new access token given a refresh token.
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPost("refresh")]
    [ProducesResponseType<string>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> RefreshToken(SingleValueDto<string> dto)
    {
        var query = new RefreshTokenQuery { RefreshToken = dto.Value ?? "" };
        var result = await mediator.Send(query);
        return result.Match(Ok, Problem);
    }


    /// <summary>
    /// Logs a user out.
    /// </summary>
    /// <returns></returns>
    [Authorize]
    [HttpPost("logout")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [Produces("application/json")]
    public async Task<IActionResult> Logout()
    {
        var command = new LogoutCommand();
        await mediator.Send(command);
        return Ok();
    }


    /// <summary>
    /// Sends a reset password link by email.
    /// </summary>
    /// <param name="email"></param>
    /// <returns></returns>
    [HttpPost("reset-password/send/{email}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [Produces("application/json")]
    public async Task<IActionResult> SendResetPasswordLink(string email)
    {
        var command = new SendResetPasswordLinkCommand { Email = email };
        await mediator.Send(command);
        return Ok();
    }

    /// <summary>
    /// Resets password given a valid token
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPost("reset-password")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    [Produces("application/json")]
    public async Task<IActionResult> ResetPassword(ResetPasswordCommand command)
    {
        var result = await mediator.Send(command);
        return result.Match(_ => NoContent(), Problem);
    }
}
