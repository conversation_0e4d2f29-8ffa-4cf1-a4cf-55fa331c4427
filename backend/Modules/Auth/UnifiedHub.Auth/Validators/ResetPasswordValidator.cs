using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Auth.Core.Commands;
using UnifiedHub.Core.Resources.Shared;

namespace UnifiedHub.Auth.Validators;

public sealed class ResetPasswordValidator : AbstractValidator<ResetPasswordCommand>
{
    public ResetPasswordValidator(
        IStringLocalizer<SharedResource> l)
    {
        RuleFor(x => x.Token)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_token"]])
            .NotEmpty()
            .WithMessage(l["translate_0_is_required", l["translate_token"]]);

        RuleFor(x => x.NewPassword)
            .NotNull()
            .WithMessage(l["translate_new_password_is_required"])
            .NotEmpty()
            .WithMessage(l["translate_new_password_is_required"])
            .MinimumLength(8)
            .WithMessage(l["translate_password_should_be_at_least_8_characters"]);
    }
}
