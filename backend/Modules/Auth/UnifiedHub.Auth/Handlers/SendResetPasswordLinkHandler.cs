using System.Web;
using MediatR;
using Microsoft.Extensions.Options;
using UnifiedHub.Auth.Core.Commands;
using UnifiedHub.Auth.Persistence;
using UnifiedHub.Auth.Services.ResetPasswordTokenGenerator;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Misc;
using UnifiedHub.Emailing.Commands.SendEmailWithView;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Auth.Handlers;

public sealed class SendResetPasswordLinkHandler(
    IOptions<AppSettings> appSettingsOption,
    IResetPasswordTokenGenerator passwordTokenGenerator,
    IAuthDataSource dataSource,
    ISender mediator) : IRequestHandler<SendResetPasswordLinkCommand>
{
    private readonly AppSettings _appSettings = appSettingsOption.Value;

    public async Task Handle(
        SendResetPasswordLinkCommand command,
        CancellationToken cancellationToken)
    {
        var user = dataSource.Items<User>().SingleOrDefault(x => x.Email == command.Email);

        if (user == null)
        {
            return;
        }

        var token = await passwordTokenGenerator.GenerateAsync(user, cancellationToken);

        await mediator.Send(new SendEmailWithViewCommand
        {
            Subject = new MultilingualString
            {
                Ar = "استعادة كلمة المرور",
                En = "Reset password"
            },
            To = [command.Email],
            View = GetEmailView(token),
        }, cancellationToken);
    }

    private string GetEmailView(string token)
    {
        return $$$"""
                  {% section ContentAr %}

                  {% partial 'Spacing', Value: 50 %}

                  <!-- Title (Ar) -->
                  {% partial 'Heading1', Text: 'مرحباً!', IsRtl: true %}
                  {% partial 'Spacing', Value: 20 %}

                  <!-- Message (Ar) -->
                  {% partial 'Paragraph', Text: 'لقد طلبت استعادة كلمة المرور الخاصة بحسابك. الرجاء الضغط على الرابط في الأسفل لإتمام عملية استعادة كلمة المرور.', IsRtl: true %}
                  {% partial 'Spacing', Value: 50 %}

                  <!-- Button (Ar) -->
                  {% partial 'Button', Url: '{{{_appSettings.AppUrl}}}/auth/reset-password?token={{{HttpUtility.UrlEncode(token)}}}', Text: 'استعادة كلمة المرور' %}
                  {% partial 'Spacing', Value: 100 %}

                  {% endsection %}

                  {% section ContentEn %}

                  {% partial 'Spacing', Value: 50 %}

                  <!-- Title (En) -->
                  {% partial 'Heading1', Text: 'Hello!', IsRtl: false %}
                  {% partial 'Spacing', Value: 20 %}

                  <!-- Message (En) -->
                  {% partial 'Paragraph', Text: 'You have requested to reset your password. Please click on the link below to finish resetting your password.', IsRtl: false %}
                  {% partial 'Spacing', Value: 50 %}

                  <!-- Message (En) -->
                  {% partial 'Button', Url: '{{{_appSettings.AppUrl}}}/auth/reset-password?token={{{HttpUtility.UrlEncode(token)}}}', Text: 'Reset password' %}
                  {% partial 'Spacing', Value: 100 %}

                  {% endsection %}
                  """;
    }
}
