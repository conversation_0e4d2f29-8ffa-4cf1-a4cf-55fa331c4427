using MediatR;
using UnifiedHub.Auth.Services;
using UnifiedHub.RolesManagement.Core.Notifications.Roles;

namespace UnifiedHub.Auth.Handlers;

public sealed class RoleUpdateNotificationHandler(
    IPermissionCache permissionCache) : INotificationHandler<RoleUpdateNotification>
{
    public async Task Handle(RoleUpdateNotification notification, CancellationToken cancellationToken)
    {
        await permissionCache.ClearAsync();
    }
}
