using System.Security.Cryptography;
using MediatR;
using UnifiedHub.Auth.Core.Commands;
using UnifiedHub.Auth.Persistence;
using UnifiedHub.Dtos.Features.Authentication;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Auth.Handlers;

public sealed class GenerateRefreshTokenHandler(
    IAuthDataSource dataSource) : IRequestHandler<GenerateRefreshTokenCommand, RefreshTokenGeneratedDto>
{
    public async Task<RefreshTokenGeneratedDto> Handle(
        GenerateRefreshTokenCommand command,
        CancellationToken cancellationToken)
    {
        var refreshTokenBytes = RandomNumberGenerator.GetBytes(32);
        var hashedRefreshToken = Convert.ToBase64String(SHA256.HashData(refreshTokenBytes));
        var refreshToken = new RefreshToken
        {
            UserId = command.UserId,
            HashedValue = hashedRefreshToken
        };
        await dataSource.AddItemAsync(refreshToken, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);

        return new RefreshTokenGeneratedDto
        {
            Id = refreshToken.Id,
            Value = Convert.ToBase64String(refreshTokenBytes)
        };
    }
}
