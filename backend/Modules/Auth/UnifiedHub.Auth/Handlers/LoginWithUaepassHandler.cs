using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using UnifiedHub.Auth.Core.Commands;
using UnifiedHub.Auth.Core.Utilities;
using UnifiedHub.Auth.Persistence;
using UnifiedHub.Auth.Services.JwtGenerator;
using UnifiedHub.Auth.Services.Uaepass;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Dtos.Features.Identity.Authentication;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Auth.Handlers;

public sealed class LoginWithUaepassHandler(
    IUaepassService uaepassService,
    IJwtGenerator jwtGenerator,
    IStringLocalizer<SharedResource> l,
    ISender mediator,
    IAuthDataSource dataSource)
    : IRequestHandler<LoginWithUaepassCommand, ErrorOr<LoginSuccessDto>>
{
    public async Task<ErrorOr<LoginSuccessDto>> Handle(
        LoginWithUaepassCommand command,
        CancellationToken cancellationToken)
    {
        var result = await uaepassService.GetUserAsync(
            command.AuthorizationCode,
            command.RedirectUrl,
            cancellationToken);

        if (result.IsError)
        {
            return result.Errors.ToErrorOr<LoginSuccessDto>();
        }

        var uaepassUser = result.Value;

        if (string.IsNullOrEmpty(uaepassUser.Idn))
        {
            return Error.Failure(description: l["translate_current_user_does_not_have_a_uae_id_number"]);
        }

        var item = await dataSource.Items<User>()
            .Include(x => x.DepartmentLinks)
            .ThenInclude(x => x.Department)
            .Include(x => x.RoleLinks)
            .ThenInclude(x => x.Role)
            .SingleOrDefaultAsync(x => x.UaeId == uaepassUser.Idn || x.Email == uaepassUser.Email, cancellationToken);

        if (item != null && string.IsNullOrEmpty(item.UaeId))
        {
            return Error.Failure(description: l["translate_another_user_with_the_same_email_address_already_exists"]);
        }

        if (item == null && command.RegisterIfDoesNotExist)
        {
            item = await CreateUserFromUaepassData(uaepassUser, cancellationToken);
        }

        if (item == null)
        {
            return Error.Failure(
                description: l["translate_could_not_find_a_user_associated_with_the_provided_uae_id_number"]);
        }

        if (item.State != User.StateActive)
        {
            return Error.Failure(
                description: l["translate_your_account_is_not_active_please_contact_the_administrator_to_activate_it"]);
        }

        item.UaepassUserId = uaepassUser.Uuid;

        await dataSource.CommitAsync(cancellationToken);

        var refreshToken = await mediator.Send(new GenerateRefreshTokenCommand { UserId = item.Id }, cancellationToken);

        return new LoginSuccessDto
        {
            RefreshToken = refreshToken.Value,
            AccessToken = await jwtGenerator.GenerateAsync(item, refreshToken.Id, cancellationToken)
        };
    }

    private async Task<User> CreateUserFromUaepassData(
        UaepassUser uaepassUser,
        CancellationToken cancellationToken = default)
    {
        var user = new User
        {
            Name = new MultilingualString
            {
                Ar = uaepassUser.FullNameAr,
                En = uaepassUser.FullNameEn
            },
            Email = uaepassUser.Email,
            UaepassUserId = uaepassUser.Uuid,
            State = User.StateActive,
            SecurityStamp = Utils.GenerateSecurityStamp(),
            UaeId = uaepassUser.Idn
        };

        await dataSource.AddItemAsync(user, cancellationToken);

        return user;
    }
}
