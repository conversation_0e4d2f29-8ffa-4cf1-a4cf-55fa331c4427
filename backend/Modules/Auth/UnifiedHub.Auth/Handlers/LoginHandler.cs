using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using UnifiedHub.Auth.Core.Commands;
using UnifiedHub.Auth.Persistence;
using UnifiedHub.Auth.Services.JwtGenerator;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Core.Services;
using UnifiedHub.Dtos.Features.Identity.Authentication;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Auth.Handlers;

public sealed class LoginHandler(
    IPasswordHasher passwordHasher,
    IJwtGenerator jwtGenerator,
    IAuthDataSource dataSource,
    ISender mediator,
    IStringLocalizer<SharedResource> l) : IRequestHandler<LoginCommand, ErrorOr<LoginSuccessDto>>
{
    public async Task<ErrorOr<LoginSuccessDto>> Handle(LoginCommand command, CancellationToken cancellationToken)
    {
        var item = await dataSource
            .Items<User>()
            .Include(x => x.DepartmentLinks)
            .ThenInclude(x => x.Department)
            .Include(x => x.RoleLinks)
            .ThenInclude(x => x.Role)
            .SingleOrDefaultAsync(x => x.Email == command.Email, cancellationToken);

        if (item == null)
        {
            return Error.Failure(description: l["translate_incorrect_email_or_password"]);
        }

        if (item.HashedPassword == null)
        {
            return Error.Failure(description: l["translate_this_user_cannot_be_logged_in_via_email_and_password"]);
        }

        if (!passwordHasher.Verify(command.Password, item.HashedPassword))
        {
            return Error.Failure(description: l["translate_incorrect_email_or_password"]);
        }

        if (item.State != User.StateActive)
        {
            return Error.Failure(
                description: l["translate_your_account_is_not_active_please_contact_the_administrator_to_activate_it"]);
        }

        await dataSource.CommitAsync(cancellationToken);

        var refreshToken = await mediator.Send(new GenerateRefreshTokenCommand { UserId = item.Id }, cancellationToken);

        return new LoginSuccessDto
        {
            RefreshToken = refreshToken.Value,
            AccessToken = await jwtGenerator.GenerateAsync(item, refreshToken.Id, cancellationToken)
        };
    }
}
