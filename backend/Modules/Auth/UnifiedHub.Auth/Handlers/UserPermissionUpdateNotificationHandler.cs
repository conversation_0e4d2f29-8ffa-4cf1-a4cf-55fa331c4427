using MediatR;
using UnifiedHub.Auth.Services;
using UnifiedHub.RolesManagement.Core.Notifications.Roles;

namespace UnifiedHub.Auth.Handlers;

public sealed class UserPermissionUpdateNotificationHandler(
    IPermissionCache cache) : INotificationHandler<UserPermissionUpdateNotification>
{
    public async Task Handle(UserPermissionUpdateNotification notification, CancellationToken cancellationToken)
    {
        await Task.WhenAll(notification.UserIds.Select(cache.ResetAsync));
    }
}
