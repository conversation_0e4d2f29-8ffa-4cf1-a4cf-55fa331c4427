using MediatR;
using UnifiedHub.Auth.Core.Queries;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Features.Identity.Authentication;
using UnifiedHub.Dtos.Modules.DepartmentsManagement.Departments;
using UnifiedHub.Dtos.Modules.UsersManagement.Users;

namespace UnifiedHub.Auth.Handlers;

public sealed class WhoAmIHandler(
    IIdentityService identityService) : IRequestHandler<WhoAmIQuery, WhoAmIDto>
{
    public async Task<WhoAmIDto> Handle(WhoAmIQuery query, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        return new WhoAmIDto
        {
            User = new UserSimpleDto
            {
                Id = identityService.User!.Id,
                Name = identityService.User.Name
            },
            Departments = identityService.Departments!.Select(x => new DepartmentSimpleDto
            {
                Id = x.Id,
                Name = x.Name,
            })
        };
    }
}
