using System.Security.Cryptography;
using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using UnifiedHub.Auth.Core.Queries;
using UnifiedHub.Auth.Persistence;
using UnifiedHub.Auth.Services.JwtGenerator;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Auth.Handlers;

public sealed class RefreshTokenHandler(
    IJwtGenerator jwtGenerator,
    IAuthDataSource dataSource,
    IStringLocalizer<SharedResource> l) : IRequestHandler<RefreshTokenQuery, ErrorOr<string>>
{
    public async Task<ErrorOr<string>> Handle(RefreshTokenQuery query, CancellationToken cancellationToken)
    {
        var hashedRefreshToken = Convert.ToBase64String(SHA256.HashData(Convert.FromBase64String(query.RefreshToken)));
        var item = await dataSource.Items<RefreshToken>()
            .Include(x => x.User)
            .ThenInclude(x => x.DepartmentLinks)
            .ThenInclude(x => x.Department)
            .Include(x => x.User)
            .ThenInclude(x => x.RoleLinks)
            .ThenInclude(x => x.Role)
            .SingleOrDefaultAsync(x => x.HashedValue == hashedRefreshToken, cancellationToken);

        if (item == null)
        {
            return Error.Failure(description: l["translate_you_need_to_login_again"]);
        }

        if (item.User.State != User.StateActive)
        {
            return Error.Failure(
                description: l["translate_your_account_is_not_active_please_contact_the_administrator_to_activate_it"]);
        }

        return (ErrorOr<string>) await jwtGenerator.GenerateAsync(item.User, item.Id, cancellationToken);
    }
}
