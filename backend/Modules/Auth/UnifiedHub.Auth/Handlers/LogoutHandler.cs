using MediatR;
using Microsoft.AspNetCore.Http;
using UnifiedHub.Auth.Core.Commands;
using UnifiedHub.Auth.Persistence;
using UnifiedHub.Model.Entities.Identity;
using IdentityClaimNames = UnifiedHub.Auth.Constants.IdentityClaimNames;

namespace UnifiedHub.Auth.Handlers;

public sealed class LogoutHandler(
    IAuthDataSource dataSource,
    IHttpContextAccessor? httpContextAccessor = null) : IRequestHandler<LogoutCommand>
{
    public async Task Handle(LogoutCommand request, CancellationToken cancellationToken)
    {
        var principal = httpContextAccessor?.HttpContext?.User;

        if (principal == null)
        {
            return;
        }

        var refreshTokenIdStr =
            principal.Claims.SingleOrDefault(x => x.Type == IdentityClaimNames.RefreshTokenId)?.Value;

        if (!Guid.TryParse(refreshTokenIdStr ?? "", out var refreshTokenId))
        {
            return;
        }

        var refreshToken = await dataSource.FindItemAsync<RefreshToken>([refreshTokenId], cancellationToken);

        if (refreshToken == null)
        {
            return;
        }

        await dataSource.RemoveItemAsync(refreshToken, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);
    }
}
