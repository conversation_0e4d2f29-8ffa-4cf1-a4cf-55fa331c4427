using ErrorOr;
using MediatR;
using Microsoft.Extensions.Localization;
using UnifiedHub.Auth.Core.Commands;
using UnifiedHub.Auth.Core.Utilities;
using UnifiedHub.Auth.Persistence;
using UnifiedHub.Auth.Services.ResetPasswordTokenGenerator;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Core.Services;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.Auth.Handlers;

public sealed class ResetPasswordHandler(
    IAuthDataSource dataSource,
    IResetPasswordTokenGenerator passwordTokenGenerator,
    IStringLocalizer<SharedResource> l,
    IPasswordHasher passwordHasher) : IRequestHandler<ResetPasswordCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        ResetPasswordCommand command,
        CancellationToken cancellationToken)
    {
        var userId = await passwordTokenGenerator.GetUserId(command.Token!, cancellationToken);
        if (userId == null)
        {
            return Error.Failure(description: l["translate_invalid_token"]);
        }

        var user = await dataSource.FindItemAsync<User>([userId], cancellationToken);
        if (user == null)
        {
            return Error.Failure(description: l["translate_invalid_token"]);
        }

        if (!await passwordTokenGenerator.VerifyAsync(command.Token!, user, cancellationToken))
        {
            return Error.Failure(description: l["translate_invalid_token"]);
        }

        user.HashedPassword = passwordHasher.Hash(command.NewPassword!);
        user.SecurityStamp = Utils.GenerateSecurityStamp();

        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
