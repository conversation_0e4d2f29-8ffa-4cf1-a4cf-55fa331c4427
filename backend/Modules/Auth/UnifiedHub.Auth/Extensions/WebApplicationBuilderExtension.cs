using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using UnifiedHub.Auth.Persistence;
using UnifiedHub.Auth.Rbac;
using UnifiedHub.Auth.Services;
using UnifiedHub.Auth.Services.JwtGenerator;
using UnifiedHub.Auth.Services.ResetPasswordTokenGenerator;
using UnifiedHub.Auth.Services.Uaepass;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Module.Extensions;
using UnifiedHub.Rbac.Extensions;

namespace UnifiedHub.Auth.Extensions;

public sealed class AuthModuleOptions
{
    public string[]? AllowedEndpointsDuringRequiredPasswordChange { get; set; }
}

public static class WebApplicationBuilderExtension
{
    public static WebApplicationBuilder AddAuthModule(this WebApplicationBuilder builder)
    {
        return builder.AddAuthModule(_ => { });
    }

    public static WebApplicationBuilder AddAuthModule(
        this WebApplicationBuilder builder,
        Action<AuthModuleOptions> configureOptions)
    {
        builder.AddModuleCore<IAuthDataSource, DataContext>(
                typeof(WebApplicationBuilderExtension).Assembly)
            .AddRbacProvider<RbacProvider>()
            .AddUaePass();

        var options = new AuthModuleOptions();
        configureOptions(options);
        builder.Services.AddSingleton(Options.Create(options));

        builder.Services.AddScoped<IPermissionCache, PermissionCache>();
        builder.Services.AddScoped<IJwtGenerator, JwtGenerator>();
        builder.Services.AddScoped<IResetPasswordTokenGenerator, ResetPasswordTokenGenerator>();
        builder.Services.AddScoped<IIdentityService, IdentityService>();

        return builder;
    }


    private static WebApplicationBuilder AddUaePass(this WebApplicationBuilder builder)
    {
        var configuration = builder.Configuration;
        var services = builder.Services;

        var uaepassSettings = new UaepassSettings();
        configuration.Bind(UaepassSettings.SectionName, uaepassSettings);

        services.AddSingleton(Options.Create(uaepassSettings));
        services.AddScoped<IUaepassService, UaepassService>();

        return builder;
    }
}
