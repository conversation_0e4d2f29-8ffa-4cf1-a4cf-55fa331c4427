using UnifiedHub.Rbac.Interfaces;

namespace UnifiedHub.Auth.Core.Rbac;

public static partial class P
{
    public static class Misc
    {
        // This permission allows users to bypass is involved methods
        // and have full visibility over entities attached to departments
        // regardless of the users' attached departments.
        public const string FullDepartmentScope = "auth:full_department_scope";

        public static readonly string[] All = [FullDepartmentScope];
    }
}

public static partial class Roles
{
    public static class Misc
    {
        public static readonly RbacRole[] All = [new("departments_full_scope", [P.Misc.FullDepartmentScope])];
    }
}
