using System.Security.Cryptography;

namespace UnifiedHub.Auth.Core.Utilities;

public static class Utils
{
    public static string GenerateSecurityStamp()
    {
        var randomNumber = RandomNumberGenerator.GetBytes(32);

        // Combine the GUID and random number into a single byte array
        var guid = Guid.NewGuid();
        var guidBytes = guid.ToByteArray();
        var securityStampBytes = new byte[guidBytes.Length + randomNumber.Length];
        Buffer.BlockCopy(guidBytes, 0, securityStampBytes, 0, guidBytes.Length);
        Buffer.BlockCopy(randomNumber, 0, securityStampBytes, guidBytes.Length, randomNumber.Length);

        // Convert the byte array to a Base64 string
        var securityStamp = Convert.ToBase64String(securityStampBytes);
        return securityStamp;
    }
}
