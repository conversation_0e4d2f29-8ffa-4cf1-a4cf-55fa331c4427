using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.SuggestionsManagement.Suggestions;

namespace UnifiedHub.SuggestionsManagement.Core.Queries.Suggestions;

public sealed class GetSuggestionListQuery : IRequest<PaginatedResult<SuggestionListDto>>
{
    public string? Keyword { get; set; }

    public SearchScope Scope { get; set; } = SearchScope.All;

    public string[]? States { get; set; }

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;

    public enum SearchScope
    {
        // Returns all items that the user have access to.
        All,

        // Returns all items that belong to the employee.
        Own
    }
}
