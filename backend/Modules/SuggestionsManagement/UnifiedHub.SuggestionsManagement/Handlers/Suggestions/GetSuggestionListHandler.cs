using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Services;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.Dtos.Modules.SuggestionsManagement.Suggestions;
using UnifiedHub.Model.Entities.SuggestionsManagement;
using UnifiedHub.Persistence.Extensions;
using UnifiedHub.SuggestionsManagement.Core.Queries.Suggestions;
using UnifiedHub.SuggestionsManagement.Extensions;
using UnifiedHub.SuggestionsManagement.Persistence;

namespace UnifiedHub.SuggestionsManagement.Handlers.Suggestions;

public sealed class GetSuggestionListHandler(
    ISuggestionsManagementDataSource dataSource,
    ILanguageService languageService,
    IIdentityService identityService)
    : IRequestHandler<GetSuggestionListQuery, PaginatedResult<SuggestionListDto>>
{
    public async Task<PaginatedResult<SuggestionListDto>> Handle(
        GetSuggestionListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var items = dataSource
            .Items<Suggestion>()
            .AsNoTracking()
            .AsExpandable()
            .Scoped(identityService);

        if (query.Scope == GetSuggestionListQuery.SearchScope.Own)
        {
            var isOwnerExp = E.Suggestions.IsOwner(identityService.User?.Id);
            items = items.Where(x => isOwnerExp.Invoke(x));
        }

        var count = items.Count();

        if (!string.IsNullOrEmpty(query.Keyword))
        {
            items = items.SearchMultilingualString(x => x.Name, query.Keyword);
        }

        if (query.States is { Length: > 0 })
        {
            items = items.Where(x => query.States.Contains(x.State));
        }

        items = items.MultilingualOrderBy(x => x.Name, languageService);

        var filteredCount = items.Count();

        if (query.PageSize != -1)
        {
            items = items
                .Skip(query.PageNumber * query.PageSize)
                .Take(query.PageSize);
        }

        return new PaginatedResult<SuggestionListDto>
        {
            Items = items.Select(SuggestionListDto.Mapper(identityService.User?.Id)),
            Count = count,
            FilteredCount = filteredCount,
        };
    }
}
