using ErrorOr;
using LinqKit;
using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Dtos.Modules.SuggestionsManagement.SuggestionImpactLevels;
using UnifiedHub.Model.Entities.SuggestionsManagement;
using UnifiedHub.SuggestionsManagement.Core.Commands.SuggestionImpactLevels;
using UnifiedHub.SuggestionsManagement.Persistence;

namespace UnifiedHub.SuggestionsManagement.Handlers.SuggestionImpactLevels;

public sealed class CreateOrUpdateSuggestionImpactLevelHandler(
    IMapper mapper,
    ISuggestionsManagementDataSource dataSource)
    : IRequestHandler<CreateSuggestionImpactLevelCommand, ErrorOr<SuggestionImpactLevelGetDto>>,
        IRequestHandler<UpdateSuggestionImpactLevelCommand, ErrorOr<SuggestionImpactLevelGetDto>>
{
    public async Task<ErrorOr<SuggestionImpactLevelGetDto>> Handle(
        CreateSuggestionImpactLevelCommand command,
        CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    public async Task<ErrorOr<SuggestionImpactLevelGetDto>> Handle(
        UpdateSuggestionImpactLevelCommand command,
        CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    private async Task<ErrorOr<SuggestionImpactLevelGetDto>> CreateOrUpdate(
        CreateOrUpdateSuggestionImpactLevelCommand command,
        CancellationToken cancellationToken)
    {
        SuggestionImpactLevel? item = null!;

        if (command is UpdateSuggestionImpactLevelCommand updatedCommand)
        {
            item = dataSource.Items<SuggestionImpactLevel>()
                .SingleOrDefault(x => x.Id == updatedCommand.Id);

            if (item == null)
            {
                return Error.NotFound();
            }

            mapper.Map(updatedCommand, item);
        }

        else if (command is CreateSuggestionImpactLevelCommand createCommand)
        {
            item = new SuggestionImpactLevel();
            mapper.Map(createCommand, item);
            await dataSource.AddItemAsync(item, cancellationToken);
        }

        await dataSource.CommitAsync(cancellationToken);

        return dataSource
            .Items<SuggestionImpactLevel>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => x.Id == item.Id)
            .Select(SuggestionImpactLevelGetDto.Mapper())
            .Single();
    }
}
