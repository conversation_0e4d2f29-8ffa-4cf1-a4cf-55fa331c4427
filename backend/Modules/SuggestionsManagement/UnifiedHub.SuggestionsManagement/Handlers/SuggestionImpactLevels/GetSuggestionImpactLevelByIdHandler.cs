using ErrorOr;
using LinqKit.Core;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Dtos.Modules.SuggestionsManagement.SuggestionImpactLevels;
using UnifiedHub.Model.Entities.SuggestionsManagement;
using UnifiedHub.SuggestionsManagement.Core.Queries.SuggestionImpactLevels;
using UnifiedHub.SuggestionsManagement.Persistence;

namespace UnifiedHub.SuggestionsManagement.Handlers.SuggestionImpactLevels;

public sealed class GetSuggestionImpactLevelByIdHandler(
    ISuggestionsManagementDataSource dataSource)
    : IRequestHandler<GetSuggestionImpactLevelByIdQuery, ErrorOr<SuggestionImpactLevelGetDto>>
{
    public async Task<ErrorOr<SuggestionImpactLevelGetDto>> Handle(
        GetSuggestionImpactLevelByIdQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var item = dataSource
            .Items<SuggestionImpactLevel>()
            .AsNoTracking()
            .AsExpandable()
            .Select(SuggestionImpactLevelGetDto.Mapper())
            .SingleOrDefault(x => x.Id == query.Id);

        return item == null ? Error.NotFound() : (ErrorOr<SuggestionImpactLevelGetDto>) item;
    }
}
