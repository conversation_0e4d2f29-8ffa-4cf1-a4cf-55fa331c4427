using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.SuggestionsManagement.Core.Commands.SuggestionCategories;

namespace UnifiedHub.SuggestionsManagement.Validators.SuggestionCategories;

public sealed class CreateSuggestionCategoryValidator : AbstractValidator<CreateSuggestionCategoryCommand>
{
    public CreateSuggestionCategoryValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public sealed class UpdateSuggestionCategoryValidator : AbstractValidator<UpdateSuggestionCategoryCommand>
{
    public UpdateSuggestionCategoryValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public static class AbstractValidatorApply
{
    public static void Apply<TCommand>(
        this AbstractValidator<TCommand> validator,
        IStringLocalizer<SharedResource> l)
        where TCommand : CreateOrUpdateSuggestionCategoryCommand
    {
        validator.RuleFor(x => x.Name)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_name"]]);
    }
}
