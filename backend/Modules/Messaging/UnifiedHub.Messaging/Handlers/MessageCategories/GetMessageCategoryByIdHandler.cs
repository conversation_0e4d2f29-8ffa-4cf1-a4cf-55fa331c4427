using ErrorOr;
using LinqKit.Core;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Dtos.Modules.Messaging.MessageCategories;
using UnifiedHub.Messaging.Core.Queries.MessageCategories;
using UnifiedHub.Messaging.Persistence;
using UnifiedHub.Model.Entities.Messaging;

namespace UnifiedHub.Messaging.Handlers.MessageCategories;

public sealed class GetMessageCategoryByIdHandler(
    IMessagingDataSource dataSource) : IRequestHandler<GetMessageCategoryByIdQuery, ErrorOr<MessageCategoryGetDto>>
{
    public async Task<ErrorOr<MessageCategoryGetDto>> Handle(
        GetMessageCategoryByIdQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var item = dataSource
            .Items<MessageCategory>()
            .AsNoTracking()
            .AsExpandable()
            .Select(MessageCategoryGetDto.Mapper())
            .SingleOrDefault(x => x.Id == query.Id);

        return item == null ? Error.NotFound() : (ErrorOr<MessageCategoryGetDto>) item;
    }
}
