using MediatR;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Dtos.Misc;
using UnifiedHub.Messaging.Core.Queries.Messages;
using UnifiedHub.Model.Entities.Messaging;

namespace UnifiedHub.Messaging.Handlers.Messages;

public sealed class GetMessageTypeListHandler(
    IStringLocalizer<SharedResource> l) : IRequestHandler<GetMessageTypeListQuery, IEnumerable<ItemDto<string>>>
{
    public async Task<IEnumerable<ItemDto<string>>> <PERSON><PERSON>(
        GetMessageTypeListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        return [
            new() {Id = Message.TypeMessage, Name = l["translate_message_type_message"]},
            new() {Id = Message.TypeCircular, Name = l["translate_message_type_circular"]},
            new() {Id = Message.TypeDirective, Name = l["translate_message_type_directive"]},
            new() {Id = Message.TypeOrder, Name = l["translate_message_type_order"]},
        ];
    }
}