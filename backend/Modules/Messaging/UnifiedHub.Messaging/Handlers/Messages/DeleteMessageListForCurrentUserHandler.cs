using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Messaging.Core.Commands.Messages;
using UnifiedHub.Messaging.Persistence;
using UnifiedHub.Model.Entities.Messaging;

namespace UnifiedHub.Messaging.Handlers.Messages;

public sealed class DeleteMessageListForCurrentUserHandler(
    IMessagingDataSource dataSource,
    IIdentityService identityService) : IRequestHandler<DeleteMessageListForCurrentUserCommand>
{
    public async Task Handle(
        DeleteMessageListForCurrentUserCommand command,
        CancellationToken cancellationToken)
    {
        var userId = identityService.User?.Id;

        await dataSource.Items<MessageEmployeeAccess>()
            .Where(x => command.Ids.Contains(x.MessageId) && x.Employee.UserId == userId)
            .ExecuteDeleteAsync(cancellationToken);
    }
}
