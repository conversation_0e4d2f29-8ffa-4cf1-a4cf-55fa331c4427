using ErrorOr;
using LinqKit;
using MediatR;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.Messaging.Core.Commands.Messages;
using UnifiedHub.Messaging.Persistence;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Entities.Messaging;

namespace UnifiedHub.Messaging.Handlers.Messages;

public sealed class AddEmployeeListToMessageHandler(
    IMessagingDataSource dataSource,
    IStringLocalizer<SharedResource> l,
    IIdentityService identityService) : IRequestHandler<AddEmployeeListToMessageCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        AddEmployeeListToMessageCommand command,
        CancellationToken cancellationToken)
    {
        var userId = identityService.User?.Id ?? Guid.Empty;
        var canWrite = E.Messages.CanWrite(userId);
        var message = dataSource.Items<Message>()
            .AsExpandable()
            .Where(x => canWrite.Invoke(x))
            .SingleOrDefault(x => x.Id == command.MessageId);

        if (message == null)
        {
            return Error.NotFound();
        }

        if (command.ByFilter)
        {
            // Get the IDs of employees already linked to the message with the specified type
            var employeeIdsInMessage = dataSource.Items<Message>()
                .AsExpandable()
                .Where(x => x.Id == command.MessageId)
                // XXX: do not filter by type because type is not included in uniqueness.
                .SelectMany(x => x.EmployeeLinks)
                .Select(link => link.EmployeeId)
                .Distinct();

            // Use NOT IN pattern with Contains to find employees not in the list
            var employees = dataSource.Items<Employee>()
                .AsExpandable()
                .Where(x => !employeeIdsInMessage.Contains(x.Id));

            employees = GetEmployeeListNotInMessageHandler.Filter(employees, x => x, command);
            var links = employees.Select(e => new MessageToEmployeeLink
            {
                MessageId = command.MessageId,
                EmployeeId = e.Id,
                Type = command.Type,
            });
            await dataSource.AddItemsAsync(links, cancellationToken);
        }

        else
        {
            await dataSource.AddItemsAsync(command.EmployeeIds.Select(e =>
                new MessageToEmployeeLink
                {
                    MessageId = command.MessageId,
                    EmployeeId = e,
                    Type = command.Type,
                }), cancellationToken);
        }

        try
        {
            await dataSource.CommitAsync(cancellationToken);
        }
        catch
        {
            return Error.Failure(
                description: l["translate_ensure_that_none_of_the_employees_is_attached_to_the_message"]);
        }

        return Unit.Value;
    }
}
