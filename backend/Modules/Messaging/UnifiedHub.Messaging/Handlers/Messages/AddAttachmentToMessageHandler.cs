using ErrorOr;
using LinqKit;
using MediatR;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Dtos.Features.FileManagement.Files;
using UnifiedHub.EmployeesManagement.Core.Services;
using UnifiedHub.FileManagement.Commands.UploadFile;
using UnifiedHub.Messaging.Core.Commands.Messages;
using UnifiedHub.Messaging.Persistence;
using UnifiedHub.Model.Entities.Messaging;

namespace UnifiedHub.Messaging.Handlers.Messages;

public sealed class AddAttachmentToMessageHandler(
    IMessagingDataSource dataSource,
    IEmployeeIdentityService employeeIdentityService,
    IStringLocalizer<SharedResource> l,
    ISender mediator)
    : IRequestHandler<AddAttachmentToMessageCommand, ErrorOr<FileDto>>
{
    public async Task<ErrorOr<FileDto>> Handle(
        AddAttachmentToMessageCommand command,
        CancellationToken cancellationToken)
    {
        var message = dataSource
            .Items<Message>()
            .SingleOrDefault(x => x.Id == command.MessageId);

        var employeeId = employeeIdentityService.Employee?.Id;

        if (message == null || message.SendingEmployeeId != employeeId)
        {
            return Error.NotFound();
        }

        if (message.SentTime != null)
        {
            return Error.Failure(description: l["translate_cannot_update_a_sent_message"]);
        }

        if (message.State != Message.StateDraft && message.State != Message.StateReturned)
        {
            return Error.Failure(description: l["translate_message_is_not_in_draft_or_returned_state"]);
        }

        if (command.File?.Bytes == null)
        {
            return Error.Failure(description: l["translate_a_file_should_be_provided"]);
        }

        var uploadFileCommand = new UploadFileCommand
        {
            Bytes = command.File.Bytes,
            Name = command.File.Name,
            Description = command.File.Description,
        };
        var uploadResult = await mediator.Send(uploadFileCommand, cancellationToken);
        if (uploadResult.IsError)
        {
            return ErrorOr<FileDto>.From(uploadResult.Errors);
        }

        var item = new MessageAttachment
        {
            MessageId = command.MessageId,
            FileId = uploadResult.Value.Id
        };
        await dataSource.AddItemAsync(item, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);

        return dataSource.Items<MessageAttachment>()
            .AsExpandable()
            .Where(x => x.MessageId == command.MessageId && x.FileId == item.FileId)
            .Select(x => x.File)
            .Select(FileDto.Mapper())
            .Single();
    }
}
