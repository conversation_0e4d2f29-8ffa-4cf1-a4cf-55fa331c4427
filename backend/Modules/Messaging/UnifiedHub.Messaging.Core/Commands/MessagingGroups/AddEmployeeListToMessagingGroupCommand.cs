using ErrorOr;
using MediatR;
using UnifiedHub.Messaging.Core.Queries.MessagingGroups;

namespace UnifiedHub.Messaging.Core.Commands.MessagingGroups;

public sealed class AddEmployeeListToMessagingGroupCommand
    : GetEmployeeListNotInMessagingGroupFilter, IRequest<ErrorOr<Unit>>
{
    public Guid GroupId { get; set; }

    public Guid[] EmployeeIds { get; set; } = [];

    public bool ByFilter { get; set; }
}
