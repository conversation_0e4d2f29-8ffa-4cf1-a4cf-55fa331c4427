using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.EmployeesManagement.Employees;
using UnifiedHub.Model.Entities.Messaging;

namespace UnifiedHub.Messaging.Core.Queries.Messages;

public abstract class GetEmployeeListInMessageFilter
{
    public string? Keyword { get; set; }

    public string? Number { get; set; }

    public string[]? Genders { get; set; }

    public Guid[]? JobClassificationIds { get; set; }

    public Guid[]? JobLevelIds { get; set; }

    public Guid[]? DepartmentIds { get; set; }

    public Guid[]? JobTitleIds { get; set; }
}

public sealed class GetEmployeeListInMessageQuery
    : GetEmployeeListInMessageFilter, IRequest<PaginatedResult<EmployeeBasicDto>>
{
    public Guid MessageId { get; set; }

    public string Type { get; set; } = MessageToEmployeeLink.TypeRecipient;

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;
}
