using ErrorOr;
using MediatR;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.RolesManagement.Core.Commands.Roles;
using UnifiedHub.RolesManagement.Core.Notifications.Roles;
using UnifiedHub.RolesManagement.Persistence;

namespace UnifiedHub.RolesManagement.Handlers.Roles;

public sealed class RemoveUserListFromRoleListHandler(
    IRolesManagementDataSource dataSource,
    IPublisher mediator) : IRequestHandler<RemoveUserListFromRoleListCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        RemoveUserListFromRoleListCommand command,
        CancellationToken cancellationToken)
    {
        var query = dataSource.Items<RoleUserLink>()
            .Where(x => command.RoleIds.Contains(x.RoleId) && command.UserIds.Contains(x.UserId));

        await dataSource.RemoveItemsAsync(query, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);
        
        await mediator.Publish(new UserPermissionUpdateNotification
        {
            UserIds = command.UserIds
        }, cancellationToken);

        return Unit.Value;
    }
}
