using ErrorOr;
using LinqKit.Core;
using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Dtos.Modules.RolesManagement.Roles;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.RolesManagement.Core.Commands.Roles;
using UnifiedHub.RolesManagement.Core.Notifications.Roles;
using UnifiedHub.RolesManagement.Persistence;

namespace UnifiedHub.RolesManagement.Handlers.Roles;

public sealed class CreateOrUpdateRoleHandler(
    IRolesManagementDataSource dataSource,
    IMapper mapper,
    IStringLocalizer<SharedResource> l,
    ISender mediator)
    : IRequestHandler<CreateRoleCommand, ErrorOr<RoleGetDto>>,
        IRequestHandler<UpdateRoleCommand, ErrorOr<RoleGetDto>>
{
    public async Task<ErrorOr<RoleGetDto>> Handle(
        CreateRoleCommand command,
        CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    public async Task<ErrorOr<RoleGetDto>> Handle(
        UpdateRoleCommand command,
        CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    public async Task<ErrorOr<RoleGetDto>> CreateOrUpdate(
        CreateRoleCommand command,
        CancellationToken cancellationToken)
    {
        Role item;

        if (command is UpdateRoleCommand updateCommand)
        {
            var existingItem = await dataSource.FindItemAsync<Role>([updateCommand.Id], cancellationToken);
            if (existingItem == null)
            {
                return Error.NotFound();
            }

            if (!string.IsNullOrEmpty(existingItem.BuiltInId))
            {
                return Error.Failure(description: l["translate_cannot_update_built_in_role"]);
            }

            item = existingItem;
        }
        else
        {
            item = new Role();
            await dataSource.AddItemAsync(item, cancellationToken);
        }

        mapper.Map(command, item);
        await dataSource.CommitAsync(cancellationToken);

        await mediator.Send(new RoleUpdateNotification(), cancellationToken);

        return dataSource.Items<Role>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => x.Id == item.Id)
            .Select(RoleGetDto.Mapper(dataSource.Items<Permission>().ToList()))
            .Single();
    }
}
