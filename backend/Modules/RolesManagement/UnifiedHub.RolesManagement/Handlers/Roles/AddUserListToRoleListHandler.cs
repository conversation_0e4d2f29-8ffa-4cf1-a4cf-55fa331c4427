using ErrorOr;
using MediatR;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.RolesManagement.Core.Commands.Roles;
using UnifiedHub.RolesManagement.Core.Notifications.Roles;
using UnifiedHub.RolesManagement.Persistence;

namespace UnifiedHub.RolesManagement.Handlers.Roles;

public sealed class AddUserListToRoleListHandler(
    IRolesManagementDataSource dataSource,
    IStringLocalizer<SharedResource> l,
    IPublisher mediator) : IRequestHandler<AddUserListToRoleListCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        AddUserListToRoleListCommand command,
        CancellationToken cancellationToken)
    {
        await dataSource.AddItemsAsync(command.RoleIds.SelectMany(r => command.UserIds.Select(u => new RoleUserLink
        {
            RoleId = r,
            UserId = u
        })), cancellationToken);

        try
        {
            await dataSource.CommitAsync(cancellationToken);
            await mediator.Publish(new UserPermissionUpdateNotification
            {
                UserIds = command.UserIds
            }, cancellationToken);
        }
        catch
        {
            return Error.Failure(description: l[
                "translate_ensure_that_all_roles_and_users_exists_and_none_of_the_users_is_attached_to_any_of_the_roles"]);
        }

        return Unit.Value;
    }
}
