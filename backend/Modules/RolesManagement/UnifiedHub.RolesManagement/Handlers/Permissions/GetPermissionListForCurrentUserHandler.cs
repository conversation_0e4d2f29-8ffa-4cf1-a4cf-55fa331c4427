using MediatR;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.RolesManagement.Core.Queries.Permissions;

namespace UnifiedHub.RolesManagement.Handlers.Permissions;

public sealed class GetPermissionListForCurrentUserHandler(
    IIdentityService identityService) : IRequestHandler<GetPermissionListForCurrentUserQuery, string[]>
{
    public async Task<string[]> Handle(
        GetPermissionListForCurrentUserQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;
        return identityService.Permissions;
    }
}
