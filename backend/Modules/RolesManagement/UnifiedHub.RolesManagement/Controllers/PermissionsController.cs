using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Web;
using UnifiedHub.Dtos.Features.Identity.Permissions;
using UnifiedHub.RolesManagement.Core.Queries.Permissions;

namespace UnifiedHub.RolesManagement.Controllers;

[Authorize]
[ApiController]
[Route("/permissions")]
public sealed class PermissionsController(
    ISender mediator) : BaseApiController
{
    /// <summary>
    /// Get a list of all permissions in the system.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<PermissionDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List([FromQuery] GetPermissionListQuery query)
    {
        return Ok(await mediator.Send(query));
    }

    /// <summary>
    /// Get a list of permissions assigned to user.
    /// </summary>
    /// <returns></returns>
    [HttpGet("current")]
    [Produces("application/json")]
    [ProducesResponseType<string[]>(StatusCodes.Status200OK)]
    public async Task<IActionResult> ListCurrent()
    {
        var query = new GetPermissionListForCurrentUserQuery();
        return Ok(await mediator.Send(query));
    }
}
