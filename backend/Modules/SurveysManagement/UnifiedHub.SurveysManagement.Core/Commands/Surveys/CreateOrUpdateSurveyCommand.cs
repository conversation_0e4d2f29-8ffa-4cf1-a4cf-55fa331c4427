using ErrorOr;
using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.SurveysManagement.Surveys;
using UnifiedHub.Model.Entities.SurveysManagement;

namespace UnifiedHub.SurveysManagement.Core.Commands.Surveys;

public sealed class CreateSurveyCommand : CreateOrUpdateSurveyCommand;

public sealed class UpdateSurveyCommand : CreateOrUpdateSurveyCommand
{
    public Guid Id { get; set; }
}

public class CreateOrUpdateSurveyCommand : IRequest<ErrorOr<SurveyGetDto>>
{
    public MultilingualString? Name { get; set; }

    public MultilingualString? Description { get; set; }
    
    public DateTime? From { get; set; }

    public DateTime? To { get; set; }

    public IEnumerable<Guid> UserIds { get; set; } = [];

    public IEnumerable<CreateOrUpdateSurveyQuestion> Questions { get; set; } = [];

    public class CreateOrUpdateSurveyQuestion
    {
        public Guid Id { get; set; }

        public MultilingualString? Name { get; set; }

        public MultilingualString? Description { get; set; }

        public string Type { get; set; } = SurveyQuestion.TypeText;

        public int Order { get; set; }

        public bool IsRequired { get; set; }

        public SurveyQuestion.Option[] Options { get; set; } = [];
    }
}
