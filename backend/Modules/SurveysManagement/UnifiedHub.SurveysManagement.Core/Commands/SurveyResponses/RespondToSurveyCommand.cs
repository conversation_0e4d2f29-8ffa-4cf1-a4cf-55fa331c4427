using ErrorOr;
using MediatR;
using UnifiedHub.Dtos.Modules.SurveysManagement.SurveyResponses;

namespace UnifiedHub.SurveysManagement.Core.Commands.SurveyResponses;

public class RespondToSurveyCommand : IRequest<ErrorOr<SurveyResponseDto>>
{
    public Guid SurveyId { get; set; }

    public IEnumerable<RespondToSurveyAnswer> Answers { get; set; } = [];

    public class RespondToSurveyAnswer
    {
        public Guid QuestionId { get; set; }

        public string? Value { get; set; }
        
        // For checkbox questions that can have multiple values
        public string[] Values { get; set; } = [];
    }
}
