using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Extensions;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.SurveysManagement.Core.Commands.Surveys;

namespace UnifiedHub.SurveysManagement.Validators.Surveys;

public sealed class CreateSurveyValidator : AbstractValidator<CreateSurveyCommand>
{
    public CreateSurveyValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public sealed class UpdateSurveyValidator : AbstractValidator<UpdateSurveyCommand>
{
    public UpdateSurveyValidator(
        IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public static class CreateOrUpdateSurveyValidator
{
    public static void Apply<TCommand>(
        this AbstractValidator<TCommand> validator,
        IStringLocalizer<SharedResource> l)
        where TCommand : CreateOrUpdateSurveyCommand
    {
        validator.RuleFor(x => x.Name)
            .NotEmptyMultilingualString()
            .WithMessage(l["translate_0_is_required", l["translate_name"]]);

        validator.RuleFor(x => x.From)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_from"]])
            .Must((c, from) => from <= c.To)
            .WithMessage(l["translate_from_should_be_less_than_or_equal_to_to"]);

        validator.RuleFor(x => x.To)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_to"]]);

        validator.RuleForEach(x => x.Questions)
            .ChildRules(x =>
                x.RuleFor(y => y.Name).NotEmptyMultilingualString()
                    .WithMessage(l["translate_0_is_required", l["translate_question_name"]]));
    }
}
