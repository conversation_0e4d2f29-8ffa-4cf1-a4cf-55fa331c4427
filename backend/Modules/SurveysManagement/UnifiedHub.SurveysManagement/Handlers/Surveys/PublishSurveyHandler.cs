using ErrorOr;
using LinqKit;
using MediatR;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Model.Entities.SurveysManagement;
using UnifiedHub.SurveysManagement.Core.Commands.Surveys;
using UnifiedHub.SurveysManagement.Persistence;

namespace UnifiedHub.SurveysManagement.Handlers.Surveys;

public sealed class PublishSurveyHandler(
    ISurveysManagementDataSource dataSource,
    IStringLocalizer<SharedResource> l) : IRequestHandler<PublishSurveyCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        PublishSurveyCommand command,
        CancellationToken cancellationToken)
    {
        var item = dataSource
            .Items<Survey>()
            .AsExpandable()
            .SingleOrDefault(x => x.Id == command.Id);

        if (item == null)
        {
            return Error.NotFound();
        }

        if (item.State == Survey.StatePublished)
        {
            return Error.Failure(description: l["translate_survey_is_already_published"]);
        }

        item.State = Survey.StatePublished;
        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
