using ErrorOr;
using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Services;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.Dtos.Modules.SurveysManagement.SurveyResponses;
using UnifiedHub.Model.Entities.SurveysManagement;
using UnifiedHub.SurveysManagement.Core.Commands.SurveyResponses;
using UnifiedHub.SurveysManagement.Persistence;

namespace UnifiedHub.SurveysManagement.Handlers.SurveyResponses;

public sealed class RespondToSurveyHandler(
    ISurveysManagementDataSource dataSource,
    IIdentityService identityService,
    IDateTimeProvider dateTimeProvider)
    : IRequestHandler<RespondToSurveyCommand, ErrorOr<SurveyResponseDto>>
{
    public async Task<ErrorOr<SurveyResponseDto>> Handle(
        RespondToSurveyCommand command,
        CancellationToken cancellationToken)
    {
        var userId = identityService.User?.Id;
        var canBeRespondedExp = E.Surveys.CanBeRespondedToByUser(userId, dateTimeProvider.UtcNow);

        var data = dataSource
            .Items<Survey>()
            .Include(x => x.Questions)
            .Include(x => x.Responses.Where(r => r.UserId == userId))
            .ThenInclude(r => r.Answers)
            .AsExpandable()
            .Where(x => canBeRespondedExp.Invoke(x))
            .Where(x => x.Id == command.SurveyId)
            .Select(x => new
            {
                Survey = x,
                Response = x.Responses.SingleOrDefault(y => y.UserId == userId)
            })
            .SingleOrDefault();


        if (data == null)
        {
            return Error.NotFound();
        }

        var survey = data.Survey;

        var item = data.Response;

        if (item == null)
        {
            item = new SurveyResponse
            {
                Survey = survey,
                UserId = userId!.Value,
                Answers = survey.Questions.Select(x => new SurveyResponseAnswer
                {
                    QuestionId = x.Id
                }).ToList()
            };
            await dataSource.AddItemAsync(item, cancellationToken);
        }
        else
        {
            await Helpers.ReconcileCollections(
                item.Answers,
                item.Survey.Questions.ToList(),
                x => x.QuestionId,
                x => x.Id,
                (src, dst) => dst.QuestionId = src.Id,
                async (x, c) => await dataSource.RemoveItemsAsync(x, c),
                cancellationToken);
        }

        item.Answers.Join(command.Answers, x => x.QuestionId, x => x.QuestionId, (dst, src) =>
                new
                {
                    Dst = dst,
                    Src = src,
                    Question = survey.Questions.First(q => q.Id == src.QuestionId)
                })
            .ToList()
            .ForEach(x => 
            {
                // Handle different question types
                if (x.Question.Type == SurveyQuestion.QuestionType.Checkbox)
                {
                    x.Dst.Values = x.Src.Values;
                    x.Dst.Value = null; // Clear single value for checkbox
                }
                else
                {
                    x.Dst.Value = x.Src.Value;
                    x.Dst.Values = []; // Clear multiple values for non-checkbox
                }
            });

        await dataSource.CommitAsync(cancellationToken);

        return dataSource
            .Items<SurveyResponse>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => x.Id == item.Id)
            .Select(SurveyResponseDto.Mapper())
            .Single();
    }
}
