using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Web;
using UnifiedHub.Dtos.Modules.Payroll.Payslips;
using UnifiedHub.Payroll.Core.Queries.Payslips;

namespace UnifiedHub.Payroll.Controllers;

[Authorize]
[ApiController]
[Route("/payroll/payslips")]
public sealed class PayslipsController(
    ISender mediator) : BaseApiController
{
    /// <summary>
    /// Returns a list of payslips.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<PayslipListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List([FromQuery] GetPayslipListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Returns a payslip given an id.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:guid}")]
    [Produces("application/json")]
    [ProducesResponseType<PayslipListDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Get(Guid id)
    {
        var query = new GetPayslipByIdQuery { Id = id };
        var result = await mediator.Send(query);
        return result.Match(Ok, Problem);
    }
}
