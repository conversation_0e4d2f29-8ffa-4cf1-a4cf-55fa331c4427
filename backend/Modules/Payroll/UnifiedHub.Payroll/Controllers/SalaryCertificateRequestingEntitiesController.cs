using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Web;
using UnifiedHub.Dtos.Modules.Payroll.SalaryCertificateRequestingEntities;
using UnifiedHub.EmployeesManagement.Core.Rbac;
using UnifiedHub.Payroll.Core.Commands.SalaryCertificateRequestingEntities;
using UnifiedHub.Payroll.Core.Queries.SalaryCertificateRequestingEntities;
using UnifiedHub.Rbac.Attributes;

namespace UnifiedHub.Payroll.Controllers;

[Authorize]
[ApiController]
[Route("/payroll/salaries/certificate-requesting-entities")]
public sealed class SalaryCertificateRequestingEntitiesController(
    ISender mediator) : BaseApiController
{
    /// <summary>
    /// Returns a list of certificate requesting entities.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<SalaryCertificateRequestingEntityListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List([FromQuery] GetSalaryCertificateRequestingEntityListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Returns an certificate requesting entity given an id.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:guid}")]
    [Rbac(P.Employees.ManageLists)]
    [Produces("application/json")]
    [ProducesResponseType<SalaryCertificateRequestingEntityGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Get(Guid id)
    {
        var query = new GetSalaryCertificateRequestingEntityByIdQuery { Id = id };
        var result = await mediator.Send(query);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Creates an certificate requesting entity.
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPost]
    [Rbac(P.Employees.ManageLists)]
    [Produces("application/json")]
    [ProducesResponseType<SalaryCertificateRequestingEntityGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Create(CreateSalaryCertificateRequestingEntityCommand command)
    {
        var result = await mediator.Send(command);
        var controllerRoute = RouteData.Values["controller"]!.ToString()!.ToLower();
        return result.Match(item => Created($"/{controllerRoute}/{item.Id}", item), Problem);
    }

    /// <summary>
    /// Updates an certificate requesting entity.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPut("{id:guid}")]
    [Rbac(P.Employees.ManageLists)]
    [ProducesResponseType<SalaryCertificateRequestingEntityGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Update(Guid id, UpdateSalaryCertificateRequestingEntityCommand command)
    {
        command.Id = id;
        var result = await mediator.Send(command);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Deletes an certificate requesting entity.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id:guid}")]
    [Rbac(P.Employees.ManageLists)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Delete(Guid id)
    {
        var command = new DeleteSalaryCertificateRequestingEntityCommand { Id = id };
        var result = await mediator.Send(command);
        return result.Match(_ => NoContent(), Problem);
    }
}
