using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Web;
using UnifiedHub.Dtos.Modules.Payroll.Salaries;
using UnifiedHub.Rbac.Attributes;
using UnifiedHub.Payroll.Core.Commands.Salaries;
using UnifiedHub.Payroll.Core.Queries.Salaries;
using UnifiedHub.Payroll.Core.Rbac;

namespace UnifiedHub.Payroll.Controllers;

[Authorize]
[ApiController]
[Route("/payroll/salaries")]
public sealed class SalariesController(
    ISender mediator) : BaseApiController
{
    /// <summary>
    /// Returns a list of salaries.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet]
    [Rbac(P.Salaries.Read)]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<SalaryListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List([FromQuery] GetSalaryListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Returns a salary given an id.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:guid}")]
    [Rbac(P.Salaries.Read)]
    [Produces("application/json")]
    [ProducesResponseType<SalaryGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Get(Guid id)
    {
        var query = new GetSalaryByIdQuery { Id = id };
        var result = await mediator.Send(query);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Creates a salary.
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPost]
    [Rbac(P.Salaries.Write)]
    [Produces("application/json")]
    [ProducesResponseType<SalaryGetDto>(StatusCodes.Status201Created)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Create(CreateSalaryCommand command)
    {
        var result = await mediator.Send(command);
        var controllerRoute = RouteData.Values["controller"]!.ToString()!.ToLower();
        return result.Match(item => Created($"/{controllerRoute}/{item.Id}", item), Problem);
    }

    /// <summary>
    /// Updates a salary.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPut("{id:guid}")]
    [Rbac(P.Salaries.Write)]
    [ProducesResponseType<SalaryGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Update(Guid id, UpdateSalaryCommand command)
    {
        command.Id = id;
        var result = await mediator.Send(command);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Deletes a salary.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id:guid}")]
    [Rbac(P.Salaries.Delete)]
    [Produces("application/octet-stream")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Delete(Guid id)
    {
        var command = new DeleteSalaryCommand { Id = id };
        var result = await mediator.Send(command);
        return result.Match(_ => NoContent(), Problem);
    }

    /// <summary>
    /// Returns a list of salary change transactions..
    /// </summary>
    /// <param name="id"></param>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet("{id:guid}/change-transactions")]
    [Rbac(P.Salaries.Read)]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<SalaryChangeTransactionDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List(Guid id, [FromQuery] GetSalaryChangeTransactionListQuery query)
    {
        query.SalaryId = id;
        var result = await mediator.Send(query);
        return Ok(result);
    }
}
