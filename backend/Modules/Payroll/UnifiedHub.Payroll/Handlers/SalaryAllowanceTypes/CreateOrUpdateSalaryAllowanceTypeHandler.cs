using ErrorOr;
using LinqKit.Core;
using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Dtos.Modules.Payroll.SalaryAllowanceTypes;
using UnifiedHub.Model.Entities.Payroll;
using UnifiedHub.Payroll.Persistence;
using UnifiedHub.Payroll.Core.Commands.SalaryAllowanceTypes;

namespace UnifiedHub.Payroll.Handlers.SalaryAllowanceTypes;

public sealed class CreateOrUpdateSalaryAllowanceTypeHandler(
    IMapper mapper,
    IPayrollDataSource dataSource)
    : IRequestHandler<CreateSalaryAllowanceTypeCommand, ErrorOr<SalaryAllowanceTypeGetDto>>,
        IRequestHandler<UpdateSalaryAllowanceTypeCommand, ErrorOr<SalaryAllowanceTypeGetDto>>
{
    public async Task<ErrorOr<SalaryAllowanceTypeGetDto>> Handle(
        CreateSalaryAllowanceTypeCommand command,
        CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    public async Task<ErrorOr<SalaryAllowanceTypeGetDto>> Handle(
        UpdateSalaryAllowanceTypeCommand command,
        CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    private async Task<ErrorOr<SalaryAllowanceTypeGetDto>> CreateOrUpdate(
        CreateOrUpdateSalaryAllowanceTypeCommand command,
        CancellationToken cancellationToken)
    {
        SalaryAllowanceType? item;

        if (command is UpdateSalaryAllowanceTypeCommand updatedCommand)
        {
            item = dataSource.Items<SalaryAllowanceType>().SingleOrDefault(x => x.Id == updatedCommand.Id);

            if (item == null)
            {
                return Error.NotFound();
            }

            mapper.Map(updatedCommand, item);
        }
        else
        {
            item = new SalaryAllowanceType();
            mapper.Map(command as CreateSalaryAllowanceTypeCommand, item);
            await dataSource.AddItemAsync(item, cancellationToken);
        }


        await dataSource.CommitAsync(cancellationToken);


        return dataSource
            .Items<SalaryAllowanceType>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => x.Id == item.Id)
            .Select(SalaryAllowanceTypeGetDto.Mapper())
            .Single();
    }
}
