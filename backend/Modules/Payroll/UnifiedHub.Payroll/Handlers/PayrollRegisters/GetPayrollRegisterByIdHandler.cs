using ErrorOr;
using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Modules.Payroll.PayrollRegisters;
using UnifiedHub.Flows.Abstraction;
using UnifiedHub.Model.Entities.Payroll;
using UnifiedHub.Payroll.Persistence;
using UnifiedHub.Payroll.Core.Queries.PayrollRegisters;
using UnifiedHub.Payroll.Core.Rbac;

namespace UnifiedHub.Payroll.Handlers.PayrollRegisters;

public sealed class GetPayrollRegisterByIdHandler(
    IPayrollDataSource dataSource,
    IFlowConfig<PayrollRegister> payrollRegisterFlowConfig,
    IIdentityService identityService)
    : IRequestHandler<GetPayrollRegisterByIdQuery, ErrorOr<PayrollRegisterGetDto>>
{
    public async Task<ErrorOr<PayrollRegisterGetDto>> Handle(
        GetPayrollRegisterByIdQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var userId = identityService.User!.Id;
        var flowExp = payrollRegisterFlowConfig.GetValidTransitionsForUser();

        var item = dataSource
            .Items<PayrollRegister>()
            .AsNoTracking()
            .AsExpandable()
            .Select(PayrollRegisterGetDto.Mapper(
                dataSource.Items<SalaryAllowanceType>(),
                x => flowExp.Invoke(x, userId),
                identityService.Permissions.Contains(P.PayrollRegisters.Delete)))
            .SingleOrDefault(x => x.Id == query.Id);

        return item == null ? Error.NotFound() : (ErrorOr<PayrollRegisterGetDto>) item;
    }
}
