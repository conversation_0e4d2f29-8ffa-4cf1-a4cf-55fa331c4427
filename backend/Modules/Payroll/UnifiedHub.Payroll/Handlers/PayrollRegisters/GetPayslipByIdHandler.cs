using ErrorOr;
using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Core.Services;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Modules.Payroll.Payslips;
using UnifiedHub.Model.Entities.Payroll;
using UnifiedHub.Payroll.Core.Queries.Payslips;
using UnifiedHub.Payroll.Core.Rbac;
using UnifiedHub.Payroll.Persistence;

namespace UnifiedHub.Payroll.Handlers.PayrollRegisters;

public sealed class GetPayslipByIdHandler(
    IPayrollDataSource dataSource,
    IIdentityService identityService,
    IDateTimeProvider dateTimeProvider)
    : IRequestHandler<GetPayslipByIdQuery, ErrorOr<PayslipGetDto>>
{
    public async Task<ErrorOr<PayslipGetDto>> Handle(
        GetPayslipByIdQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var now = DateOnly.FromDateTime(dateTimeProvider.UtcNow);
        var hasPermission = identityService.Permissions.Contains(P.PayrollRegisters.Read);
        var userId = identityService.User?.Id;

        var item = dataSource
            .Items<Payslip>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => hasPermission || x.Salary.Employee.UserId == userId)
            .Where(x => x.Id == query.Id)
            .Select(PayslipGetDto.Mapper(now, hasPermission))
            .SingleOrDefault();

        return item == null ? Error.NotFound() : (ErrorOr<PayslipGetDto>) item;
    }
}
