using MediatR;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Dtos.Misc;
using UnifiedHub.Model.Entities.Payroll;
using UnifiedHub.Payroll.Core.Queries.SalaryCertificates;

namespace UnifiedHub.Payroll.Handlers.SalaryCertificates;

public sealed class GetSalaryCertificateTypeListHandler(
    IStringLocalizer<SharedResource> l)
    : IRequestHandler<GetSalaryCertificateTypeListQuery, IEnumerable<ItemDto<string>>>
{
    public async Task<IEnumerable<ItemDto<string>>> Handle(
        GetSalaryCertificateTypeListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        return new ItemDto<string>[]
        {
            new() { Id = SalaryCertificate.TypeEmployment, Name = l["translate_salary_certificate_type_employment"] },
            new() { Id = SalaryCertificate.TypeSalary, Name = l["translate_salary_certificate_type_salary"] },
            new()
            {
                Id = SalaryCertificate.TypeDetailedSalary, Name = l["translate_salaryCertificate_type_detailed_salary"]
            },
        };
    }
}
