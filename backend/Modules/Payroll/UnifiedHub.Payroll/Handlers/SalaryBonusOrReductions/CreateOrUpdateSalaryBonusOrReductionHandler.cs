using ErrorOr;
using LinqKit.Core;
using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.Payroll.SalaryBonusOrReductions;
using UnifiedHub.Model.Entities.Payroll;
using UnifiedHub.Payroll.Persistence;
using UnifiedHub.Payroll.Core.Commands.SalaryBonusOrReductions;

namespace UnifiedHub.Payroll.Handlers.SalaryBonusOrReductions;

public sealed class CreateOrUpdateSalaryBonusOrReductionHandler(
    IMapper mapper,
    IPayrollDataSource dataSource)
    : IRequestHandler<CreateSalaryBonusOrReductionCommand, ErrorOr<SalaryBonusOrReductionGetDto>>,
        IRequestHandler<UpdateSalaryBonusOrReductionCommand, ErrorOr<SalaryBonusOrReductionGetDto>>
{
    public async Task<ErrorOr<SalaryBonusOrReductionGetDto>> Handle(
        CreateSalaryBonusOrReductionCommand command,
        CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    public async Task<ErrorOr<SalaryBonusOrReductionGetDto>> Handle(
        UpdateSalaryBonusOrReductionCommand command,
        CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    private async Task<ErrorOr<SalaryBonusOrReductionGetDto>> CreateOrUpdate(
        CreateOrUpdateSalaryBonusOrReductionCommand command,
        CancellationToken cancellationToken)
    {
        SalaryBonusOrReduction? item;

        if (command is UpdateSalaryBonusOrReductionCommand updateCommand)
        {
            item = dataSource.Items<SalaryBonusOrReduction>().Include(x => x.Salary)
                .SingleOrDefault(x => x.Id == updateCommand.Id);

            if (item == null)
            {
                return Error.NotFound();
            }

            await AddUpdateTransaction(item, updateCommand, cancellationToken);

            mapper.Map(updateCommand, item);
        }
        else if (command is CreateSalaryBonusOrReductionCommand createCommand)
        {
            item = new SalaryBonusOrReduction
            {
                SalaryId = createCommand.SalaryId
            };
            await dataSource.AddItemAsync(item, cancellationToken);
            mapper.Map(createCommand, item);

            await AddCreateTransaction(item, cancellationToken);
        }
        else
        {
            throw new ArgumentException($"Invalid command type: {command.GetType().Name}");
        }

        await dataSource.CommitAsync(cancellationToken);

        return dataSource
            .Items<SalaryBonusOrReduction>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => x.Id == item.Id)
            .Select(SalaryBonusOrReductionGetDto.Mapper())
            .Single();
    }

    private async Task AddUpdateTransaction(
        SalaryBonusOrReduction item,
        UpdateSalaryBonusOrReductionCommand command,
        CancellationToken cancellationToken)
    {
        var changes = new List<(string Property, object? OldValue, object? NewValue)>();

        if (item.Category != command.Category)
        {
            changes.Add((nameof(SalaryBonusOrReduction.Category), item.Category, command.Category));
        }

        if (item.Amount != command.Amount)
        {
            changes.Add((nameof(SalaryBonusOrReduction.Amount), item.Amount, command.Amount));
        }

        if (item.Year != command.Year)
        {
            changes.Add((nameof(SalaryBonusOrReduction.Year), item.Year, command.Year));
        }

        if (item.Month != command.Month)
        {
            changes.Add((nameof(SalaryBonusOrReduction.Month), item.Month, command.Month));
        }

        if (item.ActionDate != command.ActionDate)
        {
            changes.Add((nameof(SalaryBonusOrReduction.ActionDate), item.ActionDate, command.ActionDate));
        }

        if (changes.Count != 0)
        {
            var transaction = new SalaryChangeTransaction
            {
                SalaryId = item.SalaryId,
                EntityType = SalaryChangeTransaction.EntityTypeAllowance,
                EntityId = item.Id,
                Type = SalaryChangeTransaction.TypeUpdate,
                Snapshot = new()
                {
                    Id = [item.Id],
                    Type = item.GetType().Name,
                    Changes = changes.Select(x => new ObjectChangeSnapshot.ObjectChange
                    {
                        PropertyName = x.Property,
                        OldValue = x.OldValue,
                        NewValue = x.NewValue
                    })
                },
            };
            await dataSource.AddItemAsync(transaction, cancellationToken);
        }
    }

    private async Task AddCreateTransaction(SalaryBonusOrReduction item, CancellationToken cancellationToken)
    {
        var transaction = new SalaryChangeTransaction
        {
            SalaryId = item.SalaryId,
            EntityType = SalaryChangeTransaction.EntityTypeAllowance,
            EntityId = item.Id,
            Type = SalaryChangeTransaction.TypeCreation,
            Snapshot = new()
            {
                Id = [item.Id],
                Type = item.GetType().Name,
                Changes =
                [
                    new()
                    {
                        PropertyName = nameof(SalaryBonusOrReduction.Category),
                        OldValue = null,
                        NewValue = item.Category
                    },

                    new()
                    {
                        PropertyName = nameof(SalaryBonusOrReduction.Amount),
                        OldValue = null,
                        NewValue = item.Amount
                    },

                    new()
                    {
                        PropertyName = nameof(SalaryBonusOrReduction.Year),
                        OldValue = null,
                        NewValue = item.Year
                    },

                    new()
                    {
                        PropertyName = nameof(SalaryBonusOrReduction.Month),
                        OldValue = null,
                        NewValue = item.Month
                    },

                    new()
                    {
                        PropertyName = nameof(SalaryBonusOrReduction.ActionDate),
                        OldValue = null,
                        NewValue = item.ActionDate
                    },
                ]
            },
        };
        await dataSource.AddItemAsync(transaction, cancellationToken);
    }
}
