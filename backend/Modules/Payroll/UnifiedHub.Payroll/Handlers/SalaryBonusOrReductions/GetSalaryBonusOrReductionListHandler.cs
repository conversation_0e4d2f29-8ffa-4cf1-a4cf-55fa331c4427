using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.Payroll.SalaryBonusOrReductions;
using UnifiedHub.Model.Entities.Payroll;
using UnifiedHub.Payroll.Persistence;
using UnifiedHub.Payroll.Core.Queries.SalaryBonusOrReductions;

namespace UnifiedHub.Payroll.Handlers.SalaryBonusOrReductions;

public sealed class GetSalaryBonusOrReductionListHandler(
    IPayrollDataSource dataSource)
    : IRequestHandler<GetSalaryBonusOrReductionListQuery, PaginatedResult<SalaryBonusOrReductionListDto>>
{
    public async Task<PaginatedResult<SalaryBonusOrReductionListDto>> Handle(
        GetSalaryBonusOrReductionListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var items = dataSource
            .Items<SalaryBonusOrReduction>()
            .AsNoTracking()
            .AsExpandable();

        if (
            query.FilterCountExclusions.Contains(GetSalaryBonusOrReductionListQuery.FilterCountExclusion.SalaryIds)
            && query.SalaryIds is { Length: > 0 })
        {
            items = items.Where(x => query.SalaryIds.Contains(x.SalaryId));
        }

        var count = items.Count();

        if (query.SalaryIds is { Length: > 0 })
        {
            items = items.Where(x => query.SalaryIds.Contains(x.SalaryId));
        }

        if (query.EmployeeIds is { Length: > 0 })
        {
            items = items.Where(x => query.EmployeeIds.Contains(x.Salary.EmployeeId));
        }

        if (query.Months is { Length: > 0 })
        {
            items = items.Where(x => query.Months.Contains(x.Month));
        }

        if (query.Years is { Length: > 0 })
        {
            items = items.Where(x => query.Years.Contains(x.Year));
        }

        items = items
            .OrderByDescending(x => x.Year)
            .ThenByDescending(x => x.Month)
            .ThenByDescending(x => x.CreatedTime);

        if (query.PageSize > -1)
        {
            items = items.Skip(query.PageNumber * query.PageSize).Take(query.PageSize);
        }

        return new PaginatedResult<SalaryBonusOrReductionListDto>
        {
            Items = items.Select(SalaryBonusOrReductionListDto.Mapper()).ToList(),
            Count = count,
            FilteredCount = count
        };
    }
}
