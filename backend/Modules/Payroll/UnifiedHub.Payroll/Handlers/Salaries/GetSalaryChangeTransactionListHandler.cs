using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.Payroll.Salaries;
using UnifiedHub.Model.Entities.Payroll;
using UnifiedHub.Payroll.Persistence;
using UnifiedHub.Payroll.Core.Queries.Salaries;

namespace UnifiedHub.Payroll.Handlers.Salaries;

public sealed class GetSalaryChangeTransactionListHandler(
    IPayrollDataSource dataSource)
    : IRequestHandler<GetSalaryChangeTransactionListQuery, PaginatedResult<SalaryChangeTransactionDto>>
{
    public async Task<PaginatedResult<SalaryChangeTransactionDto>> Handle(
        GetSalaryChangeTransactionListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var items = dataSource
            .Items<SalaryChangeTransaction>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => x.SalaryId == query.SalaryId);

        var count = items.Count();

        if (query.From != null)
        {
            items = items.Where(x => query.From <= x.CreatedTime);
        }

        if (query.To != null)
        {
            items = items.Where(x => x.CreatedTime < query.To);
        }

        if (query is { EntityTypes.Length: > 0 })
        {
            items = items.Where(x => query.EntityTypes.Contains(x.EntityType));
        }

        if (query is { Types.Length: > 0 })
        {
            items = items.Where(x => query.Types.Contains(x.Type));
        }

        items = items.OrderByDescending(x => x.CreatedTime);

        var filteredCount = items.Count();

        if (query.PageSize != -1)
        {
            items = items
                .Skip(query.PageNumber * query.PageSize)
                .Take(query.PageSize);
        }

        return new PaginatedResult<SalaryChangeTransactionDto>
        {
            Items = items.Select(SalaryChangeTransactionDto.Mapper()),
            Count = count,
            FilteredCount = filteredCount,
        };
    }
}
