using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Extensions;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Model.Entities.Payroll;
using UnifiedHub.Payroll.Core.Commands.SalaryAllowanceTypes;

namespace UnifiedHub.Payroll.Validators.SalaryAllowanceTypes;

public sealed class CreateSalaryAllowanceTypeValidator : AbstractValidator<CreateSalaryAllowanceTypeCommand>
{
    public CreateSalaryAllowanceTypeValidator(IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public sealed class UpdateSalaryAllowanceTypeValidator : AbstractValidator<UpdateSalaryAllowanceTypeCommand>
{
    public UpdateSalaryAllowanceTypeValidator(IStringLocalizer<SharedResource> l)
    {
        this.Apply(l);
    }
}

public static class CreateOrUpdateSalaryAllowanceTypeValidator
{
    public static void Apply<TCommand>(
        this AbstractValidator<TCommand> validator,
        IStringLocalizer<SharedResource> l)
        where TCommand : CreateOrUpdateSalaryAllowanceTypeCommand
    {
        validator.RuleFor(x => x.Name)
            .NotEmptyMultilingualString()
            .WithMessage(l["translate_0_is_required", l["translate_name"]]);

        validator.RuleFor(x => x.Type)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_type"]])
            .In([SalaryAllowanceType.TypeAllowance, SalaryAllowanceType.TypeCompensation])
            .WithMessage(l["translate_0_is_invalid", l["translate_type"]]);

        validator.RuleFor(x => x.Subtype)
            .NotNull()
            .When(x => x.Type == SalaryAllowanceType.TypeAllowance)
            .WithMessage(l["translate_0_is_required", l["translate_subtype"]])
            .In([
                SalaryAllowanceType.SubtypeChanging,
                SalaryAllowanceType.SubtypeComplementary,
                SalaryAllowanceType.SubtypeBaseSalaryAddition
            ])
            .WithMessage(l["translate_0_is_invalid", l["translate_subtype"]]);

        validator.RuleFor(x => x.DisplayMode)
            .NotNull()
            .WithMessage(l["translate_0_is_required", l["translate_display_mode"]])
            .In([SalaryAllowanceType.DisplayModeAggregated, SalaryAllowanceType.DisplayModeSeparate])
            .WithMessage(l["translate_0_is_invalid", l["translate_display_mode"]]);
    }
}
