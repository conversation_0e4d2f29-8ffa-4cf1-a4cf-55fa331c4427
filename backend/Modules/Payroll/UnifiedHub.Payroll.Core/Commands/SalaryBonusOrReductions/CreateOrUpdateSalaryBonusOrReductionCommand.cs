using ErrorOr;
using MediatR;
using UnifiedHub.Dtos.Modules.Payroll.SalaryBonusOrReductions;

namespace UnifiedHub.Payroll.Core.Commands.SalaryBonusOrReductions;

public sealed class CreateSalaryBonusOrReductionCommand : CreateOrUpdateSalaryBonusOrReductionCommand
{
    public Guid SalaryId { get; set; }
}

public sealed class UpdateSalaryBonusOrReductionCommand : CreateOrUpdateSalaryBonusOrReductionCommand
{
    public Guid Id { get; set; }
}

public abstract class CreateOrUpdateSalaryBonusOrReductionCommand : IRequest<ErrorOr<SalaryBonusOrReductionGetDto>>
{
    public int? Year { get; set; }

    public int? Month { get; set; }

    public string? Category { get; set; }

    public Guid? TypeId { get; set; }

    public decimal? Amount { get; set; }

    public DateOnly? ActionDate { get; set; }

    public string? Notes { get; set; }
}
