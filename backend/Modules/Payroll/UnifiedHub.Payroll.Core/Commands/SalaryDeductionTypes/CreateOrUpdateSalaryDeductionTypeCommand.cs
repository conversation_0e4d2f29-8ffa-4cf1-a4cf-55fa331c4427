using ErrorOr;
using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.Payroll.SalaryDeductionTypes;

namespace UnifiedHub.Payroll.Core.Commands.SalaryDeductionTypes;

public sealed class CreateSalaryDeductionTypeCommand : CreateOrUpdateSalaryDeductionTypeCommand;

public sealed class UpdateSalaryDeductionTypeCommand : CreateOrUpdateSalaryDeductionTypeCommand
{
    public Guid Id { get; set; }
}

public abstract class CreateOrUpdateSalaryDeductionTypeCommand : IRequest<ErrorOr<SalaryDeductionTypeGetDto>>
{
    public MultilingualString? Name { get; set; }

    public MultilingualString? Description { get; set; }

    public string? DisplayMode { get; set; }
}
