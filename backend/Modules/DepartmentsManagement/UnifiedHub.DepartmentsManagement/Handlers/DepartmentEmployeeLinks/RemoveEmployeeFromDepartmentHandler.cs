using ErrorOr;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.DepartmentsManagement.Core.Commands.DepartmentEmployeeLinks;
using UnifiedHub.DepartmentsManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.DepartmentsManagement.Handlers.DepartmentEmployeeLinks;

public class RemoveEmployeeFromDepartmentHandler(
    IDepartmentsManagementDataSource dataSource)
    : IRequestHandler<RemoveEmployeeListFromDepartmentCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(RemoveEmployeeListFromDepartmentCommand command,
        CancellationToken cancellationToken)
    {
        var employees = dataSource
            .Items<Employee>()
            .Include(x => x.DepartmentLinks)
            .Where(x => x.DepartmentLinks.Any(y =>
                y.Type == DepartmentEmployeeLink.TypePrimary &&
                y.DepartmentId == command.DepartmentId))
            .Where(x => command.EmployeeIds.Contains(x.Id))
            .ToList();

        employees.ForEach(e =>
        {
            var link = e.DepartmentLinks.Single(x =>
                x.Type == DepartmentEmployeeLink.TypePrimary && x.DepartmentId == command.DepartmentId);
            e.DepartmentLinks.Remove(link);
        });

        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
