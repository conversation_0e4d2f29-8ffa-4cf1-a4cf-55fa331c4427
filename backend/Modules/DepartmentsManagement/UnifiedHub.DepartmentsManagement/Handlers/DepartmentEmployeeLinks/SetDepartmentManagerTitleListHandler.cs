using ErrorOr;
using MediatR;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.DepartmentsManagement.Core.Commands.DepartmentEmployeeLinks;
using UnifiedHub.DepartmentsManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;

namespace UnifiedHub.DepartmentsManagement.Handlers.DepartmentEmployeeLinks;

public sealed class SetDepartmentManagerTitleListHandler(
    IDepartmentsManagementDataSource dataSource,
    IStringLocalizer<SharedResource> l)
    : IRequestHandler<SetDepartmentManagerTitleListCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        SetDepartmentManagerTitleListCommand command,
        CancellationToken cancellationToken)
    {
        var link = await dataSource.FindItemAsync<DepartmentEmployeeLink>(
            [command.DepartmentId, command.EmployeeId, DepartmentEmployeeLink.TypePrimary],
            cancellationToken);

        if (link == null)
        {
            return Error.Failure(description: l["translate_employee_is_not_in_department"]);
        }

        if (!link.IsManager)
        {
            return Error.Failure(description: l["translate_titles_should_only_be_added_to_managers"]);
        }

        link.Titles = command.Titles;

        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
