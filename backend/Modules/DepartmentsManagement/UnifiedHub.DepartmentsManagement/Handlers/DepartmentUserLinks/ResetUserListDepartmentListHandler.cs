using MediatR;
using UnifiedHub.DepartmentsManagement.Core.Commands.DepartmentUserLinks;
using UnifiedHub.DepartmentsManagement.Persistence;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.DepartmentsManagement.Handlers.DepartmentUserLinks;

public class ResetUserListDepartmentListHandler(
    IDepartmentsManagementDataSource dataSource)
    : IRequestHandler<ResetUserListDepartmentListCommand>
{
    public async Task Handle(ResetUserListDepartmentListCommand command,
        CancellationToken cancellationToken)
    {
        var query = dataSource.Items<DepartmentUserLink>().Where(x => command.UserIds.Contains(x.UserId));

        await dataSource.RemoveItemsAsync(query, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);
    }
}
