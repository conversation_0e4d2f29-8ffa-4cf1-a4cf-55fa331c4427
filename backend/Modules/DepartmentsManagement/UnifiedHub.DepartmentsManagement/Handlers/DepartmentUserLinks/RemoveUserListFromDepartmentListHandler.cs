using ErrorOr;
using MediatR;
using UnifiedHub.DepartmentsManagement.Core.Commands.DepartmentUserLinks;
using UnifiedHub.DepartmentsManagement.Persistence;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.DepartmentsManagement.Handlers.DepartmentUserLinks;

public class RemoveUserListFromDepartmentListHandler(
    IDepartmentsManagementDataSource dataSource)
    : IRequestHandler<RemoveUserListFromDepartmentListCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(RemoveUserListFromDepartmentListCommand command,
        CancellationToken cancellationToken)
    {
        var query = dataSource.Items<DepartmentUserLink>()
            .Where(x => command.DepartmentIds.Contains(x.DepartmentId) && command.UserIds.Contains(x.UserId));

        await dataSource.RemoveItemsAsync(query, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
