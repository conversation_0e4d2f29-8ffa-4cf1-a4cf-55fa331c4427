using ErrorOr;
using MediatR;
using UnifiedHub.DepartmentsManagement.Core.Commands.Departments;
using UnifiedHub.DepartmentsManagement.Persistence;
using UnifiedHub.Model.Entities.Identity;

namespace UnifiedHub.DepartmentsManagement.Handlers.Departments;

public sealed class DeleteDepartmentHandler(
    IDepartmentsManagementDataSource dataSource) : IRequestHandler<DeleteDepartmentCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(DeleteDepartmentCommand command, CancellationToken cancellationToken)
    {
        var item = dataSource.Items<Department>().SingleOrDefault(x => x.Id == command.Id);
        if (item == null)
        {
            return Error.NotFound();
        }

        await dataSource.RemoveItemAsync(item, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
