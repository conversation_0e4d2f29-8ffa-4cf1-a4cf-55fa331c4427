using System.Text.Json.Nodes;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using UnifiedHub.Common.Extensions;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Core.Services;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.LeavesManagement.Core.Rbac;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Entities.LeavesManagement;
using UnifiedHub.Model.Entities.Servicing;
using UnifiedHub.Servicing.Misc;
using UnifiedHub.Servicing.Misc.Enums;
using UnifiedHub.Servicing.Persistence;
using UnifiedHub.Shared.Servicing.Abstraction;

namespace UnifiedHub.LeavesManagement.Servicing;

public sealed class ReturnFromLeaveServiceConfig(
    IServiceProvider serviceProvider,
    IServicingDataSource dataSource,
    IIdentityService identityService,
    ITimezoneProvider timezoneProvider,
    IStringLocalizer<SharedResource> l)
    : SupervisorPermissionServiceConfig(serviceProvider)
{
    public override async Task<ServiceFormItem[]> GetRequestFormItemsAsync(
        ServiceRequest? request,
        Guid? userId,
        JsonNode? data,
        CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;

        userId ??= identityService.User?.Id;
        var employeeId = dataSource.Items<Employee>().SingleOrDefault(x => x.UserId == userId)?.Id;

        var isReturnFromCourse = data?["isReturnFromCourse"]?.GetValueOrDefault<bool>() ?? false;

        DateTime? to;
        Leave? leave = null;

        if (isReturnFromCourse)
        {
            to = data?["courseEndDate"]?.GetValueOrDefault<DateTime>();
        }
        else
        {
            var leaveId = data?["leave"]?["id"]?.GetValueOrDefault<Guid>();
            leave = dataSource.Items<Leave>().SingleOrDefault(x => x.Id == leaveId);
            to = leave?.To;
        }

        var returnDate = data?["returnDate"]?.GetValueOrDefault<DateTime>();
        var timezone = timezoneProvider.Get();

        var absenceDayCount = (returnDate - to)?.TotalDays - 1;

        return
        [
            new()
            {
                Id = "isReturnFromCourse",
                Type = ServiceFormItemType.Boolean,
                Label = new() { Ar = "عودة من دورة", En = "Return from course" },
                RefreshForm = true,
            },

            new()
            {
                Id = "leave",
                Type = ServiceFormItemType.Option,
                Options = dataSource
                    .Items<Leave>()
                    .Where(x => x.EmployeeId == employeeId)
                    .Where(x => x.ServiceRequestLinks.All(y => y.Type != LeaveServiceRequestLink.TypeReturn))
                    .OrderByDescending(x => x.CreatedTime)
                    .Select(x => new { x.Type, x.Id, x.From, x.To })
                    .AsEnumerable()
                    .Select(x => new ServiceFormItem.Option(
                        x.Id.ToString(),
                        new()
                        {
                            Ar =
                                $"{l[$"translate_leave_type_{x.Type}"]}: {x.From.ApplyTimezone(timezone):d} - {x.To.ApplyTimezone(timezone):d}",
                            En =
                                $"{l[$"translate_leave_type_{x.Type}"]}: {x.From.ApplyTimezone(timezone):d} - {x.To.ApplyTimezone(timezone):d}"
                        })),
                Label = new()
                {
                    Ar = "الإجازة", En = "Leave"
                },
                IsDisabled = request?.Transactions.Count > 1,
                IsHidden = isReturnFromCourse,
                IsRequired = !isReturnFromCourse,
                RefreshForm = true,
                Note = leave == null
                    ? null
                    : new()
                    {
                        Ar = $"تاريخ انتهاء الإجازة: {leave.To.ApplyTimezone(timezone):d}",
                        En = $"Leave end date: {leave.To.ApplyTimezone(timezone):d}"
                    }
            },

            new()
            {
                Id = "courseStartDate",
                Label = new() { Ar = "تاريخ بدء الدورة", En = "Course start date" },
                Type = ServiceFormItemType.Date,
                IsRequired = isReturnFromCourse,
                IsHidden = !isReturnFromCourse,
            },

            new()
            {
                Id = "courseEndDate",
                Label = new() { Ar = "تاريخ نهاية الدورة", En = "Course end date" },
                Type = ServiceFormItemType.Date,
                RefreshForm = true,
                IsRequired = isReturnFromCourse,
                IsHidden = !isReturnFromCourse,
            },

            new()
            {
                Id = "returnDate",
                Label = new() { Ar = "تاريخ مباشرة العمل", En = "Return date" },
                Type = ServiceFormItemType.Date,
                RefreshForm = true,
                IsRequired = true,
                Note = absenceDayCount is null or <= 0
                    ? null
                    : new()
                    {
                        Ar = $"عدد أيام الغياب: {absenceDayCount}",
                        En = $"Day count: {absenceDayCount}"
                    }
            },

            new()
            {
                Id = "delayReason",
                Label = new() { Ar = "سبب التأخير", En = "Delay reason" },
                Type = ServiceFormItemType.Option,
                Options = dataSource.Items<LeaveLateReturnReason>()
                    .AsNoTracking()
                    .OrderBy(x => x.Order)
                    .Select(x => new ServiceFormItem.Option(
                        x.Id.ToString(),
                        x.Name)),
                IsRequired = absenceDayCount > 0,
                IsHidden = absenceDayCount is null or <= 0
            },

            new()
            {
                Id = "notes",
                Label = new() { Ar = "الملاحظات", En = "Notes" },
                Type = ServiceFormItemType.LargeText,
            },

            new()
            {
                Id = "attachment",
                Label = new() { Ar = "المرفق", En = "Attachment" },
                Type = ServiceFormItemType.File,
            }
        ];
    }


    protected override string FinalApprovePermission => P.Leaves.FinalApproval;

    public override string ServiceBuiltInId => "services:return_from_leave";

    public override ServiceDefinition ServiceDefinition => new(
        ServiceBuiltInId,
        new() { Ar = "طلب إشعار مباشرة عمل", En = "Return from leave request" },
        Shared.Servicing.ServiceSubcategoryDefinitions.Leaves.Id,
        new()
        {
            Ar = "تساعد هذه الخدمة المستخدمين من تقديم إخطارات العودة من الإجازة في النظام.",
            En = "This service helps users submit their return from leave notifications in the system."
        });

    protected override async Task OnActionInvokedAsync(
        ServiceRequest request,
        ServiceRequestTransaction transaction,
        CancellationToken cancellationToken = default)
    {
        var isReturnFromCourse = request.Data["isReturnFromCourse"]?.GetValueOrDefault<bool>() ?? false;
        if (isReturnFromCourse)
        {
            await base.OnActionInvokedAsync(request, transaction, cancellationToken);
            return;
        }

        var leaveId = request.Data["leave"]?["id"]?.GetValueOrDefault<Guid>();
        var leave = dataSource.Items<Leave>()
            .Include(x => x.ServiceRequestLinks)
            .SingleOrDefault(x => x.Id == leaveId);

        var link = leave?.ServiceRequestLinks.FirstOrDefault(x => x.Type == LeaveServiceRequestLink.TypeReturn);

        if (leave == null || link?.ServiceRequestId == request.Id)
        {
            await base.OnActionInvokedAsync(request, transaction, cancellationToken);
            return;
        }

        if (link == null)
        {
            link = new() { LeaveId = leave.Id, Type = LeaveServiceRequestLink.TypeReturn };
            await dataSource.AddItemAsync(link, cancellationToken);
        }

        link.ServiceRequestId = request.Id;
        await dataSource.CommitAsync(cancellationToken);

        await base.OnActionInvokedAsync(request, transaction, cancellationToken);
    }
}
