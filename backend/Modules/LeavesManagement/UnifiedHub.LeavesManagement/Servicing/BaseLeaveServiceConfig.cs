using System.Linq.Expressions;
using System.Text.Json.Nodes;
using ErrorOr;
using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using UnifiedHub.Common.Extensions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.LeavesManagement.Core.Commands.Leaves;
using UnifiedHub.LeavesManagement.Core.Rbac;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.Model.Entities.LeavesManagement;
using UnifiedHub.Model.Entities.LeavesManagement.ValueObjects;
using UnifiedHub.Model.Entities.Servicing;
using UnifiedHub.Servicing.Abstraction;
using UnifiedHub.Servicing.Misc;
using UnifiedHub.Servicing.Misc.Enums;
using UnifiedHub.Servicing.Persistence;
using UnifiedHub.Shared.Servicing.Abstraction;

namespace UnifiedHub.LeavesManagement.Servicing;

// Inherited by all except: ExamLeave and SickLeave
public abstract class BaseLeaveServiceConfig(
    IServiceProvider serviceProvider)
    : SupervisorTwoPermissionServiceConfig(serviceProvider)
{
    private readonly IStringLocalizer<SharedResource> _l =
        serviceProvider.GetRequiredService<IStringLocalizer<SharedResource>>();

    private readonly IServicingDataSource _dataSource =
        serviceProvider.GetRequiredService<IServicingDataSource>();

    private readonly ISender _mediator = serviceProvider.GetRequiredService<ISender>();

    /// <summary>
    /// Tells the configuration how to obtain the maximum duration per year
    /// from a leave configuration, if null, no restriction will be enforced.
    /// </summary>
    protected virtual Func<LeaveConfig, double?> MaxDurationPerYear => _ => null;

    /// <summary>
    /// Tells the configuration how to obtain the maximum duration for any
    /// given request from a leave configuration, if null, no restriction will be enforced.
    /// </summary>
    protected virtual Func<LeaveConfig, double?> MaxDurationPerRequest => _ => null;

    /// <summary>
    /// Tells the configuration if it should check for balance before submitting the
    /// request.
    /// </summary>
    protected virtual bool ShouldCheckForBalance => false;

    /// <summary>
    /// Some leaves have a fixed balance, e.g., pilgrimage leave always
    /// has 30 days. This can be provided by overriding this property.
    /// </summary>
    protected virtual double? OverridenBalance => null;

    /// <summary>
    /// Tells the configuration the states at which the leave has
    /// been approved.
    /// </summary>
    protected virtual ServiceStateTransitionDefinition[] PositiveTerminalTransitions =>
        [new("*", States.Finalized, Actions.Approve)];

    /// <summary>
    /// Tells the configuration the states at which the leave
    /// has been rejected.
    /// </summary>
    protected virtual ServiceStateTransitionDefinition[] NegativeTerminalTransitions =>
        [new("*", States.Finalized, Actions.Reject)];

    protected virtual ServiceStateTransitionDefinition[] SubmissionTransitions =>
    [
        new("*", States.SupervisorApproval, Actions.SubmitToHr),
        new("*", States.HrApproval, Actions.SubmitToHr)
    ];

    protected virtual ServiceStateTransitionDefinition[] ForwardTransitions =>
    [
        new("*", States.HrApproval, Actions.Approve),
        new("*", States.Finalized, Actions.Approve)
    ];

    public override ServiceStateTransitionDefinition[] StateTransitions
    {
        get
        {
            var transitions = base.StateTransitions;
            var employees = _dataSource.Items<Employee>();
            var users = _dataSource.Items<User>();

            Expression<Func<ServiceRequest, Guid, bool>> hasGeneralManagerPermission = (r, u) =>
                users.First(x => x.Id == u).RoleLinks
                    .Any(x => x.Role.Permissions.Any(y =>
                        y == EmployeesManagement.Core.Rbac.P.Employees.GeneralManagerApprove));

            Expression<Func<ServiceRequest, Guid, bool>> hasHigherHrPermission = (r, u) =>
                users.First(x => x.Id == u).RoleLinks
                    .Any(x => x.Role.Permissions.Any(y => y == SecondPermission));

            Expression<Func<ServiceRequest, bool>> requiresGeneralManager = r =>
                employees.FirstOrDefault(x => x.UserId != null && x.UserId == r.UserId)!.JobLevel!.Order <= 10 &&
                employees.FirstOrDefault(x => x.UserId != null && x.UserId == r.UserId)!.JobClassification!
                    .Type == JobClassification.TypeSworn;

            var hrHeadToFinalize = transitions.Single(x => x is
                { FromState: States.HrHeadApproval, ToState: States.Finalized, Action: Actions.Approve });

            hrHeadToFinalize.Guard = r => !requiresGeneralManager.Invoke(r);

            return
            [
                ..transitions,

                new(States.HrHeadApproval,
                    LeaveStates.GeneralManagerApproval,
                    Actions.Approve,
                    requiresGeneralManager,
                    hasHigherHrPermission
                ),

                new(LeaveStates.GeneralManagerApproval,
                    States.Finalized,
                    Actions.Approve,
                    requiresGeneralManager,
                    hasGeneralManagerPermission
                ),

                new(LeaveStates.GeneralManagerApproval,
                    States.Draft,
                    Actions.Return,
                    requiresGeneralManager,
                    hasGeneralManagerPermission
                ),

                new(LeaveStates.GeneralManagerApproval,
                    States.Finalized,
                    Actions.Reject,
                    requiresGeneralManager,
                    hasGeneralManagerPermission
                )
            ];
        }
    }

    public override ServiceStateDefinition[] ServiceStateDefinitions
    {
        get
        {
            var definitions = base.ServiceStateDefinitions;

            var generalManagerApprovalState = new ServiceStateDefinition(LeaveStates.GeneralManagerApproval,
                new() { Ar = "اعتماد القائد العام", En = "General manager approval" });

            var index = definitions.FindIndex(x => x.Value == States.HrHeadApproval) + 1;

            return definitions
                .Take(index)
                .Concat([generalManagerApprovalState])
                .Concat(definitions.Skip(index))
                .ToArray();
        }
    }

    public override async Task<ServiceStateDefinition[]> GetConditionalServiceStateListAsync(
        ServiceRequest? request,
        Guid? userId,
        JsonNode? data)
    {
        var definitions = await base.GetConditionalServiceStateListAsync(request, userId, data);
        userId ??= request?.UserId;

        var employees = _dataSource.Items<Employee>();

        var requireManagerApproval = employees.Where(x => x.UserId != null && x.UserId == userId)
            .Select(x => new
            {
                RequireManagerApproval =
                    x.JobLevel!.Order <= 10 && x.JobClassification!.Type == JobClassification.TypeSworn,
            })
            .SingleOrDefault()?.RequireManagerApproval ?? false;

        return requireManagerApproval
            ? definitions
            : definitions.Where(x => x.Value != LeaveStates.GeneralManagerApproval).ToArray();
    }

    protected abstract string LeaveType { get; }

    public override string InitialState => States.Draft;


    public override async Task<ServiceFormItem[]> GetRequestFormItemsAsync(
        ServiceRequest? request,
        Guid? userId,
        JsonNode? data,
        CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;

        var from = (data?["from"] as JsonValue)?.GetValueOrDefault<DateTime>();
        var dayCount = (data?["dayCount"] as JsonValue)?.GetValueOrDefault<int?>();
        var to = from == null || dayCount == null ? null : (DateTime?) from.Value.AddDays(dayCount.Value);

        return
        [
            new ServiceFormItem
            {
                Id = "from",
                Type = ServiceFormItemType.Date,
                Label = new MultilingualString { Ar = "تاريخ بداية الإجازة", En = "Leave start date" },
                IsRequired = true
            },

            new ServiceFormItem
            {
                Id = "dayCount",
                Type = ServiceFormItemType.Number,
                Label = new MultilingualString { Ar = "عدد أيام الإجازة", En = "Leave days count" },
                IsRequired = true,
                RefreshForm = true,
                Note = to == null
                    ? null
                    : new()
                    {
                        Ar = $"تنتهي الإجازة بتاريخ {to.Value:d}",
                        En = $"Leaves date ends on {to.Value:d}"
                    }
            },
        ];
    }

    protected override async Task<ErrorOr<int>> ValidateRequestDataAsync(
        ServiceRequest request,
        string action,
        CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask;

        var from = request.Data["from"]!.GetValue<DateTime>();
        var dayCount = request.Data["dayCount"]!.GetValue<double>();

        if (dayCount < 1)
        {
            return Error.Failure(description: _l["translate_leave_days_count_cannot_be_less_than_one"]);
        }

        var data = _dataSource.Items<Employee>()
            .Include(x => x.JobClassification)
            .Include(x => x.JobLevel)
            .Select(x => new
            {
                Employee = x,
                Balance = _dataSource.Items<LeaveBalance>()
                    .SingleOrDefault(y => y.EmployeeId == x.Id && y.Type == LeaveType),
                TakenLeaves = x
                    .Leaves
                    .Where(y => y.Type == LeaveType)
                    .Where(y => y.From.Year == from.Year)
                    .ToList()
            })
            .SingleOrDefault(x => x.Employee.UserId == request.UserId);

        if (data == null)
        {
            return Error.Failure(description: _l["translate_current_user_is_not_attached_to_an_employee"]);
        }

        // Ensure the employee has enough leave balance.
        var balanceResult = await ValidateBalanceAsync(request, data.Balance, dayCount);
        if (balanceResult.IsError)
        {
            return balanceResult;
        }

        var leaveConfigs = new[]
                { data.Employee.JobClassification?.LeaveConfig, data.Employee.JobLevel?.LeaveConfig }
            .Select(x => new
            {
                MaxDurationPerYear = x == null ? double.MaxValue : MaxDurationPerYear(x) ?? double.MaxValue,
                MaxDurationPerRequest = x == null ? double.MaxValue : MaxDurationPerRequest(x) ?? double.MaxValue
            })
            .ToArray();

        // Ensure that the employee has not exceeded the yearly leave limit.
        var allowedMaxDaysPerYear = leaveConfigs.Min(x => x.MaxDurationPerYear);
        var daysTakenThisYear = data.TakenLeaves.Sum(x => (x.To - x.From).TotalDays);
        if (allowedMaxDaysPerYear < daysTakenThisYear + dayCount)
        {
            return Error.Failure(
                description: _l["translate_you_have_already_exceeded_the_number_of_leave_days_for_this_year"]);
        }


        // Ensure that the employee has not exceeded the per-request leave limit.
        var allowedMaxDaysPerRequest = leaveConfigs.Min(x => x.MaxDurationPerRequest);
        if (allowedMaxDaysPerRequest < dayCount)
        {
            return Error.Failure(
                description: _l["translate_you_have_exceeded_the_number_of_leave_days_per_request"]);
        }

        return 0;
    }


    protected virtual async Task<ErrorOr<int>> ValidateBalanceAsync(
        ServiceRequest request,
        LeaveBalance? balance,
        double requestedDays
    )
    {
        await Task.CompletedTask;

        // Ensure the employee has enough leave balance.
        if ((ShouldCheckForBalance && (balance == null || balance.Days < requestedDays)) ||
            OverridenBalance < requestedDays)
        {
            return Error.Failure(description: _l["translate_you_do_not_have_enough_balance"]);
        }

        return 0;
    }

    protected override async Task OnActionInvokingAsync(
        ServiceRequest request,
        ServiceStateTransition transition,
        JsonNode metadata,
        CancellationToken cancellationToken = default)
    {
        var employee = _dataSource.Items<Employee>().SingleOrDefault(x => x.UserId == request.UserId);
        if (employee == null)
        {
            return;
        }

        var from = request.Data["from"]?.GetValueOrDefault<DateTime>();
        var dayCount = request.Data["dayCount"]?.GetValueOrDefault<double>();

        if (from == null || dayCount == null)
        {
            return;
        }

        var isBalanceLocked = metadata["isBalanceLocked"]?.GetValueOrDefault<bool>() ?? false;

        // Lock balance.
        if (transition.FromState == InitialState)
        {
            await LockBalance(employee.Id, dayCount.Value, request.Id, metadata, cancellationToken);
        }

        // Put back the leave balance to the employee.
        if (ShouldCheckForBalance &&
            NegativeTerminalTransitions.Any(x => x.IsMatching(transition)) &&
            isBalanceLocked)
        {
            var result = await _mediator.Send(new AddOrSubtractLeaveDaysFromEmployeeCommand
            {
                EmployeeIdDaysPairs = [new(employee.Id, dayCount.Value)],
                Type = LeaveType,
                Notes = "Releasing balance, leave request was terminated.",
                ServiceRequestId = request.Id
            }, cancellationToken);

            if (!result.IsError)
            {
                metadata["isBalanceLocked"] = false;
            }
        }

        // Create leave.
        if (PositiveTerminalTransitions.Any(x => x.IsMatching(transition)))
        {
            await LockBalance(employee.Id, dayCount.Value, request.Id, metadata, cancellationToken);

            await _mediator.Send(new CreateLeaveForEmployeeCommand
            {
                EmployeeId = employee.Id,
                From = from,
                DayCount = dayCount,
                Type = LeaveType,
                ServiceRequestId = request.Id,
                DeductibleFromBalance = ShouldCheckForBalance
            }, cancellationToken);
        }


        await base.OnActionInvokingAsync(request, transition, metadata, cancellationToken);
    }

    protected override async Task OnCanceledAsync(
        ServiceRequest request,
        CancellationToken cancellationToken = default)
    {
        var isBalanceLocked = request.Metadata["isBalanceLocked"]?.GetValueOrDefault<bool>() ?? false;

        if (!isBalanceLocked)
        {
            return;
        }

        var employee = _dataSource
            .Items<Employee>()
            .Where(x => x.UserId == request.UserId)
            .Select(x => new { x.Id })
            .SingleOrDefault();

        if (employee == null)
        {
            return;
        }

        var dayCount = request.Data["dayCount"]?.GetValueOrDefault<double>();

        if (dayCount == null)
        {
            return;
        }

        await _mediator.Send(new AddOrSubtractLeaveDaysFromEmployeeCommand
        {
            EmployeeIdDaysPairs = [new(employee.Id, dayCount.Value)],
            Type = LeaveType,
            Notes = "Adding upon leave request cancellation.",
            ServiceRequestId = request.Id
        }, cancellationToken);
    }

    protected override string FirstPermission => P.Leaves.FinalApproval;

    protected override string SecondPermission => P.Leaves.HigherApprove;

    private async Task LockBalance(
        Guid employeeId,
        double dayCount,
        Guid requestId,
        JsonNode metadata,
        CancellationToken cancellationToken)
    {
        var isBalanceLocked = metadata["isBalanceLocked"]?.GetValueOrDefault<bool>() ?? false;

        if (!ShouldCheckForBalance || isBalanceLocked)
        {
            return;
        }

        var result = await _mediator.Send(new AddOrSubtractLeaveDaysFromEmployeeCommand
        {
            EmployeeIdDaysPairs = [new(employeeId, -dayCount)],
            Type = LeaveType,
            Notes = "Deducting balance for leave request.",
            ServiceRequestId = requestId,
        }, cancellationToken);

        if (!result.IsError)
        {
            metadata["isBalanceLocked"] = true;
        }
    }

    public static class LeaveStates
    {
        public const string GeneralManagerApproval = "general_manager_approval";
    }
}
