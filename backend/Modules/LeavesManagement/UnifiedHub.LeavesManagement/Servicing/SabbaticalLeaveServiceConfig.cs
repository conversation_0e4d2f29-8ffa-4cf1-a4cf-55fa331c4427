using System.Text.Json.Nodes;
using UnifiedHub.Common.Misc;
using UnifiedHub.Model.Entities.LeavesManagement;
using UnifiedHub.Model.Entities.Servicing;
using UnifiedHub.Servicing.Misc;
using UnifiedHub.Servicing.Misc.Enums;

namespace UnifiedHub.LeavesManagement.Servicing;

public sealed class SabbaticalLeaveServiceConfig(IServiceProvider serviceProvider)
    : BaseLeaveServiceConfig(serviceProvider)
{
    public override string ServiceBuiltInId => "services:sabbatical_leave";

    protected override string LeaveType => Leave.TypeSabbatical;

    public override ServiceDefinition ServiceDefinition => new(ServiceBuiltInId, new()
        {
            Ar = "طلب إجازة تفريغ",
            En = "Sabbatical leave request",
        },
        Shared.Servicing.ServiceSubcategoryDefinitions.Leaves.Id,
        new()
        {
            Ar = "تساعد هذه الخدمة المستخدمين من تقديم طلبات إجازات التفريغ في النظام.",
            En = "This service helps the users apply for their sabbatical leaves on the system."
        });


    public override async Task<ServiceFormItem[]> GetRequestFormItemsAsync(
        ServiceRequest? request,
        Guid? userId,
        JsonNode? data,
        CancellationToken cancellationToken = default)
    {
        return
        [
            ..await base.GetRequestFormItemsAsync(request, userId, data, cancellationToken),

            new()
            {
                Id = "purpose",
                Type = ServiceFormItemType.Text,
                Label = new MultilingualString { Ar = "الغرض من التفرغ", En = "Purpose of sabbatical" },
                IsRequired = true
            },

            new()
            {
                Id = "attachment",
                Type = ServiceFormItemType.File,
                Label = new MultilingualString
                {
                    Ar = "مرفق اثبات الغرض من التفرغ",
                    En = "Proof of purpose of sabbatical"
                },
                IsRequired = true
            },
        ];
    }
}
