using System.Globalization;
using System.Linq.Expressions;
using System.Text.Json.Nodes;
using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Extensions;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Services;
using UnifiedHub.Dtos.Expressions;
using UnifiedHub.Emailing.Commands.SendDefaultEmail;
using UnifiedHub.LeavesManagement.Core.Rbac;
using UnifiedHub.LeavesManagement.Persistence;
using UnifiedHub.Model.Entities.Employment;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.Model.Entities.LeavesManagement;
using UnifiedHub.Model.Entities.Servicing;
using UnifiedHub.Servicing.Abstraction;
using UnifiedHub.Servicing.Misc;
using UnifiedHub.Servicing.Misc.Enums;
using UnifiedHub.Servicing.Persistence;

namespace UnifiedHub.LeavesManagement.Servicing;

public sealed class IddaLeaveServiceConfig(
    IServiceProvider serviceProvider,
    ISender mediator,
    ILeavesManagementDataSource dataSource,
    IServicingDataSource servicingDataSource,
    IDateTimeProvider dateTimeProvider) : BaseLeaveServiceConfig(serviceProvider)
{
    public override string ServiceBuiltInId => "services:idda_leave";

    public static class IddaActions
    {
        public const string Submit = "submit";
        public const string Approve = "approve";
        public const string Return = "return";
        public const string Reject = "reject";
    }

    public static class IddaStates
    {
        public const string Draft = "draft";
        public const string Approval = "approval";
        public const string Finalized = "finalized";
    }

    public override string InitialState => IddaStates.Draft;

    public override ServiceStateTransitionDefinition[] SkipRequestValidationForTransitions =>
    [
        new("*", IddaStates.Draft, IddaActions.Return),
        new("*", IddaStates.Finalized, IddaActions.Reject)
    ];

    public override ServiceStateTransitionDefinition[] StateTransitions
    {
        get
        {
            var users = servicingDataSource.Items<User>();
            Expression<Func<ServiceRequest, Guid, bool>> hasPermissionExp = (r, u) => users.First(x => x.Id == u)
                .RoleLinks
                .Any(x => x.Role.Permissions.Any(y => y == P.Leaves.FinalApproval));

            return
            [
                new(IddaStates.Draft, IddaStates.Approval, IddaActions.Submit, null,
                    (r, u) => r.UserId == u || r.ApplyingUserId == u),

                new(IddaStates.Approval, IddaStates.Finalized, IddaActions.Approve, null, hasPermissionExp),
                new(IddaStates.Approval, IddaStates.Draft, IddaActions.Return, null, hasPermissionExp),
                new(IddaStates.Approval, IddaStates.Finalized, IddaActions.Reject, null, hasPermissionExp),
            ];
        }
    }


    protected override ServiceStateTransitionDefinition[] PositiveTerminalTransitions =>
        [new("*", IddaStates.Finalized, IddaActions.Approve)];

    protected override ServiceStateTransitionDefinition[] NegativeTerminalTransitions =>
        [new("*", IddaStates.Finalized, IddaActions.Reject)];

    protected override ServiceStateTransitionDefinition[] SubmissionTransitions =>
        [new("*", IddaStates.Approval, IddaActions.Submit)];

    protected override ServiceStateTransitionDefinition[] ForwardTransitions =>
        [new("*", IddaStates.Finalized, IddaActions.Approve)];

    public override ServiceDefinition ServiceDefinition => new(
        ServiceBuiltInId, new()
        {
            Ar = "طلب إجازة عدة",
            En = "Idda leave request",
        },
        Shared.Servicing.ServiceSubcategoryDefinitions.Leaves.Id,
        new()
        {
            Ar = "تساعد هذه الخدمة المستخدمات من تقديم طلبات إجازة العدة في النظام.",
            En = "This service helps the female users apply for their Idda leaves on the system."
        });

    protected override string LeaveType => Leave.TypeIdda;

    protected override double? OverridenBalance => 130;

    protected override async Task OnActionInvokedAsync(
        ServiceRequest request,
        ServiceRequestTransaction transaction,
        CancellationToken cancellationToken = default)
    {
        // Notify supervisor.
        if (transaction is { ToState: IddaStates.Finalized, Action: IddaActions.Approve })
        {
            await NotifyManager(request);
        }

        await base.OnActionInvokedAsync(request, transaction, cancellationToken);
    }

    public override async Task<ServiceFormItem[]> GetRequestFormItemsAsync(
        ServiceRequest? request,
        Guid? userId,
        JsonNode? data,
        CancellationToken cancellationToken = default)
    {
        return
        [
            ..await base.GetRequestFormItemsAsync(request, userId, data, cancellationToken),

            new ServiceFormItem
            {
                Id = "dateOfDeath",
                Type = ServiceFormItemType.Date,
                Label = new() { Ar = "تاريخ الوفاة", En = "Date of death" },
                IsRequired = true,
            },

            new ServiceFormItem
            {
                Id = "husbandDeathCertificate",
                Type = ServiceFormItemType.File,
                Label = new() { Ar = "شهادة الوفاة", En = "Death certificate" },
                IsRequired = true
            }
        ];
    }

    public override ServiceStateDefinition[] ServiceStateDefinitions =>
    [
        new ServiceStateDefinition(
            IddaStates.Draft,
            new MultilingualString { Ar = "تقديم الطلب", En = "Request submission" },
            new MultilingualString
            {
                Ar = "على مقدم الطلب ادخال جميع البيانات المطلوبة في الطلب.",
                En = "The applicant has to fill in required fields and submit the request."
            }),

        new ServiceStateDefinition(
            IddaStates.Approval,
            new MultilingualString { Ar = "إجراءات الموارد البشرية", En = "Human resources processing" },
            new MultilingualString
            {
                Ar = "مراجعة واعتماد قسم الموارد البشرية.",
                En = "Human resources review and approval."
            }),

        new ServiceStateDefinition(
            IddaStates.Finalized,
            new MultilingualString { Ar = "إنهاء", En = "Finalize" },
            new MultilingualString
            {
                Ar = "إغلاق الطلب.",
                En = "Request is finalized."
            })
    ];

    private async Task NotifyManager(ServiceRequest request)
    {
        var managerExp = E.Employees.Manager(dateTimeProvider.UtcNow);

        var data = dataSource.Items<Employee>()
            .Include(x => x.User)
            .AsExpandable()
            .Where(x => x.UserId == request.UserId)
            .Select(x => new
            {
                Employee = x,
                ManagerEmails = managerExp.Invoke(x)!
                    .Where(m => m.Employee.User != null)
                    .Select(m => m.Employee.User!.Email)
            })
            .SingleOrDefault();

        if (data == null || !data.ManagerEmails.Any())
        {
            return;
        }

        var emails = data.ManagerEmails.ToArray();
        var employeeName = data.Employee.Name;
        var from = request.Data["from"]?.GetValueOrDefault<DateTime>();
        var dayCount = request.Data["dayCount"]?.GetValueOrDefault<double?>();
        var to = from?.AddDays(dayCount ?? 0);

        await mediator.Send(new SendDefaultEmailCommand
        {
            To = emails,
            Subject = new()
            {
                Ar = "تمت الموافقة على طلب إجازة العدة",
                En = "Idda leave has been approved."
            },
            Body = new()
            {
                Ar =
                    $"لقد تمت الموافقة على طلب {employeeName.Ar} لإجازة العدة من تاريخ {from?.ToString("d", new CultureInfo("ar"))} وحتى تاريخ {to?.ToString("d", new CultureInfo("ar"))}.",
                En =
                    $"An Idda leave request has been approved for {employeeName.En} from {from?.ToString("d", new CultureInfo("en"))} until {to?.ToString("d", new CultureInfo("en"))}."
            }
        });
    }
}
