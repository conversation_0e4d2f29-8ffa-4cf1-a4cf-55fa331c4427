using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.LeavesManagement.LeaveBalances;
using UnifiedHub.Model.Entities.LeavesManagement;

namespace UnifiedHub.LeavesManagement.Core.Queries.LeaveBalances;

public sealed class GetLeaveBalanceTransactionListQuery : IRequest<PaginatedResult<LeaveBalanceTransactionListDto>>
{
    public Guid EmployeeId { get; set; }

    public string Type { get; set; } = Leave.TypeAnnual;

    public DateTime? From { get; set; }

    public DateTime? To { get; set; }

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;
}
