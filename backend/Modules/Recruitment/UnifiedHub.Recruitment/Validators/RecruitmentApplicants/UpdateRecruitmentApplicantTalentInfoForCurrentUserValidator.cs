using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Extensions;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Model.Entities.Recruitment;
using UnifiedHub.Recruitment.Core.Commands.RecruitmentApplicants;

namespace UnifiedHub.Recruitment.Validators.RecruitmentApplicants;

public sealed class UpdateRecruitmentApplicantTalentInfoForCurrentUserValidator
    : AbstractValidator<UpdateRecruitmentApplicantTalentInfoForCurrentUserCommand>
{
    public UpdateRecruitmentApplicantTalentInfoForCurrentUserValidator(
        IStringLocalizer<SharedResource> l)
    {
        RuleForEach(x => x.Talents)
            .ChildRules(x => x.RuleFor(y => y.Category)
                .In([
                    RecruitmentApplicantTalent.CategoryAcademic,
                    RecruitmentApplicantTalent.CategoryInnovative,
                    RecruitmentApplicantTalent.CategoryActive,
                    RecruitmentApplicantTalent.CategoryMental,
                    RecruitmentApplicantTalent.CategoryArt,
                    RecruitmentApplicantTalent.CategoryLeadership,
                    RecruitmentApplicantTalent.CategoryOther,
                ]).WithMessage(l["translate_0_is_invalid", l["translate_category"]]));
    }
}
