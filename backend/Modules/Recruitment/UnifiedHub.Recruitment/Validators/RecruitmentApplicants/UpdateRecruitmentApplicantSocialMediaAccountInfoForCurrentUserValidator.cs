using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Extensions;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Model.Entities.Recruitment;
using UnifiedHub.Recruitment.Core.Commands.RecruitmentApplicants;

namespace UnifiedHub.Recruitment.Validators.RecruitmentApplicants;

public sealed class UpdateRecruitmentApplicantSocialMediaAccountInfoForCurrentUserValidator
    : AbstractValidator<UpdateRecruitmentApplicantSocialMediaAccountInfoForCurrentUserCommand>
{
    public UpdateRecruitmentApplicantSocialMediaAccountInfoForCurrentUserValidator(
        IStringLocalizer<SharedResource> l)
    {
        RuleForEach(x => x.SocialMediaAccounts)
            .ChildRules(x => x.RuleFor(y => y.Type)
                .In([
                    RecruitmentApplicantSocialMediaAccount.TypePinterest,
                    RecruitmentApplicantSocialMediaAccount.TypeReddit,
                    RecruitmentApplicantSocialMediaAccount.TypeInstagram,
                    RecruitmentApplicantSocialMediaAccount.TypeTwitter,
                    RecruitmentApplicantSocialMediaAccount.TypeTiktok,
                    RecruitmentApplicantSocialMediaAccount.TypeSnapchat,
                    RecruitmentApplicantSocialMediaAccount.TypeFacebook,
                    RecruitmentApplicantSocialMediaAccount.TypeLinkedin,
                    RecruitmentApplicantSocialMediaAccount.TypeWhatsapp,
                    RecruitmentApplicantSocialMediaAccount.TypeYoutube,
                ]).WithMessage(l["translate_0_is_invalid", l["translate_type"]]));
    }
}
