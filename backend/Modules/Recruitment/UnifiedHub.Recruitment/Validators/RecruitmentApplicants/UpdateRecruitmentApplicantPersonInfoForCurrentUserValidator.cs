using FluentValidation;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Extensions;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Model.Entities.Recruitment;
using UnifiedHub.Recruitment.Core.Commands.RecruitmentApplicants;

namespace UnifiedHub.Recruitment.Validators.RecruitmentApplicants;

public sealed class UpdateRecruitmentApplicantPersonInfoForCurrentUserValidator
    : AbstractValidator<UpdateRecruitmentApplicantPersonInfoForCurrentUserCommand>
{
    public UpdateRecruitmentApplicantPersonInfoForCurrentUserValidator(
        IStringLocalizer<SharedResource> l)
    {
        RuleFor(x => x.Complexion)
            .In([
                RecruitmentApplicant.ComplexionDark,
                RecruitmentApplicant.ComplexionVeryDark,
                RecruitmentApplicant.ComplexionLight,
                RecruitmentApplicant.ComplexionVeryLight,
                RecruitmentApplicant.ComplexionOlive,
                RecruitmentApplicant.ComplexionMedium,
            ])
            .WithMessage(l["translate_0_is_invalid", l["translate_complexion"]]);

        RuleFor(x => x.EyeColor)
            .In([
                RecruitmentApplicant.EyeColorRed,
                RecruitmentApplicant.EyeColorGreen,
                RecruitmentApplicant.EyeColorBlue,
                RecruitmentApplicant.EyeColorBlack,
                RecruitmentApplicant.EyeColorBrown,
                RecruitmentApplicant.EyeColorGray,
                RecruitmentApplicant.EyeColorHazel,
            ])
            .WithMessage(l["translate_0_is_invalid", l["translate_eye_color"]]);
    }
}
