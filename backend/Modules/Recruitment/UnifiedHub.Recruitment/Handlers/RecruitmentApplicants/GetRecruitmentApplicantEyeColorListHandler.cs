using MediatR;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Dtos.Misc;
using UnifiedHub.Model.Entities.Recruitment;
using UnifiedHub.Recruitment.Core.Queries.RecruitmentApplicants;

namespace UnifiedHub.Recruitment.Handlers.RecruitmentApplicants;

public sealed class GetRecruitmentApplicantEyeColorListHandler(
    IStringLocalizer<SharedResource> l)
    : IRequestHandler<GetRecruitmentApplicantEyeColorListQuery, IEnumerable<ItemDto<string>>>
{
    public async Task<IEnumerable<ItemDto<string>>> Handle(
        GetRecruitmentApplicantEyeColorListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        return
        [
            new()
            {
                Id = RecruitmentApplicant.EyeColorBlack,
                Name = l["translate_recruitment_applicant_eye_color_black"]
            },
            new()
            {
                Id = RecruitmentApplicant.EyeColorBlue,
                Name = l["translate_recruitment_applicant_eye_color_blue"]
            },
            new()
            {
                Id = RecruitmentApplicant.EyeColorBrown,
                Name = l["translate_recruitment_applicant_eye_color_brown"]
            },
            new()
            {
                Id = RecruitmentApplicant.EyeColorGray,
                Name = l["translate_recruitment_applicant_eye_color_gray"]
            },
            new()
            {
                Id = RecruitmentApplicant.EyeColorGreen,
                Name = l["translate_recruitment_applicant_eye_color_green"]
            },
            new()
            {
                Id = RecruitmentApplicant.EyeColorHazel,
                Name = l["translate_recruitment_applicant_eye_color_hazel"]
            },
            new()
            {
                Id = RecruitmentApplicant.EyeColorRed,
                Name = l["translate_recruitment_applicant_eye_color_red"]
            },
        ];
    }
}
