using ErrorOr;
using MapsterMapper;
using MediatR;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Model.Entities.Recruitment;
using UnifiedHub.Recruitment.Core.Commands.RecruitmentApplicants;
using UnifiedHub.Recruitment.Persistence;

namespace UnifiedHub.Recruitment.Handlers.RecruitmentApplicants;

public sealed class UpdateRecruitmentApplicantPersonInfoForCurrentUserHandler(
    IRecruitmentDataSource dataSource,
    IIdentityService identityService,
    IStringLocalizer<SharedResource> l,
    IMapper mapper)
    : IRequestHandler<UpdateRecruitmentApplicantPersonInfoForCurrentUserCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        UpdateRecruitmentApplicantPersonInfoForCurrentUserCommand command,
        CancellationToken cancellationToken)
    {
        var userId = identityService.User?.Id;

        if (userId == null)
        {
            return Error.Failure(description: l["translate_no_user_attached_to_current_scope"]);
        }

        var item = dataSource.Items<RecruitmentApplicant>().SingleOrDefault(x => x.UserId == userId);

        if (item == null)
        {
            return Error.NotFound();
        }

        mapper.Map(command, item);

        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
