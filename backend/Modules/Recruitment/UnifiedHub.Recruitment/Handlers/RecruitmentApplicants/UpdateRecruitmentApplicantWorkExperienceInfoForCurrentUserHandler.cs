using ErrorOr;
using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.FileManagement.Commands.DeleteFile;
using UnifiedHub.FileManagement.Utilities;
using UnifiedHub.Model.Entities.Recruitment;
using UnifiedHub.Recruitment.Core.Commands.RecruitmentApplicants;
using UnifiedHub.Recruitment.Persistence;

namespace UnifiedHub.Recruitment.Handlers.RecruitmentApplicants;

public sealed class UpdateRecruitmentApplicantWorkExperienceInfoForCurrentUserHandler(
    IRecruitmentDataSource dataSource,
    IIdentityService identityService,
    IStringLocalizer<SharedResource> l,
    IMapper mapper,
    ISender mediator)
    : IRequestHandler<UpdateRecruitmentApplicantWorkExperienceInfoForCurrentUserCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        UpdateRecruitmentApplicantWorkExperienceInfoForCurrentUserCommand command,
        CancellationToken cancellationToken)
    {
        var userId = identityService.User?.Id;

        if (userId == null)
        {
            return Error.Failure(description: l["translate_no_user_attached_to_current_scope"]);
        }

        var item = dataSource.Items<RecruitmentApplicant>()
            .Include(x => x.WorkExperiences)
            .SingleOrDefault(x => x.UserId == userId);

        if (item == null)
        {
            return Error.NotFound();
        }

        var danglingWorkExperiences = item.WorkExperiences
            .Where(x => command.WorkExperiences.All(y => y.Id != x.Id))
            .ToList();
        await dataSource.RemoveItemsAsync(danglingWorkExperiences, cancellationToken);
        await dataSource.CommitAsync(cancellationToken);

        foreach (var x in danglingWorkExperiences)
        {
            if (x.ExperienceLetterFileId != null)
            {
                await mediator.Send(new DeleteFileCommand
                {
                    Id = x.ExperienceLetterFileId.Value
                }, cancellationToken);
            }

            if (x.NoObjectionCertificateFileId != null)
            {
                await mediator.Send(new DeleteFileCommand
                {
                    Id = x.NoObjectionCertificateFileId.Value
                }, cancellationToken);
            }
        }

        mapper.Map(command, item);

        foreach (var x in command.WorkExperiences)
        {
            var y = item.WorkExperiences.SingleOrDefault(z => z.Id == x.Id);
            if (y == null)
            {
                y = new() { ApplicantId = item.Id, };
                mapper.Map(x, y);
                await dataSource.AddItemAsync(y, cancellationToken);
            }
            else
            {
                mapper.Map(x, y);
            }


            y.ExperienceLetterFileId = await Utils.UploadFile(
                x.ExperienceLetterFile,
                y.ExperienceLetterFileId,
                mediator,
                mapper,
                cancellationToken);

            y.NoObjectionCertificateFileId = await Utils.UploadFile(
                x.NoObjectionCertificateFile,
                y.NoObjectionCertificateFileId,
                mediator,
                mapper,
                cancellationToken);
        }

        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
