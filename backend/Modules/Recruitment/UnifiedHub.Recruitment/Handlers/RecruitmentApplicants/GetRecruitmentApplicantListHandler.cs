using LinqKit.Core;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Services;
using UnifiedHub.Dtos.Modules.Recruitment.RecruitmentApplicants;
using UnifiedHub.Model.Entities.Recruitment;
using UnifiedHub.Persistence.Extensions;
using UnifiedHub.Recruitment.Core.Queries.RecruitmentApplicants;
using UnifiedHub.Recruitment.Persistence;

namespace UnifiedHub.Recruitment.Handlers.RecruitmentApplicants;

public sealed class GetRecruitmentApplicantListHandler(
    IRecruitmentDataSource dataSource,
    IDateTimeProvider dateTimeProvider)
    : IRequestHandler<GetRecruitmentApplicantListQuery, PaginatedResult<RecruitmentApplicantListDto>>
{
    public async Task<PaginatedResult<RecruitmentApplicantListDto>> Handle(
        GetRecruitmentApplicantListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var items = dataSource
            .Items<RecruitmentApplicant>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => x.Applications.Any());

        var count = items.Count();

        if (!string.IsNullOrEmpty(query.Keyword))
        {
            items = items.SearchMultilingualString(x => x.Name, query.Keyword);
        }

        if (!string.IsNullOrEmpty(query.Email))
        {
            query.Email = query.Email!.ToLower().Trim();
            items = items.Where(x => x.Email == query.Email);
        }

        if (query.Genders is { Length: > 0 })
        {
            items = items.Where(x => query.Genders.Contains(x.Gender));
        }

        if (query.NationalityIds is { Length: > 0 })
        {
            items = items.Where(x => x.NationalityId != null && query.NationalityIds.Contains(x.NationalityId.Value));
        }

        if (query.EducationalQualificationIds is { Length: > 0 })
        {
            items = items.Where(x => x.EducationalQualifications
                .Any(y => y.QualificationId != null &&
                          query.EducationalQualificationIds.Contains(y.QualificationId.Value)));
        }

        var now = dateTimeProvider.UtcNow;
        if (query.AgeFrom != null)
        {
            items = items.Where(x => x.DateOfBirth != null && query.AgeFrom <= now.Year - x.DateOfBirth.Value.Year);
        }

        if (query.AgeTo != null)
        {
            items = items.Where(x => x.DateOfBirth != null && now.Year - x.DateOfBirth.Value.Year < query.AgeTo);
        }

        var filteredCount = items.Count();

        items = items.OrderByDescending(x => x.CreatedTime);

        if (query.PageSize != -1)
        {
            items = items
                .Skip(query.PageNumber * query.PageSize)
                .Take(query.PageSize);
        }

        return new PaginatedResult<RecruitmentApplicantListDto>
        {
            Items = items.Select(RecruitmentApplicantListDto.Mapper()),
            Count = count,
            FilteredCount = filteredCount,
        };
    }
}
