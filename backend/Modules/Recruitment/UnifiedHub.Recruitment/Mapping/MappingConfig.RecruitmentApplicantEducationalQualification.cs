using Mapster;
using UnifiedHub.Model.Entities.Recruitment;
using UnifiedHub.Recruitment.Core.Commands.RecruitmentApplicants;

namespace UnifiedHub.Recruitment.Mapping;

public static partial class MappingConfig
{
    private static void ApplyRecruitmentApplicantEducationalQualificationConfig()
    {
        var mappingConfig = TypeAdapterConfig.GlobalSettings;

        mappingConfig
            .ForType<UpdateRecruitmentApplicantEducationalQualificationInfoForCurrentUserCommand.
                EducationalQualification,
                RecruitmentApplicantEducationalQualification>()
            .Ignore(x => x.CertificateFile!, x => x.CertificateFileId!);
    }
}
