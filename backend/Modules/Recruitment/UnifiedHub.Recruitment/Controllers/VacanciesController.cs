using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Web;
using UnifiedHub.Dtos.Modules.Recruitment.Vacancies;
using UnifiedHub.Dtos.Modules.Recruitment.VacancyApplications;
using UnifiedHub.Rbac.Attributes;
using UnifiedHub.Recruitment.Core.Commands.Vacancies;
using UnifiedHub.Recruitment.Core.Queries.Vacancies;
using UnifiedHub.Recruitment.Core.Rbac;

namespace UnifiedHub.Recruitment.Controllers;

[Authorize]
[ApiController]
[Route("/recruitment/vacancies")]
public sealed class VacanciesController(
    ISender mediator) : BaseApiController
{
    /// <summary>
    /// Returns a list of vacancies.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet]
    [Rbac(P.Vacancies.Read)]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<VacancyListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> List([FromQuery] GetVacancyListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Returns a vacancy given an id.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:guid}")]
    [Rbac(P.Vacancies.Read)]
    [Produces("application/json")]
    [ProducesResponseType<VacancyGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Get(Guid id)
    {
        var query = new GetVacancyByIdQuery { Id = id };
        var result = await mediator.Send(query);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Creates a vacancy.
    /// </summary>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPost]
    [Rbac(P.Vacancies.Write)]
    [Produces("application/json")]
    [ProducesResponseType<VacancyGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Create(CreateVacancyCommand command)
    {
        var result = await mediator.Send(command);
        var controllerRoute = RouteData.Values["controller"]!.ToString()!.ToLower();
        return result.Match(item => Created($"/{controllerRoute}/{item.Id}", item), Problem);
    }

    /// <summary>
    /// Updates a vacancy.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="command"></param>
    /// <returns></returns>
    [HttpPut("{id:guid}")]
    [Rbac(P.Vacancies.Write)]
    [ProducesResponseType<VacancyGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Update(Guid id, UpdateVacancyCommand command)
    {
        command.Id = id;
        var result = await mediator.Send(command);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Deletes a vacancy.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id:guid}")]
    [Rbac(P.Vacancies.Delete)]
    [Produces("application/octet-stream")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Delete(Guid id)
    {
        var command = new DeleteVacancyCommand { Id = id };
        var result = await mediator.Send(command);
        return result.Match(_ => NoContent(), Problem);
    }

    /// <summary>
    /// Returns a list of active vacancies.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet("active")]
    [AllowAnonymous]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<VacancyActiveListDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> ListActive([FromQuery] GetActiveVacancyListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Returns an active vacancy given an id.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("active/{id:guid}")]
    [AllowAnonymous]
    [Produces("application/json")]
    [ProducesResponseType<VacancyActiveGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetActive(Guid id)
    {
        var query = new GetActiveVacancyByIdQuery { Id = id };
        var result = await mediator.Send(query);
        return result.Match(Ok, Problem);
    }

    [HttpPost("{id:guid}/apply")]
    [Produces("application/json")]
    [ProducesResponseType<VacancyApplicationGetDto>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType<ValidationProblemDetails>(StatusCodes.Status400BadRequest)]
    [ProducesResponseType<ProblemDetails>(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Apply(Guid id)
    {
        var command = new ApplyForVacancyCommand { Id = id };
        var result = await mediator.Send(command);
        return result.Match(Ok, Problem);
    }

    /// <summary>
    /// Returns a list of vacancies.
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpGet("simple")]
    [Produces("application/json")]
    [ProducesResponseType<PaginatedResult<VacancySimpleDto>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> SimpleList([FromQuery] GetVacancySimpleListQuery query)
    {
        var result = await mediator.Send(query);
        return Ok(result);
    }
}
