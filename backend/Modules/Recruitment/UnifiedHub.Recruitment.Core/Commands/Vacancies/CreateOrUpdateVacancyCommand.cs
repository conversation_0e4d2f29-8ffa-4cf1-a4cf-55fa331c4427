using ErrorOr;
using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.Recruitment.Vacancies;

namespace UnifiedHub.Recruitment.Core.Commands.Vacancies;

public sealed class CreateVacancyCommand : CreateOrUpdateVacancyCommand;

public sealed class UpdateVacancyCommand : CreateOrUpdateVacancyCommand
{
    public Guid Id { get; set; }
}

public abstract class CreateOrUpdateVacancyCommand : IRequest<ErrorOr<VacancyGetDto>>
{
    public MultilingualString? Name { get; set; }

    public MultilingualString? Description { get; set; }

    public MultilingualString[]? Conditions { get; set; }

    public int? ReapplicationWindowInDays { get; set; }

    public int? ReturnedApplicationEditWindowInDays { get; set; }

    public Guid? JobClassificationId { get; set; }

    public bool IsPublished { get; set; }

    public DateOnly? From { get; set; }

    public DateOnly? To { get; set; }

    public int? MaxApplicationCount { get; set; }

    public int? MaxApplicationsPerDayCount { get; set; }

    public string[] Genders { get; set; } = [];

    public int? AgeFrom { get; set; }

    public int? AgeTo { get; set; }

    public IEnumerable<Guid> NationalityIds { get; set; } = [];

    public IEnumerable<Guid> EducationalQualificationIds { get; set; } = [];
}
