using ErrorOr;
using MediatR;

namespace UnifiedHub.Recruitment.Core.Commands.RecruitmentApplicants;

public sealed class UpdateRecruitmentApplicantTalentInfoForCurrentUserCommand : IRequest<ErrorOr<Unit>>
{
    public IEnumerable<Talent> Talents { get; set; } = [];

    public class Talent
    {
        public string? Name { get; set; }

        public Guid? TalentId { get; set; }

        public string? Category { get; set; }
    }
}
