using ErrorOr;
using MediatR;

namespace UnifiedHub.Recruitment.Core.Commands.RecruitmentApplicants;

public sealed class UpdateRecruitmentApplicantBankInfoForCurrentUserCommand : IRequest<ErrorOr<Unit>>
{
    public bool HasBankAccount { get; set; }

    public string? BankName { get; set; }

    public string? BankBranchName { get; set; }

    public string? BankAccountNumber { get; set; }

    public string? BankIban { get; set; }

    public Guid? BankCountryId { get; set; }

    public string? BankAddress { get; set; }

    public string? BankNotes { get; set; }
}
