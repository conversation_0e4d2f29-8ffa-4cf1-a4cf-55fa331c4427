using ErrorOr;
using MediatR;

namespace UnifiedHub.Recruitment.Core.Commands.RecruitmentApplicants;

public sealed class UpdateRecruitmentApplicantVehicleInfoForCurrentUserCommand : IRequest<ErrorOr<Unit>>
{
    public bool HasVehicle { get; set; }

    public IEnumerable<Vehicle> Vehicles { get; set; } = [];

    public class Vehicle
    {
        public string? Name { get; set; }

        public int? Year { get; set; }

        public string? PlateNumber { get; set; }

        public string? IssueAuthority { get; set; }

        public string? Color { get; set; }

        public string? PlateCategory { get; set; }

        public string? TrafficNumber { get; set; }

        public string? Notes { get; set; }
    }
}
