using MediatR;
using UnifiedHub.Common.Misc;
using UnifiedHub.Dtos.Modules.RolesManagement.Roles;

namespace UnifiedHub.UsersManagement.Core.Queries.Users;

public sealed class GetRoleListForUserQuery : IRequest<PaginatedResult<RoleSimpleDto>>
{
    public Guid Id { get; set; }

    public string? Keyword { get; set; }

    public int PageNumber { get; set; } = 0;

    public int PageSize { get; set; } = -1;
}
