using ErrorOr;
using MediatR;
using UnifiedHub.Auth.Persistence;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.UsersManagement.Core.Commands.Users;

namespace UnifiedHub.UsersManagement.Handlers.Users;

public sealed class ActivateUserHandler(
    IAuthDataSource dataSource) : IRequestHandler<ActivateUserCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        ActivateUserCommand command,
        CancellationToken cancellationToken)
    {
        var item = await dataSource.FindItemAsync<User>([command.UserId], cancellationToken);

        if (item == null)
        {
            return Error.NotFound();
        }

        item.State = User.StateActive;

        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
