using ErrorOr;
using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Dtos.Modules.UsersManagement.Users;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.UsersManagement.Core.Queries.Users;
using UnifiedHub.UsersManagement.Persistence;

namespace UnifiedHub.UsersManagement.Handlers.Users;

public sealed class GetCurrentUserHandler(
    IUsersManagementDataSource dataSource,
    IIdentityService identityService,
    IStringLocalizer<SharedResource> l)
    : IRequestHandler<GetCurrentUserQuery, ErrorOr<UserGetDto>>
{
    public async Task<ErrorOr<UserGetDto>> Handle(GetCurrentUserQuery request, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var userId = identityService.User?.Id;
        var user = dataSource
            .Items<User>()
            .AsNoTracking()
            .AsExpandable()
            .Select(UserGetDto.Mapper())
            .SingleOrDefault(x => x.Id == userId);

        return user == null
            ? Error.NotFound(description: l["translate_could_not_find_user"])
            : (ErrorOr<UserGetDto>) user;
    }
}
