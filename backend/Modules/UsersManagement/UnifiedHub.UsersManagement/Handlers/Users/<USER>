using LinqKit.Core;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Services;
using UnifiedHub.Dtos.Modules.RolesManagement.Roles;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.Persistence.Extensions;
using UnifiedHub.UsersManagement.Core.Queries.Users;
using UnifiedHub.UsersManagement.Persistence;

namespace UnifiedHub.UsersManagement.Handlers.Users;

public sealed class GetRoleListNotForUserHandler(
    IUsersManagementDataSource dataSource,
    ILanguageService languageService) :
    IRequestHandler<GetRoleListNotForUserQuery, PaginatedResult<RoleListDto>>
{
    public async Task<PaginatedResult<RoleListDto>> Handle(
        GetRoleListNotForUserQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var items = dataSource.Items<Role>()
            .AsNoTracking()
            .AsExpandable()
            .Where(x => x.UserLinks.All(y => y.UserId != query.Id));

        var count = items.Count();

        items = items.SearchMultilingualString(x => x.Name, query.Keyword)
            .MultilingualOrderBy(x => x.Name, languageService);

        var filteredCount = items.Count();

        if (query.PageSize != -1)
        {
            items = items
                .Skip(query.PageNumber * query.PageSize)
                .Take(query.PageSize);
        }

        return new PaginatedResult<RoleListDto>
        {
            Items = items.Select(RoleListDto.Mapper()),
            Count = count,
            FilteredCount = filteredCount
        };
    }
}
