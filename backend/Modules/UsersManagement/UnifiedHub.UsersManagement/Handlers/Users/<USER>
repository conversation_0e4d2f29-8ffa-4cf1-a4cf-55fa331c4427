using ErrorOr;
using MediatR;
using Microsoft.Extensions.Localization;
using UnifiedHub.Auth.Core.Utilities;
using UnifiedHub.Core.Resources.Shared;
using UnifiedHub.Core.Services;
using UnifiedHub.Core.Services.IdentityService;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.UsersManagement.Core.Commands.Users;
using UnifiedHub.UsersManagement.Persistence;

namespace UnifiedHub.UsersManagement.Handlers.Users;

public sealed class ChangeCurrentUserPasswordHandler(
    IUsersManagementDataSource dataSource,
    IIdentityService identityService,
    IStringLocalizer<SharedResource> l,
    IPasswordHasher passwordHasher) :
    IRequestHandler<ChangeCurrentUserPasswordCommand, ErrorOr<Unit>>
{
    public async Task<ErrorOr<Unit>> Handle(
        ChangeCurrentUserPasswordCommand command,
        CancellationToken cancellationToken)
    {
        var userId = identityService.User?.Id;
        var user = await dataSource.FindItemAsync<User>([userId], cancellationToken);

        if (user == null)
        {
            return Error.NotFound(description: l["translate_could_not_find_user"]);
        }

        if (!string.IsNullOrEmpty(user.HashedPassword) &&
            !passwordHasher.Verify(command.OldPassword!, user.HashedPassword))
        {
            return Error.Failure(description: l["translate_old_password_is_wrong"]);
        }

        if (!string.IsNullOrEmpty(user.HashedPassword) &&
            passwordHasher.Verify(command.NewPassword!, user.HashedPassword))
        {
            return Error.Failure(description: l["translate_new_password_is_the_same_as_old"]);
        }

        user.HashedPassword = passwordHasher.Hash(command.NewPassword!);
        user.IsPasswordChangeRequired = false;
        user.SecurityStamp = Utils.GenerateSecurityStamp();

        // Remove all refresh tokens for the user to force re-login
        await dataSource.RemoveItemsAsync(
            dataSource.Items<RefreshToken>().Where(x => x.UserId == userId), cancellationToken);

        await dataSource.CommitAsync(cancellationToken);

        return Unit.Value;
    }
}
