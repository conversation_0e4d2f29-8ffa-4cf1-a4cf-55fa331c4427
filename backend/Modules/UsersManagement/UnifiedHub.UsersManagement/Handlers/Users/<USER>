using LinqKit.Core;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Common.Misc;
using UnifiedHub.Core.Services;
using UnifiedHub.Dtos.Modules.UsersManagement.Users;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.Persistence.Extensions;
using UnifiedHub.UsersManagement.Core.Queries.Users;
using UnifiedHub.UsersManagement.Persistence;

namespace UnifiedHub.UsersManagement.Handlers.Users;

public sealed class GetUserListHandler(
    IUsersManagementDataSource dataSource,
    ILanguageService languageService) : IRequestHandler<GetUserListQuery, PaginatedResult<UserListDto>>
{
    public async Task<PaginatedResult<UserListDto>> Handle(GetUserListQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;

        var items = dataSource.Items<User>()
            .AsNoTracking()
            .AsExpandable();

        var count = items.Count();

        if (!string.IsNullOrEmpty(query.Keyword))
        {
            items = items.SearchMultilingualString(x => x.Name, query.Keyword);
        }

        if (!string.IsNullOrEmpty(query.Email))
        {
            items = items.Where(x => x.Email == query.Email.Trim().ToLower());
        }

        var filteredCount = items.Count();

        items = items.MultilingualOrderBy(x => x.Name, languageService);

        if (query.PageSize != -1)
        {
            items = items
                .Skip(query.PageNumber * query.PageSize)
                .Take(query.PageSize);
        }

        return new PaginatedResult<UserListDto>
        {
            Items = items
                .Select(UserListDto.Mapper())
                .ToList(),
            Count = count,
            FilteredCount = filteredCount
        };
    }
}
