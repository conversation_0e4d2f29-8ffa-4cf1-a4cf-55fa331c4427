using ErrorOr;
using MapsterMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using UnifiedHub.Auth.Core.Utilities;
using UnifiedHub.Core.Services;
using UnifiedHub.Dtos.Modules.UsersManagement.Users;
using UnifiedHub.Model.Entities.Identity;
using UnifiedHub.UsersManagement.Core.Commands.Users;
using UnifiedHub.UsersManagement.Core.Queries.Users;
using UnifiedHub.UsersManagement.Persistence;

namespace UnifiedHub.UsersManagement.Handlers.Users;

public sealed class CreateOrUpdateUserHandler(
    IMapper mapper,
    ISender mediator,
    IPasswordHasher passwordHasher,
    IUsersManagementDataSource dataSource) :
    IRequestHandler<CreateUserCommand, ErrorOr<UserGetDto>>,
    IRequestHandler<UpdateUserCommand, ErrorOr<UserGetDto>>
{
    public async Task<ErrorOr<UserGetDto>> Handle(CreateUserCommand command, CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    public async Task<ErrorOr<UserGetDto>> Handle(UpdateUserCommand command, CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    private async Task<ErrorOr<UserGetDto>> CreateOrUpdate(
        CreateOrUpdateUserCommand command,
        CancellationToken cancellationToken)
    {
        command.UaeId = command.UaeId?.ToLower().Trim();

        User? item = null;

        if (command is CreateUserCommand createCommand)
        {
            createCommand.Email = createCommand.Email!.ToLower().Trim();

            item = new User();
            mapper.Map(createCommand, item);
            item.State = User.StateActive;
            await dataSource.AddItemAsync(item, cancellationToken);
        }
        else if (command is UpdateUserCommand updateCommand)
        {
            item = dataSource.Items<User>()
                .Include(x => x.DepartmentLinks)
                .SingleOrDefault(x => x.Id == updateCommand.Id);

            if (item == null)
            {
                return Error.NotFound();
            }

            mapper.Map(updateCommand, item);
        }


        if (!string.IsNullOrEmpty(command.Password))
        {
            item!.IsPasswordChangeRequired = true;
            item.HashedPassword = passwordHasher.Hash(command.Password!);
        }

        item!.SecurityStamp = Utils.GenerateSecurityStamp();
        item.UaeId = string.IsNullOrEmpty(command.UaeId) ? null : command.UaeId;

        // Upload photo:
        item.PhotoFileId = await FileManagement.Utilities.Utils.UploadFile(
            command.PhotoFile,
            item.PhotoFileId,
            mediator,
            mapper,
            cancellationToken);

        await dataSource.CommitAsync(cancellationToken);

        return await mediator.Send(new GetUserByIdQuery { Id = item.Id }, cancellationToken);
    }
}
