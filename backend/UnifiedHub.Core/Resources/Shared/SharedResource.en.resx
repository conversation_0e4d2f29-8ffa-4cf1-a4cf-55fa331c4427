<root>

  <data name="translate_type">
    <value>Type</value>
  </data>

  <data name="translate_schedule_record_type_on_duty">
    <value>On duty</value>
  </data>

  <data name="translate_schedule_record_type_off_duty">
    <value>Off duty</value>
  </data>

  <data name="translate_gender_male">
    <value>Male</value>
  </data>

  <data name="translate_gender_female">
    <value>Female</value>
  </data>

  <data name="translate_incorrect_email_or_password">
    <value>Incorrect email or password.</value>
  </data>

  <data name="translate_this_user_cannot_be_logged_in_via_email_and_password">
    <value>This user cannot be logged in via email and password.</value>
  </data>

  <data name="translate_user_already_in_department">
    <value>User already in department.</value>
  </data>

  <data name="translate_cannot_delete_yourself">
    <value>Cannot delete yourself.</value>
  </data>

  <data name="translate_failed_to_fetch_uae_pass_user_details">
    <value>Failed to fetch UAE pass user details.</value>
  </data>

  <data name="translate_failed_to_authorize_vai_the_provided_authorization_code">
    <value>Failed to authorize via the provided authorization code.</value>
  </data>

  <data name="translate_current_user_does_not_have_a_uae_id_number">
    <value>Current user does not have a UAE ID number.</value>
  </data>

  <data name="translate_could_not_find_a_user_associated_with_the_provided_uae_id_number">
    <value>Could not find a user associated with the provided UAE ID number.</value>
  </data>

  <data name="translate_email_is_already_used_by_another_employee">
    <value>Email is already used by another employee.</value>
  </data>

  <data name="translate_employee_number_is_already_taken">
    <value>Employee number is already taken.</value>
  </data>

  <data name="translate_from_cannot_be_in_the_past">
    <value>From cannot be in the past.</value>
  </data>

  <data name="translate_leave_days_count_cannot_be_less_than_one">
    <value>Leave days count cannot be less than one.</value>
  </data>

  <data name="translate_current_user_is_not_attached_to_an_employee">
    <value>Current user is not attached to an employee.</value>
  </data>

  <data name="translate_you_do_not_have_enough_balance">
    <value>You do not have enough balance.</value>
  </data>

  <data name="translate_you_have_already_exceeded_the_number_of_leave_days_for_this_year">
    <value>You have already exceeded the number of leave days for this year.</value>
  </data>

  <data name="translate_you_have_exceeded_the_number_of_leave_days_per_request">
    <value>You have exceeded the number of leave days per request.</value>
  </data>

  <data name="translate_one_or_more_validation_error_has_occurred">
    <value>One or more validation error has occurred.</value>
  </data>

  <data name="translate_name_is_required">
    <value>Name is required.</value>
  </data>

  <data name="translate_could_not_find_parent_department">
    <value>Could not find parent department.</value>
  </data>

  <data name="translate_the_new_department_is_a_child_of_the_current_department">
    <value>The new department is a child of the current department.</value>
  </data>

  <data name="translate_could_not_find_the_department">
    <value>Could not find the department.</value>
  </data>

  <data name="translate_could_not_find_the_user">
    <value>Could not find the user.</value>
  </data>

  <data name="translate_email_is_required">
    <value>Email is required.</value>
  </data>

  <data name="translate_invalid_email_address">
    <value>Invalid email address.</value>
  </data>

  <data name="translate_email_is_already_taken">
    <value>Email is already taken.</value>
  </data>

  <data name="translate_password_is_required">
    <value>Password is required.</value>
  </data>

  <data name="translate_password_should_be_at_least_8_characters">
    <value>Password should be at least 8 characters.</value>
  </data>

  <data name="translate_leave_type_annual">
    <value>Annual</value>
  </data>

  <data name="translate_leave_type_time_in_lieu">
    <value>TimeInLieu</value>
  </data>

  <data name="translate_leave_type_pilgrimage">
    <value>Pilgrimage</value>
  </data>

  <data name="translate_leave_type_exam">
    <value>Exam</value>
  </data>

  <data name="translate_leave_type_unpaid">
    <value>Unpaid</value>
  </data>

  <data name="translate_leave_type_guardian">
    <value>Guardian</value>
  </data>

  <data name="translate_leave_type_sabbatical">
    <value>Sabbatical</value>
  </data>

  <data name="translate_leave_type_mourning">
    <value>Mourning</value>
  </data>

  <data name="translate_leave_type_maternity">
    <value>Maternity</value>
  </data>

  <data name="translate_leave_type_sick">
    <value>Sick</value>
  </data>

  <data name="translate_leave_type_father">
    <value>Father</value>
  </data>

  <data name="translate_leave_type_casual">
    <value>Casual</value>
  </data>

  <data name="translate_leave_type_idda">
    <value>Idda</value>
  </data>

  <data name="translate_leave_type_national_service">
    <value>National service</value>
  </data>

  <data name="translate_leave_type_study">
    <value>Study</value>
  </data>

  <data name="translate_leave_type_graduation">
    <value>Graduation</value>
  </data>

  <data name="translate_punch_type_in">
    <value>In</value>
  </data>

  <data name="translate_punch_type_out">
    <value>Out</value>
  </data>

  <data name="translate_salary_bonus_or_deduction_category_bonus">
    <value>Bonus</value>
  </data>

  <data name="translate_salary_bonus_or_deduction_category_reduction">
    <value>Reduction</value>
  </data>

  <data name="translate_salary_bonus_or_deduction_subtype_other">
    <value>Other</value>
  </data>

  <data name="translate_employee_child_allowance_status_active">
    <value>Active</value>
  </data>

  <data name="translate_employee_child_allowance_status_inactive">
    <value>Inactive</value>
  </data>

  <data name="translate_employee_educational_qualification_grade_excellent">
    <value>Excellent</value>
  </data>

  <data name="translate_employee_educational_qualification_grade_very_good">
    <value>Very good</value>
  </data>

  <data name="translate_employee_educational_qualification_grade_good">
    <value>Good</value>
  </data>

  <data name="translate_employee_educational_qualification_grade_pass">
    <value>Pass</value>
  </data>

  <data name="translate_attendance_permission_type_forgetfulness">
    <value>Forgetfulness</value>
  </data>

  <data name="translate_attendance_permission_type_late_in">
    <value>Late in</value>
  </data>

  <data name="translate_attendance_permission_type_early_out">
    <value>Early out</value>
  </data>

  <data name="translate_salary_certificate_type_employment">
    <value>Employment</value>
  </data>

  <data name="translate_salary_certificate_type_salary">
    <value>Salary</value>
  </data>

  <data name="translate_salaryCertificate_type_detailed_salary">
    <value>Detailed salary</value>
  </data>

  <data name="translate_salary_certificate_language_arabic">
    <value>Arabic</value>
  </data>

  <data name="translate_salary_certificate_language_english">
    <value>English</value>
  </data>

  <data name="translate_employee_state_active">
    <value>Active</value>
  </data>

  <data name="translate_employee_state_inactive">
    <value>Inactive</value>
  </data>

  <data name="translate_employee_state_suspended">
    <value>Suspended</value>
  </data>

  <data name="translate_employee_event_type_nationality_change">
    <value>Nationality change</value>
  </data>

  <data name="translate_employee_event_type_job_level_change">
    <value>Job level change</value>
  </data>

  <data name="translate_employee_event_type_termination">
    <value>Termination</value>
  </data>

  <data name="translate_employee_event_type_old_transfer_change">
    <value>Old transfer change</value>
  </data>

  <data name="translate_employee_event_type_name_change">
    <value>Name change</value>
  </data>

  <data name="translate_employee_event_type_suspension">
    <value>Suspension</value>
  </data>

  <data name="translate_employee_event_type_activation">
    <value>Activation</value>
  </data>

  <data name="translate_employee_event_type_unknown">
    <value>Unknown</value>
  </data>

  <data name="translate_invalid_relation_type">
    <value>Invalid relation type</value>
  </data>

  <data name="translate_starting_date_of_the_leave_should_be_the_same_as_date_of_death">
    <value>Starting date of the leave should be the same as date of death.</value>
  </data>

  <data name="translate_you_are_allowed_to_take_8_days_for_offshore_first_and_second_degree_relatives">
    <value>You are allowed to take 8 days for offshore first and second degree relatives.</value>
  </data>

  <data name="translate_you_are_allowed_to_take_5_days_for_onshore_first_and_second_degree_relatives">
    <value>You are allowed to take 5 days for onshore first and second degree relatives.</value>
  </data>

  <data name="translate_you_are_allowed_to_take_5_days_for_offshore_third_degree_relatives">
    <value>You are allowed to take 5 days for offshore third degree relatives.</value>
  </data>

  <data name="translate_you_are_allowed_to_take_3_days_for_onshore_third_degree_relatives">
    <value>You are allowed to take 3 days for onshore third degree relatives.</value>
  </data>

  <data name="translate_only_female_employees_can_apply_for_this_leave">
    <value>Only female employees can apply for this leave.</value>
  </data>

  <data name="translate_only_male_employees_can_apply_for_this_leave">
    <value>Only male employees can apply for this leave.</value>
  </data>

  <data name="translate_you_should_attach_the_medical_report_attachment_if_the_leave_is_more_than_2_days">
    <value>You should attach the medical report if the leave is more than 2 days.</value>
  </data>

  <data name="translate_leave_starting_date_should_be_within_one_month_of_date_of_birth">
    <value>Leave starting date should be within one month of the date of birth.</value>
  </data>

  <data name="translate_your_job_classification_is_not_applicable_for_this_leave">
    <value>Your job classification is not applicable for this leave.</value>
  </data>

  <data name="translate_to_should_be_larger_than_from">
    <value>To should be larger than from.</value>
  </data>

  <data name="translate_you_cannot_apply_for_more_than_0_hours">
    <value>You cannot apply for more than {0} hours.</value>
  </data>

  <data name="translate_you_have_used_all_your_allowable_hour_count_for_this_month">
    <value>You have used all you allowable hour count for this month.</value>
  </data>

  <data name="translate_you_can_no_longer_apply_for_forgetfulness_permission_for_this_month">
    <value>You can no longer apply for forgetfulness permission for this month.</value>
  </data>

  <data name="translate_period_number_0_has_to_date_less_than_from_date">
    <value>Period number {0} has a to date less than from date.</value>
  </data>

  <data name="translate_you_have_exceeded_number_of_allowed_days_during_the_year_max_is_0_used_is_1_requested_is_2">
    <value>
      You have exceeded the number of allowed days during the year. Maximum is {0}, used is {1}, requested is {2}.
    </value>
  </data>

  <data name="translate_job_classification_type_civil">
    <value>Civil</value>
  </data>

  <data name="translate_job_classification_type_sworn">
    <value>Sworn</value>
  </data>

  <data name="translate_0_is_required">
    <value>{0} is required.</value>
  </data>

  <data name="translate_0_is_invalid">
    <value>{0} is invalid.</value>
  </data>

  <data name="translate_day_count">
    <value>Day count</value>
  </data>

  <data name="translate_you_are_not_authorized_to_set_the_states_of_requests">
    <value>You are not authorized to set the states of requests.</value>
  </data>

  <data name="translate_invalid_state">
    <value>Invalid state.</value>
  </data>

  <data name="translate_request_not_found">
    <value>Request not found.</value>
  </data>

  <data name="translate_request_is_already_in_this_state">
    <value>Request is already in this state.</value>
  </data>

  <data name="translate_the_user_is_not_authorized_to_take_this_action">
    <value>The user is not authorized to take this action.</value>
  </data>

  <data name="translate_attachment_file">
    <value>Attachment file</value>
  </data>

  <data name="translate_salary_allowance_type_type_allowance">
    <value>Allowance</value>
  </data>

  <data name="translate_salary_allowance_type_type_compensation">
    <value>Compensation</value>
  </data>

  <data name="translate_salary_allowance_type_subtype_changing">
    <value>Changing</value>
  </data>

  <data name="translate_salary_allowance_type_subtype_complementary">
    <value>Complementary</value>
  </data>

  <data name="translate_salary_allowance_type_subtype_base_salary_addition">
    <value>Base salary addition</value>
  </data>

  <data name="translate_salary_allowance_type_display_mode_aggregated">
    <value>Aggregated</value>
  </data>

  <data name="translate_salary_allowance_type_display_mode_separate">
    <value>Separate</value>
  </data>

  <data name="translate_salary_deduction_type_display_mode_aggregated">
    <value>Aggregated</value>
  </data>

  <data name="translate_salary_deduction_type_display_mode_separate">
    <value>Separate</value>
  </data>

  <data name="translate_employee_job_level_change_type_promotion">
    <value>Promotion</value>
  </data>

  <data name="translate_employee_job_level_change_type_demotion">
    <value>Demotion</value>
  </data>

  <data name="translate_employee_job_level_change_type_stripping">
    <value>Stripping</value>
  </data>

  <data name="translate_message_importance_level_normal">
    <value>Normal</value>
  </data>

  <data name="translate_message_importance_level_urgent">
    <value>Urgent</value>
  </data>

  <data name="translate_message_confidentiality_level_general">
    <value>General</value>
  </data>

  <data name="translate_message_confidentiality_level_classified">
    <value>Classified</value>
  </data>

  <data name="translate_marriage_status_married">
    <value>Married</value>
  </data>

  <data name="translate_marriage_status_deceased">
    <value>Deceased</value>
  </data>

  <data name="translate_marriage_status_divorced">
    <value>Divorced</value>
  </data>

  <data name="translate_marriage_status_unknown">
    <value>Unknown</value>
  </data>

  <data name="translate_employee_document_type_national_id">
    <value>National ID</value>
  </data>

  <data name="translate_employee_document_type_passport">
    <value>Passport</value>
  </data>

  <data name="translate_employee_document_type_family_book">
    <value>Family book</value>
  </data>

  <data name="translate_employee_document_type_contract">
    <value>Contract</value>
  </data>

  <data name="translate_employee_document_type_residency">
    <value>Residency</value>
  </data>

  <data name="translate_employee_document_type_signature">
    <value>Signature</value>
  </data>

  <data name="translate_employee_document_type_other">
    <value>Other</value>
  </data>

  <data name="translate_employee_transfer_type_transfer">
    <value>Transfer</value>
  </data>

  <data name="translate_employee_transfer_type_secondment">
    <value>Secondment</value>
  </data>

  <data name="translate_employee_transfer_type_delegation">
    <value>Delegation</value>
  </data>

  <data name="translate_add">
    <value>Add</value>
  </data>

  <data name="translate_deduct">
    <value>Deduct</value>
  </data>

  <data name="translate_year">
    <value>Years</value>
  </data>

  <data name="translate_month">
    <value>Months</value>
  </data>

  <data name="translate_day">
    <value>Days</value>
  </data>

  <data name="translate_name">
    <value>Name</value>
  </data>

  <data name="translate_gender">
    <value>Gender</value>
  </data>

  <data name="translate_date_of_birth">
    <value>Date of Birth</value>
  </data>

  <data name="translate_age">
    <value>Age</value>
  </data>

  <data name="translate_department">
    <value>Department</value>
  </data>

  <data name="translate_mobile_number">
    <value>Mobile Number</value>
  </data>

  <data name="translate_email">
    <value>Email</value>
  </data>

  <data name="translate_years">
    <value>Years</value>
  </data>

  <data name="translate_months">
    <value>Months</value>
  </data>

  <data name="translate_days">
    <value>Days</value>
  </data>

  <data name="translate_0_should_be_at_least_1_characters">
    <value>{0} should be at least {1} characters.</value>
  </data>

  <data name="translate_credit_card_type_visa">
    <value>Visa</value>
  </data>

  <data name="translate_credit_card_type_mastercard">
    <value>Mastercard</value>
  </data>

  <data name="translate_credit_card_type_american_express">
    <value>American Express</value>
  </data>

  <data name="translate_credit_card_type_other">
    <value>Other</value>
  </data>

  <data name="translate_driver_license_type_motorbike">
    <value>Motorbike</value>
  </data>

  <data name="translate_driver_license_type_light">
    <value>Light</value>
  </data>

  <data name="translate_driver_license_type_heavy">
    <value>Heavy</value>
  </data>

  <data name="translate_driver_license_type_light_bus">
    <value>Light bus</value>
  </data>

  <data name="translate_driver_license_type_heavy_bus">
    <value>Heavy bus</value>
  </data>

  <data name="translate_driver_license_type_light_forklift">
    <value>Light forklift</value>
  </data>

  <data name="translate_driver_license_type_heavy_forklift">
    <value>Heavy forklift</value>
  </data>

  <data name="translate_language_level_none">
    <value>None</value>
  </data>

  <data name="translate_language_level_basic">
    <value>Basic</value>
  </data>

  <data name="translate_language_level_good">
    <value>Good</value>
  </data>

  <data name="translate_language_level_excellent">
    <value>Excellent</value>
  </data>

  <data name="translate_language_level_native">
    <value>Native</value>
  </data>

  <data name="translate_social_media_platform_type_facebook">
    <value>Facebook</value>
  </data>

  <data name="translate_social_media_platform_type_twitter">
    <value>Twitter</value>
  </data>

  <data name="translate_social_media_platform_type_instagram">
    <value>Instagram</value>
  </data>

  <data name="translate_social_media_platform_type_linkedin">
    <value>Linkedin</value>
  </data>

  <data name="translate_social_media_platform_type_whatsapp">
    <value>Whatsapp</value>
  </data>

  <data name="translate_social_media_platform_type_youtube">
    <value>Youtube</value>
  </data>

  <data name="translate_social_media_platform_type_tiktok">
    <value>Tiktok</value>
  </data>

  <data name="translate_social_media_platform_type_snapchat">
    <value>Snapchat</value>
  </data>

  <data name="translate_social_media_platform_type_pinterest">
    <value>Pinterest</value>
  </data>

  <data name="translate_social_media_platform_type_reddit">
    <value>Reddit</value>
  </data>

  <data name="translate_rbac_permission_name_foundation:write">
    <value>Write system data.</value>
  </data>

  <data name="translate_rbac_permission_description_foundation:write">
    <value>Will be able to write general system data.</value>
  </data>

  <data name="translate_rbac_role_name_full_access">
    <value>Full access</value>
  </data>

  <data name="translate_rbac_role_description_full_access">
    <value>Will have the ability to perform all actions on all system components.</value>
  </data>

  <!-- File Management -->
  <data name="translate_could_not_find_file_in_object_storage">
    <value>Could not find file in object storage.</value>
  </data>

  <!-- Flow -->
  <data name="translate_flow_state_draft">
    <value>Draft</value>
  </data>

  <data name="translate_flow_state_processing">
    <value>Processing</value>
  </data>

  <data name="translate_flow_state_approved">
    <value>Approved</value>
  </data>

  <data name="translate_flow_state_rejected">
    <value>Rejected</value>
  </data>

  <!-- Servicing -->
  <data name="translate_cannot_apply_to_this_service">
    <value>Cannot apply to this service.</value>
  </data>

  <data name="translate_cannot_update_this_request">
    <value>Cannot update this request.</value>
  </data>

  <data name="translate_rbac_permission_name_servicing:requests:read">
    <value>Read service requests</value>
  </data>

  <data name="translate_rbac_permission_description_servicing:requests:read">
    <value>Will be able to read all service requests for all users.</value>
  </data>

  <data name="translate_rbac_permission_name_servicing:requests:write">
    <value>Write service requests</value>
  </data>

  <data name="translate_rbac_permission_description_servicing:requests:write">
    <value>Will be able to write all service requests for all users.</value>
  </data>

  <data name="translate_rbac_permission_name_servicing:requests:delete">
    <value>Delete service requests</value>
  </data>

  <data name="translate_rbac_permission_description_servicing:requests:delete">
    <value>Will be able to delete all service requests for all users.</value>
  </data>

  <data name="translate_rbac_role_name_servicing:requests">
    <value>Service requests management</value>
  </data>

  <data name="translate_rbac_role_description_servicing:requests">
    <value>Manage all requests for all users in the system</value>
  </data>

  <!-- Auth -->
  <data name="translate_user_not_active">
    <value>User not active</value>
  </data>

  <data name="translate_your_account_is_not_active_please_contact_the_administrator_to_activate_it">
    <value>Your account is not active. Please contact the administrator to activate it.</value>
  </data>

  <data name="translate_required_password_change">
    <value>Required password change</value>
  </data>

  <data
    name="translate_your_password_has_been_reset_by_an_administrator_you_need_to_change_it_before_using_the_application">
    <value>Your password has been reset by an administrator. You need to change it before using the application.</value>
  </data>

  <data name="translate_you_need_to_login_again">
    <value>You need to login again.</value>
  </data>

  <data name="translate_another_user_with_the_same_email_address_already_exists">
    <value>Another user with the same email address already exists.</value>
  </data>

  <data name="translate_rbac_permission_name_auth:full_department_scope">
    <value>Full department scope</value>
  </data>

  <data name="translate_rbac_permission_description_auth:full_department_scope">
    <value>The users will have full visibility over department-specific entities regardless of the users' current attached departments.</value>
  </data>

  <data name="translate_rbac_role_name_departments_full_scope">
    <value>Full departmental scope</value>
  </data>

  <data name="translate_rbac_role_description_departments_full_scope">
    <value>The users will have full visibility over department-specific entities regardless of the users' current attached departments.</value>
  </data>

  <!-- Attendance -->
  <data name="translate_zone_not_found">
    <value>Zone not found.</value>
  </data>

  <data name="translate_employee_is_already_in_the_zone">
    <value>Employee is already in the zone.</value>
  </data>

  <data name="translate_employee_is_not_in_the_zone">
    <value>Employee is not in the zone.</value>
  </data>

  <data name="translate_user_not_found">
    <value>User not found.</value>
  </data>

  <data name="translate_employee_not_attached_to_user">
    <value>Employee not attached to user.</value>
  </data>

  <data name="translate_the_employee_is_not_on_duty_now">
    <value>The employee is not on duty now.</value>
  </data>

  <data name="translate_more_than_one_potential_slots_were_returned">
    <value>More than one potential slots were returned.</value>
  </data>

  <data name="translate_could_not_find_schedule">
    <value>Could not find schedule.</value>
  </data>

  <data name="translate_employee_already_in_schedule">
    <value>Employee already in schedule.</value>
  </data>

  <data name="translate_schedule_was_not_found">
    <value>Schedule was not found.</value>
  </data>

  <data name="translate_the_employee_is_not_assigned_to_the_provided_schedule">
    <value>The employee is not assigned to the provided schedule.</value>
  </data>

  <data name="translate_no_identity_was_attached_to_request">
    <value>No identity was attached to request.</value>
  </data>

  <data name="translate_identity_has_no_employee_attached_to_it">
    <value>Identity has no employee attached to it.</value>
  </data>

  <data name="translate_user_assigned_to_the_request_does_not_have_an_employee_record">
    <value>User assigned to the request does not have an employee record.</value>
  </data>

  <data name="translate_user_id">
    <value>User ID</value>
  </data>

  <data name="translate_from">
    <value>From</value>
  </data>

  <data name="translate_to">
    <value>To</value>
  </data>

  <data name="translate_from_should_be_less_than_to">
    <value>From should be less than To.</value>
  </data>

  <data name="translate_longitude">
    <value>Longitude</value>
  </data>

  <data name="translate_latitude">
    <value>Latitude</value>
  </data>

  <data name="translate_radius">
    <value>Radius</value>
  </data>

  <data name="translate_type_is_required">
    <value>Type is required.</value>
  </data>

  <data name="translate_start_time_is_required">
    <value>Start time is required.</value>
  </data>

  <data name="translate_records_is_required_required">
    <value>Records is required.</value>
  </data>

  <data name="translate_there_should_be_at_least_one_record">
    <value>There should be at least one record.</value>
  </data>

  <data name="translate_invalid_type_was_provided">
    <value>Invalid type was provided.</value>
  </data>

  <data name="translate_duration_in_hours_is_required">
    <value>Duration in hours is required.</value>
  </data>

  <data name="translate_duration_cannot_be_less_than_or_equal_0">
    <value>Duration cannot be less than or equal {0}.</value>
  </data>

  <data name="translate_rbac_permission_name_attendance:zones:read">
    <value>"Read zone data</value>
  </data>

  <data name="translate_rbac_permission_description_attendance:zones:read">
    <value>Will be able to read zone data.</value>
  </data>

  <data name="translate_rbac_permission_name_attendance:zones:write">
    <value>Write zone data</value>
  </data>

  <data name="translate_rbac_permission_description_attendance:zones:write">
    <value>Will be able to write zone data.</value>
  </data>

  <data name="translate_rbac_permission_name_attendance:zones:delete">
    <value>Delete zone data</value>
  </data>

  <data name="translate_rbac_permission_description_attendance:zones:delete">
    <value>Will be able to delete zone data.</value>
  </data>

  <data name="translate_rbac_role_name_attendance_zones">
    <value>Zone management</value>
  </data>

  <data name="translate_rbac_role_description_attendance_zones">
    <value>Read, add, delete and update zones.</value>
  </data>

  <data name="translate_rbac_permission_name_attendance:permissions:read">
    <value>Read permission data</value>
  </data>

  <data name="translate_rbac_permission_description_attendance:permissions:read">
    <value>Will be able to read permission data.</value>
  </data>

  <data name="translate_rbac_permission_name_attendance:permissions:write">
    <value>Write permission data</value>
  </data>

  <data name="translate_rbac_permission_description_attendance:permissions:write">
    <value>Will be able to write permission data.</value>
  </data>

  <data name="translate_rbac_permission_name_attendance:permissions:delete">
    <value>Delete permission data</value>
  </data>

  <data name="translate_rbac_permission_description_attendance:permissions:delete">
    <value>Will be able to delete permission data.</value>
  </data>

  <data name="translate_rbac_role_name_attendance_permissions">
    <value>Permission management</value>
  </data>

  <data name="translate_rbac_role_description_attendance_permissions">
    <value>Read, add, delete and update permissions.</value>
  </data>

  <data name="translate_rbac_permission_name_attendance:transactions:read">
    <value>Read attendance transactions</value>
  </data>

  <data name="translate_rbac_permission_description_attendance:transactions:read">
    <value>Will be able to read attendance transactions.</value>
  </data>

  <data name="translate_rbac_role_name_attendance_transactions">
    <value>Attendance transactions management</value>
  </data>

  <data name="translate_rbac_role_description_attendance_transactions">
    <value>Attendance transactions management</value>
  </data>

  <data name="translate_rbac_permission_name_attendance:punches:read">
    <value>Read attendance punches</value>
  </data>

  <data name="translate_rbac_permission_description_attendance:punches:read">
    <value>Will be able to read punches.</value>
  </data>

  <data name="translate_rbac_role_name_punches">
    <value>Punches management</value>
  </data>

  <data name="translate_rbac_role_description_punches">
    <value>Punches management.</value>
  </data>

  <data name="translate_rbac_permission_name_attendance:schedules:read">
    <value>Read schedule data</value>
  </data>

  <data name="translate_rbac_permission_description_attendance:schedules:read">
    <value>Will be able to read schedule data.</value>
  </data>

  <data name="translate_rbac_permission_name_attendance:schedules:write">
    <value>Write schedule data</value>
  </data>

  <data name="translate_rbac_permission_description_attendance:schedules:write">
    <value>Will be able to write schedule data.</value>
  </data>

  <data name="translate_rbac_permission_name_attendance:schedules:delete">
    <value>Delete schedule data</value>
  </data>

  <data name="translate_rbac_permission_description_attendance:schedules:delete">
    <value>Will be able to delete schedule data.</value>
  </data>

  <data name="translate_rbac_role_name_attendance_schedules">
    <value>Schedule management</value>
  </data>

  <data name="translate_rbac_role_description_attendance_schedules">
    <value>Read, add, delete and update schedules</value>
  </data>

  <!-- Users -->
  <data name="translate_new_password">
    <value>New password</value>
  </data>

  <data name="translate_could_not_find_user">
    <value>Could not find user.</value>
  </data>

  <data name="translate_old_password_is_wrong">
    <value>Old password is wrong.</value>
  </data>

  <data name="translate_new_password_is_the_same_as_old">
    <value>New password is the same as old.</value>
  </data>

  <data name="translate_rbac_permission_name_users:read">
    <value>Read user data</value>
  </data>

  <data name="translate_rbac_permission_description_users:read">
    <value>Will be able to read user data.</value>
  </data>

  <data name="translate_rbac_permission_name_users:write">
    <value>Write user data</value>
  </data>

  <data name="translate_rbac_permission_description_users:write">
    <value>Will be able to write user data.</value>
  </data>

  <data name="translate_rbac_permission_name_users:delete">
    <value>Delete user data</value>
  </data>

  <data name="translate_rbac_permission_description_users:delete">
    <value>Will be able to delete user data.</value>
  </data>

  <data name="translate_rbac_role_name_users">
    <value>User management</value>
  </data>

  <data name="translate_rbac_role_description_users">
    <value>Read, add, delete and update users</value>
  </data>
  
  <!-- Roles -->
  <data name="translate_rbac_permission_name_roles:read">
    <value>Read role data</value>
  </data>

  <data name="translate_rbac_permission_description_roles:read">
    <value>Will be able to read role data.</value>
  </data>

  <data name="translate_rbac_permission_name_roles:write">
    <value>Write role data</value>
  </data>

  <data name="translate_rbac_permission_description_roles:write">
    <value>Will be able to write role data.</value>
  </data>

  <data name="translate_rbac_permission_name_roles:delete">
    <value>Delete role data</value>
  </data>

  <data name="translate_rbac_permission_description_roles:delete">
    <value>Will be able to delete role data.</value>
  </data>

  <data name="translate_rbac_role_name_roles">
    <value>Role management</value>
  </data>

  <data name="translate_rbac_role_description_roles">
    <value>Read, add, delete and update roles</value>
  </data>

  <!-- Departments -->
  <data name="translate_rbac_permission_name_departments:read">
    <value>Read department data</value>
  </data>

  <data name="translate_rbac_permission_description_departments:read">
    <value>Will be able to read department data.</value>
  </data>

  <data name="translate_rbac_permission_name_departments:write">
    <value>Write department data</value>
  </data>

  <data name="translate_rbac_permission_description_departments:write">
    <value>Will be able to write department data.</value>
  </data>

  <data name="translate_rbac_permission_name_departments:delete">
    <value>Delete department data</value>
  </data>

  <data name="translate_rbac_permission_description_departments:delete">
    <value>Will be able to delete department data.</value>
  </data>

  <data name="translate_rbac_role_name_departments">
    <value>Department management</value>
  </data>

  <data name="translate_rbac_role_description_departments">
    <value>Read, add, delete and update departments.</value>
  </data>

  <!-- Employees -->
  <data name="translate_number">
    <value>Number</value>
  </data>

  <data name="translate_job_level">
    <value>Job Level</value>
  </data>

  <data name="translate_nationality">
    <value>Nationality</value>
  </data>

  <data name="translate_qualification">
    <value>Qualification</value>
  </data>

  <data name="translate_hire_date">
    <value>Hire Date</value>
  </data>

  <data name="translate_length_of_service">
    <value>Length of Service</value>
  </data>

  <data name="translate_job_title">
    <value>Job Title</value>
  </data>

  <data name="translate_job_category">
    <value>Job Category</value>
  </data>

  <data name="translate_nationality_acquisition_descent">
    <value>Dy descent</value>
  </data>

  <data name="translate_nationality_acquisition_law">
    <value>By law</value>
  </data>

  <data name="translate_nationality_acquisition_naturalization">
    <value>By naturalization</value>
  </data>

  <data name="translate_nationality_acquisition_article5">
    <value>According to Article 5</value>
  </data>

  <data name="translate_nationality_acquisition_article7">
    <value>According to Article 7</value>
  </data>

  <data name="translate_item_is_linked_to_other_resources">
    <value>Item is linked to other resources.</value>
  </data>

  <data name="translate_invalid_allowance_status">
    <value>Invalid allowance status.</value>
  </data>

  <data name="translate_employee_not_found">
    <value>Employee not found.</value>
  </data>

  <data name="translate_directory_does_not_exist">
    <value>Directory does not exist.</value>
  </data>

  <data name="translate_employee_number_not_found">
    <value>Employee number not found.</value>
  </data>

  <data name="translate_photo_file_not_found">
    <value>Photo file not found.</value>
  </data>

  <data name="translate_failed_to_create_photo_file">
    <value>Failed to create photo file.</value>
  </data>

  <data name="translate_failed_to_update_user_photo">
    <value>Failed to update user photo.</value>
  </data>

  <data name="translate_employee_is_currently_at_the_provided_state">
    <value>Employee is currently at the provided state.</value>
  </data>

  <data name="translate_new_nationality_required">
    <value>New nationality required.</value>
  </data>

  <data name="translate_invalid_nationality">
    <value>Invalid nationality.</value>
  </data>

  <data name="translate_major">
    <value>Major</value>
  </data>

  <data name="translate_employee_id">
    <value>Employee ID</value>
  </data>

  <data name="translate_allowance_status">
    <value>Allowance status</value>
  </data>

  <data name="translate_employee">
    <value>Employee</value>
  </data>

  <data name="translate_graduation_date">
    <value>Graduation date</value>
  </data>

  <data name="translate_promotion_type">
    <value>Promotion type</value>
  </data>

  <data name="translate_reason">
    <value>Reason</value>
  </data>

  <data name="translate_change_date">
    <value>Change date</value>
  </data>

  <data name="translate_current_job_level">
    <value>Current job level</value>
  </data>

  <data name="translate_new_job_level">
    <value>New job level</value>
  </data>

  <data name="translate_reference_date">
    <value>Reference date</value>
  </data>

  <data name="translate_start_date">
    <value>Start date</value>
  </data>

  <data name="translate_end_date">
    <value>End date</value>
  </data>

  <data name="translate_record_date">
    <value>Record date</value>
  </data>

  <data name="translate_period">
    <value>Period</value>
  </data>

  <data name="translate_last_work_date">
    <value>Last work date</value>
  </data>

  <data name="translate_termination_reason">
    <value>Termination reason</value>
  </data>

  <data name="translate_new_department">
    <value>New department</value>
  </data>

  <data name="translate_transfer_time">
    <value>Transfer time</value>
  </data>

  <data name="translate_email_required">
    <value>Email required.</value>
  </data>

  <data name="translate_nationality_acquisition_method">
    <value>Nationality acquisition method</value>
  </data>

  <data name="translate_employee_number_is_required">
    <value>Employee number is required.</value>
  </data>

  <data name="translate_selected_user_is_already_attached_to_another_employee">
    <value>Selected user is already attached to another employee.</value>
  </data>

  <data name="translate_state">
    <value>State</value>
  </data>

  <data name="translate_file">
    <value>File</value>
  </data>

  <data name="translate_job_classification_is_required">
    <value>Job classification is required.</value>
  </data>

  <data name="translate_organizational_unit">
    <value>Organizational unit</value>
  </data>

  <data name="translate_rbac_permission_name_employees:children:write">
    <value>Write employee job level changes data.</value>
  </data>

  <data name="translate_rbac_permission_description_employees:children:write">
    <value>Will be able to write employee job level changes data.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:children:delete">
    <value>Delete employee job level changes data</value>
  </data>

  <data name="translate_rbac_permission_description_employees:children:delete">
    <value>Will be able to delete employee job level changes data.</value>
  </data>

  <data name="translate_rbac_role_name_employees:children">
    <value>Employees children management</value>
  </data>

  <data name="translate_rbac_role_description_employees:children">
    <value>Add, delete and update employee job level changes data.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:job_level_changes:read">
    <value>Read employee job level changes data.</value>
  </data>

  <data name="translate_rbac_permission_description_employees:job_level_changes:read">
    <value>Will be able to read employee job level changes data.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:job_level_changes:write">
    <value>Write employee job level changes data.</value>
  </data>

  <data name="translate_rbac_permission_description_employees:job_level_changes:write">
    <value>Will be able to write employee job level changes data.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:job_level_changes:delete">
    <value>Delete employee job level changes data</value>
  </data>

  <data name="translate_rbac_permission_description_employees:job_level_changes:delete">
    <value>Will be able to delete employee job level changes data.</value>
  </data>

  <data name="translate_rbac_role_name_employees:job_level_changes">
    <value>Employees management</value>
  </data>

  <data name="translate_rbac_role_description_employees:job_level_changes">
    <value>Read, add, delete and update employee job level changes data.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:read">
    <value>Read employee data.</value>
  </data>

  <data name="translate_rbac_permission_description_employees:read">
    <value>Will be able to read employee data.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:read_sensitive">
    <value>Read employee sensitive data.</value>
  </data>

  <data name="translate_rbac_permission_description_employees:read_sensitive">
    <value>Will be able to read employee sensitive data.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:write">
    <value>Write employee data.</value>
  </data>

  <data name="translate_rbac_permission_description_employees:write">
    <value>Will be able to write employee data.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:delete">
    <value>Delete employee data</value>
  </data>

  <data name="translate_rbac_permission_description_employees:delete">
    <value>Will be able to delete employee data.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:approve">
    <value>Approve employee requests</value>
  </data>

  <data name="translate_rbac_permission_description_employees:approve">
    <value>Will be able to approve employee requests.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:higher_approve">
    <value>High approve employee requests</value>
  </data>

  <data name="translate_rbac_permission_description_employees:higher_approve">
    <value>Will be able to high approve employee requests.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:manage_lists">
    <value>Manage human resources settings</value>
  </data>

  <data name="translate_rbac_permission_description_employees:manage_lists">
    <value>Will be able to add, update, delete employees-specific system lists.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:export">
    <value>Export employees data</value>
  </data>

  <data name="translate_rbac_permission_description_employees:export">
    <value>Will be able to export employees data to Excel file.</value>
  </data>

  <data name="translate_rbac_role_name_employees">
    <value>Employees management</value>
  </data>

  <data name="translate_rbac_role_description_employees">
    <value>Read, add, delete and update employees data.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:terminations:read">
    <value>Read employee termination data.</value>
  </data>

  <data name="translate_rbac_permission_description_employees:terminations:read">
    <value>Will be able to read employee termination data.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:terminations:write">
    <value>Write employee termination data.</value>
  </data>

  <data name="translate_rbac_permission_description_employees:terminations:write">
    <value>Will be able to write employee termination data.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:terminations:delete">
    <value>Delete employee termination data</value>
  </data>

  <data name="translate_rbac_permission_description_employees:terminations:delete">
    <value>Will be able to delete employee termination data.</value>
  </data>

  <data name="translate_rbac_role_name_employees:terminations">
    <value>Employees management</value>
  </data>

  <data name="translate_rbac_role_description_employees:terminations">
    <value>Read, add, delete and update employee termination data.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:armory_approve">
    <value>Approve armory requests</value>
  </data>

  <data name="translate_rbac_permission_description_employees:armory_approve">
    <value>Will be able to approve armory requests for employees.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:it_approve">
    <value>Approve IT requests</value>
  </data>

  <data name="translate_rbac_permission_description_employees:it_approve">
    <value>Will be able to approve IT requests for employees.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:warehouse_approve">
    <value>Approve warehouse requests</value>
  </data>

  <data name="translate_rbac_permission_description_employees:warehouse_approve">
    <value>Will be able to approve warehouse requests for employees.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:affairs_approve">
    <value>Approve affairs requests</value>
  </data>

  <data name="translate_rbac_permission_description_employees:affairs_approve">
    <value>Will be able to approve affairs requests for employees.</value>
  </data>

  <data name="translate_rbac_permission_name_employees:finance_approve">
    <value>Approve finance requests</value>
  </data>

  <data name="translate_rbac_permission_description_employees:finance_approve">
    <value>Will be able to approve finance requests for employees.</value>
  </data>

  <!-- Payroll -->
  <data name="translate_date_colon_0">
    <value>Date: {0}</value>
  </data>

  <data name="translate_salary_certificate_pdf">
    <value>Salary certificate.pdf</value>
  </data>

  <data name="translate_detailed_salary_certificate_pdf">
    <value>Detailed salary certificate.pdf</value>
  </data>

  <data name="translate_employment_proof_certificate_pdf">
    <value>Employment proof certificate.pdf</value>
  </data>

  <data name="translate_and_a_total_salary_of_0_1_dirhams_only">
    <value>&#160;and a total salary of {0} ({1}) dirhams only.</value>
  </data>

  <data name="translate_and_what_follows_are_their_employment_details:">
    <value>&#160;and what follows are their employment details:</value>
  </data>

  <data name="translate_salary_certificate">
    <value>Salary Certificate</value>
  </data>

  <data
    name="translate_the_general_command_of_the_amiri_guard_certifies_that_0_1_has_been_working_under_it_with_the_number_2_since_3_and_is_still_in_his_position_to_date_4">
    <value>The general command of the Amiri Guard certifies that {0}{1} has been working under it with the number {2}
      since {3}, and is still in his position to date{4}
    </value>
  </data>

  <data name="translate_s">
    <value>S</value>
  </data>

  <data name="translate_details">
    <value>Details</value>
  </data>

  <data name="translate_base_salary">
    <value>Base salary</value>
  </data>

  <data name="translate_0_1_dirhams_only">
    <value>{0} ({1}) dirhams only.</value>
  </data>

  <data name="translate_pension_deduction">
    <value>Pension deduction</value>
  </data>

  <data name="translate_not_paid">
    <value>Not paid</value>
  </data>

  <data name="translate_total_salary">
    <value>Total salary</value>
  </data>

  <data
    name="translate_this_certificate_has_been_given_to_them_upon_their_request_without_the_command_bearing_any_responsibility_to_be_presented_to_0">
    <value>
      <![CDATA[This certificate has been given to them upon their request without the command bearing any responsibility, to be presented to {0}.]]>
    </value>
  </data>

  <data name="translate_this_certificate_is_valid_for_one_month_from_date_of_issue">
    <value>
      This certificate is valid for one month from date of issue.
    </value>
  </data>

  <data name="translate_salary_change_transaction_entity_type_salary">
    <value>Salary</value>
  </data>

  <data name="translate_salary_change_transaction_entity_type_allowance">
    <value>Allowance</value>
  </data>

  <data name="translate_salary_change_transaction_entity_type_deduction">
    <value>Deduction</value>
  </data>

  <data name="translate_salary_change_transaction_entity_type_bonus_or_reduction">
    <value>Bonus or reduction</value>
  </data>

  <data name="translate_salary_change_transaction_type_creation">
    <value>Creation</value>
  </data>

  <data name="translate_salary_change_transaction_type_update">
    <value>Update</value>
  </data>

  <data name="translate_salary_change_transaction_type_deletion">
    <value>Deletion</value>
  </data>

  <data name="translate_another_register_with_the_same_year_and_month_exists">
    <value>Another register with the same year and month exists.</value>
  </data>

  <data name="translate_general_payroll_of_employees_for_the_month_0_1">
    <value>General payroll of employees for the month {0} - {1}</value>
  </data>

  <data name="translate_serial">
    <value>Serial</value>
  </data>

  <data name="translate_employee_number">
    <value>Employee number</value>
  </data>

  <data name="translate_bank">
    <value>Bank</value>
  </data>

  <data name="translate_iban">
    <value>IBAN</value>
  </data>

  <data name="translate_base">
    <value>base</value>
  </data>

  <data name="translate_actual">
    <value>Actual</value>
  </data>

  <data name="translate_total">
    <value>Total</value>
  </data>

  <data name="translate_total_deductions">
    <value>Total deductions</value>
  </data>

  <data name="translate_net">
    <value>Net</value>
  </data>

  <data name="translate_rbac_permission_name_payroll:registers:read">
    <value>Read payroll registers data</value>
  </data>

  <data name="translate_rbac_permission_description_payroll:registers:read">
    <value>Will be able to read payroll registers data.</value>
  </data>

  <data name="translate_rbac_permission_name_payroll:registers:delete">
    <value>Delete payroll registers data</value>
  </data>

  <data name="translate_rbac_permission_description_payroll:registers:delete">
    <value>Will be able to delete payroll registers data.</value>
  </data>

  <data name="translate_rbac_permission_name_payroll:registers:generate">
    <value>Generate payroll registers</value>
  </data>

  <data name="translate_rbac_permission_description_payroll:registers:generate">
    <value>Will be able to generate payroll registers.</value>
  </data>

  <data name="translate_rbac_permission_name_payroll:registers:approve">
    <value>First approval of payroll registers</value>
  </data>

  <data name="translate_rbac_permission_description_payroll:registers:approve">
    <value>Will be able to approve payroll registers.</value>
  </data>

  <data name="translate_rbac_permission_name_payroll:registers:finalize">
    <value>Final approval of payroll registers</value>
  </data>

  <data name="translate_rbac_permission_description_payroll:registers:finalize">
    <value>Will be able to finalize the approval of payroll registers.</value>
  </data>

  <data name="translate_rbac_role_name_payroll_registers">
    <value>Payroll registers management</value>
  </data>

  <data name="translate_rbac_role_description_payroll_registers">
    <value>Read, delete, generate, and finalize approvals for payroll registers.</value>
  </data>

  <data name="translate_rbac_permission_name_payroll:salaries:read">
    <value>Read salaries data</value>
  </data>

  <data name="translate_rbac_permission_description_payroll:salaries:read">
    <value>Will be able to read salaries data.</value>
  </data>

  <data name="translate_rbac_permission_name_payroll:salaries:write">
    <value>Write salaries data</value>
  </data>

  <data name="translate_rbac_permission_description_payroll:salaries:write">
    <value>Will be able to write salaries data.</value>
  </data>

  <data name="translate_rbac_permission_name_payroll:salaries:delete">
    <value>Delete salaries data</value>
  </data>

  <data name="translate_rbac_permission_description_payroll:salaries:delete">
    <value>Will be able to delete salaries data.</value>
  </data>

  <data name="translate_rbac_permission_name_payroll:salaries:approve">
    <value>Approve salaries affairs</value>
  </data>

  <data name="translate_rbac_permission_description_payroll:salaries:approve">
    <value>Will be able to approve salaries data.</value>
  </data>

  <data name="translate_rbac_permission_name_payroll:salaries:higher_approve">
    <value>Higher approve salaries affairs</value>
  </data>

  <data name="translate_rbac_permission_description_payroll:salaries:higher_approve">
    <value>Will be able to high approve salaries data.</value>
  </data>

  <data name="translate_rbac_role_name_payroll:salaries">
    <value>Salaries management</value>
  </data>

  <data name="translate_rbac_role_description_payroll:salaries">
    <value>Read, add, delete and update salaries data.</value>
  </data>

  <!-- Messaging -->
  <data name="translate_message_type_message">
    <value>Message</value>
  </data>

  <data name="translate_message_type_circular">
    <value>Circular</value>
  </data>

  <data name="translate_message_type_directive">
    <value>Directive</value>
  </data>

  <data name="translate_message_type_order">
    <value>Order</value>
  </data>

  <data name="translate_message_state_draft">
    <value>Draft</value>
  </data>

  <data name="translate_message_state_submitted">
    <value>Submitted</value>
  </data>

  <data name="translate_message_state_returned">
    <value>Returned</value>
  </data>

  <data name="translate_message_state_approved">
    <value>Approved</value>
  </data>

  <data name="translate_date">
    <value>Date</value>
  </data>

  <data name="translate_not_sent_yet">
    <value>Not sent yet</value>
  </data>

  <data name="translate_reference_number">
    <value>Reference number</value>
  </data>

  <data name="translate_importance_level">
    <value>Importance level</value>
  </data>

  <data name="translate_confidentiality_level">
    <value>Confidentiality level</value>
  </data>

  <data name="translate_copy_to_0_for_information">
    <value>Copy to {0} for information.</value>
  </data>

  <data name="translate_recipients">
    <value>Recipients</value>
  </data>

  <data name="translate_body">
    <value>Body</value>
  </data>

  <data name="translate_subject">
    <value>Subject</value>
  </data>

  <data name="translate_selected_employee_should_be_the_manager_of_the_current_user">
    <value>Selected employee should be the manager of the current user.</value>
  </data>

  <data name="translate_selected_manager_does_not_have_a_signature">
    <value>Selected manager does not have a signature.</value>
  </data>

  <data name="translate_rbac_permission_name_messaging:categories:read">
    <value>Read message categories data</value>
  </data>

  <data name="translate_rbac_permission_description_messaging:categories:read">
    <value>The user will be able to read all message categories data.</value>
  </data>

  <data name="translate_rbac_permission_name_messaging:categories:write">
    <value>Write message categories data</value>
  </data>

  <data name="translate_rbac_permission_description_messaging:categories:write">
    <value>The user will be able to write all message categories data.</value>
  </data>

  <data name="translate_rbac_permission_name_messaging:categories:delete">
    <value>Delete message categories data</value>
  </data>

  <data name="translate_rbac_permission_description_messaging:categories:delete">
    <value>The user will be able to delete all message categories data.</value>
  </data>

  <data name="translate_rbac_role_name_message_categories">
    <value>Message categories management</value>
  </data>

  <data name="translate_rbac_role_description_message_categories">
    <value>Read, add, delete and update message categories data.</value>
  </data>

  <data name="translate_rbac_permission_name_messaging:messages:send_circulars">
    <value>Send circulars</value>
  </data>

  <data name="translate_rbac_permission_description_messaging:messages:send_circulars">
    <value>The user will have the ability to send circulars.</value>
  </data>

  <data name="translate_rbac_permission_name_messaging:messages:send_directives">
    <value>Send directives</value>
  </data>

  <data name="translate_rbac_permission_description_messaging:messages:send_directives">
    <value>The user will have the ability to send directives.</value>
  </data>

  <data name="translate_rbac_permission_name_messaging:messages:send_orders">
    <value>Send orders</value>
  </data>

  <data name="translate_rbac_permission_description_messaging:messages:send_orders">
    <value>The user will have the ability to send orders.</value>
  </data>

  <!-- Suggestions -->
  <data name="translate_suggestion_state_draft">
    <value>Draft</value>
  </data>

  <data name="translate_suggestion_state_submitted">
    <value>Submitted</value>
  </data>

  <data name="translate_suggestion_state_returned">
    <value>Returned</value>
  </data>

  <data name="translate_suggestion_state_preceded">
    <value>Preceded</value>
  </data>

  <data name="translate_suggestion_state_inapplicable">
    <value>Inapplicable</value>
  </data>

  <data name="translate_suggestion_state_in_progress">
    <value>In progress</value>
  </data>

  <data name="translate_suggestion_state_completed">
    <value>Completed</value>
  </data>

  <data name="translate_rbac_permission_name_suggestions:assess">
    <value>Assess suggestions</value>
  </data>

  <data name="translate_rbac_permission_description_suggestions:assess">
    <value>Will be able to assess suggestions by the employees.</value>
  </data>

  <data name="translate_rbac_permission_name_suggestions:department_assess">
    <value>Assess suggestions (Departments)</value>
  </data>

  <data name="translate_rbac_permission_description_suggestions:department_assess">
    <value>Will be able to assess suggestions by the department related to the user.</value>
  </data>

  <data name="translate_rbac_permission_name_suggestions:manage_lists">
    <value>Manage suggestion-specific lists</value>
  </data>

  <data name="translate_rbac_permission_description_suggestions:manage_lists">
    <value>Will be able to add, edit, and delete suggestion-specific lists.</value>
  </data>

  <!-- Surveys -->
  <data name="translate_survey_state_published">
    <value>Published</value>
  </data>

  <data name="translate_survey_state_unpublished">
    <value>Unpublished</value>
  </data>

  <data name="translate_from_should_be_less_than_or_equal_to_to">
    <value>From date should be less than or equal to to date.</value>
  </data>

  <data name="translate_rbac_permission_name_surveys:read">
    <value>Read survey data</value>
  </data>

  <data name="translate_rbac_permission_description_surveys:read">
    <value>Will be able to read survey data.</value>
  </data>

  <data name="translate_rbac_permission_name_surveys:write">
    <value>Write survey data</value>
  </data>

  <data name="translate_rbac_permission_description_surveys:write">
    <value>Will be able to write survey data.</value>
  </data>

  <data name="translate_rbac_permission_name_surveys:delete">
    <value>Delete survey data</value>
  </data>

  <data name="translate_rbac_permission_description_surveys:delete">
    <value>Will be able to delete survey data.</value>
  </data>

  <data name="translate_rbac_role_name_surveys">
    <value>Survey management</value>
  </data>

  <data name="translate_rbac_role_description_surveys">
    <value>Read, add, delete and update surveys.</value>
  </data>

  <!-- Recruitment -->
  <data name="translate_recruitment_applicant_classification_citizen">
    <value>Citizen</value>
  </data>

  <data name="translate_recruitment_applicant_classification_gcc">
    <value>GCC Citizen</value>
  </data>

  <data name="translate_recruitment_applicant_classification_resident">
    <value>Resident</value>
  </data>

  <data name="translate_recruitment_applicant_classification_residency_dependant">
    <value>Residency Dependent</value>
  </data>

  <data name="translate_recruitment_applicant_classification_visa_dependant">
    <value>Visa Dependent</value>
  </data>

  <data name="translate_recruitment_applicant_classification_visa_holder">
    <value>Visa Holder</value>
  </data>

  <data name="translate_recruitment_applicant_complexion_dark">
    <value>Dark Complexion</value>
  </data>

  <data name="translate_recruitment_applicant_complexion_very_dark">
    <value>Very Dark Complexion</value>
  </data>

  <data name="translate_recruitment_applicant_complexion_light">
    <value>Light Complexion</value>
  </data>

  <data name="translate_recruitment_applicant_complexion_very_light">
    <value>Very Light Complexion</value>
  </data>

  <data name="translate_recruitment_applicant_complexion_olive">
    <value>Olive Complexion</value>
  </data>

  <data name="translate_recruitment_applicant_complexion_medium">
    <value>Medium Complexion</value>
  </data>

  <data name="translate_recruitment_applicant_country_visit_type_business">
    <value>Business</value>
  </data>

  <data name="translate_recruitment_applicant_country_visit_type_leisure">
    <value>Leisure</value>
  </data>

  <data name="translate_recruitment_applicant_disability_type_hearing">
    <value>Hearing Disability</value>
  </data>

  <data name="translate_recruitment_applicant_disability_type_vision">
    <value>Vision Disability</value>
  </data>

  <data name="translate_recruitment_applicant_disability_type_motion">
    <value>Motion Disability</value>
  </data>

  <data name="translate_recruitment_applicant_disability_type_psychological">
    <value>Psychological Disability</value>
  </data>

  <data name="translate_recruitment_applicant_disability_type_speech">
    <value>Speech Disability</value>
  </data>

  <data name="translate_recruitment_applicant_eye_color_black">
    <value>Black</value>
  </data>

  <data name="translate_recruitment_applicant_eye_color_blue">
    <value>Blue</value>
  </data>

  <data name="translate_recruitment_applicant_eye_color_brown">
    <value>Brown</value>
  </data>

  <data name="translate_recruitment_applicant_eye_color_gray">
    <value>Gray</value>
  </data>

  <data name="translate_recruitment_applicant_eye_color_green">
    <value>Green</value>
  </data>

  <data name="translate_recruitment_applicant_eye_color_hazel">
    <value>Hazel</value>
  </data>

  <data name="translate_recruitment_applicant_eye_color_red">
    <value>Red</value>
  </data>

  <data name="translate_recruitment_applicant_referral_source_recruitment_agency">
    <value>Recruitment Agency</value>
  </data>

  <data name="translate_recruitment_applicant_referral_source_facebook">
    <value>Facebook</value>
  </data>

  <data name="translate_recruitment_applicant_referral_source_twitter">
    <value>Twitter</value>
  </data>

  <data name="translate_recruitment_applicant_referral_source_instagram">
    <value>Instagram</value>
  </data>

  <data name="translate_recruitment_applicant_referral_source_website">
    <value>Website</value>
  </data>

  <data name="translate_recruitment_applicant_referral_source_friends_and_family">
    <value>Friends and Family</value>
  </data>

  <data name="translate_recruitment_applicant_referral_source_newspaper">
    <value>Newspaper</value>
  </data>

  <data name="translate_recruitment_applicant_referral_source_job_fair">
    <value>Job Fair</value>
  </data>

  <data name="translate_recruitment_applicant_referral_source_other">
    <value>Other</value>
  </data>

  <data name="translate_recruitment_applicant_talent_category_academic">
    <value>Academic</value>
  </data>

  <data name="translate_recruitment_applicant_talent_category_innovative">
    <value>Innovative</value>
  </data>

  <data name="translate_recruitment_applicant_talent_category_active">
    <value>Active</value>
  </data>

  <data name="translate_recruitment_applicant_talent_category_mental">
    <value>Mental</value>
  </data>

  <data name="translate_recruitment_applicant_talent_category_art">
    <value>Art</value>
  </data>

  <data name="translate_recruitment_applicant_talent_category_leadership">
    <value>Leadership</value>
  </data>

  <data name="translate_recruitment_applicant_talent_category_other">
    <value>Other</value>
  </data>

  <data name="translate_recruitment_applicant_visa_type_search_for_job">
    <value>Job Search</value>
  </data>

  <data name="translate_recruitment_applicant_visa_type_golden_visa">
    <value>Golden Visa</value>
  </data>

  <data name="translate_recruitment_applicant_visa_type_visit_visa">
    <value>Visit Visa</value>
  </data>

  <data name="translate_recruitment_applicant_visa_type_tourist">
    <value>Tourist Visa</value>
  </data>

  <data name="translate_recruitment_applicant_visa_type_work">
    <value>Work Visa</value>
  </data>

  <data
    name="translate_you_cannot_resubmit_this_application_you_either_missed_your_submission_window_or_the_application_is_already_submitted">
    <value>
      You cannot resubmit this application. You either missed your submission window or the application is already
      submitted.
    </value>
  </data>

  <data name="translate_current_user_does_not_have_an_applicant_profile_yet">
    <value>Current user does not have an applicant profile yet.</value>
  </data>

  <data name="translate_vacancy_is_not_active">
    <value>Vacancy is not active.</value>
  </data>

  <data name="translate_vacancy_is_not_available_for_applications_today">
    <value>Vacancy is not available for applications today.</value>
  </data>

  <data name="translate_vacancy_is_not_available_for_your_application">
    <value>Vacancy is not available for your application.</value>
  </data>

  <data name="translate_vacancy_is_not_available_for_your_gender">
    <value>Vacancy is not available for your gender.</value>
  </data>

  <data name="translate_you_are_young_for_this_vacancy">
    <value>You are too young for this vacancy.</value>
  </data>

  <data name="translate_you_are_old_for_this_vacancy">
    <value>You are too old for this vacancy.</value>
  </data>

  <data name="translate_vacancy_is_not_available_for_your_nationality">
    <value>Vacancy is not available for your nationality.</value>
  </data>

  <data name="translate_vacancy_is_not_available_for_your_educational_qualification">
    <value>Vacancy is not available for your educational qualification.</value>
  </data>

  <data name="translate_applicant_user_is_attached_to_an_employee">
    <value>Applicant user is attached to an employee.</value>
  </data>

  <data name="translate_rbac_permission_name_recruitment:applicants:read">
    <value>Read recruitment applicants data.</value>
  </data>

  <data name="translate_rbac_permission_description_recruitment:applicants:read">
    <value>Will be able to read recruitment applicants data.</value>
  </data>

  <data name="translate_rbac_permission_name_recruitment:applications:read">
    <value>Read vacancy applications data.</value>
  </data>

  <data name="translate_rbac_permission_description_recruitment:applications:read">
    <value>Will be able to read vacancy applications data.</value>
  </data>

  <data name="translate_rbac_permission_name_recruitment:vacancies:read">
    <value>Read vacancy data</value>
  </data>

  <data name="translate_rbac_permission_description_recruitment:vacancies:read">
    <value>Will be able to read vacancy data.</value>
  </data>

  <data name="translate_rbac_permission_name_recruitment:vacancies:write">
    <value>Write vacancy data</value>
  </data>

  <data name="translate_rbac_permission_description_recruitment:vacancies:write">
    <value>Will be able to write vacancy data.</value>
  </data>

  <data name="translate_rbac_permission_name_recruitment:vacancies:delete">
    <value>Delete vacancy data</value>
  </data>

  <data name="translate_rbac_permission_description_recruitment:vacancies:delete">
    <value>Will be able to delete vacancy data.</value>
  </data>

  <data name="translate_rbac_role_name_vacancies">
    <value>Vacancy management</value>
  </data>

  <data name="translate_rbac_role_description_vacancies">
    <value>Read, add, delete and update vacancies.</value>
  </data>

  <!-- Courses -->
  <data name="translate_rbac_permission_name_courses:read">
    <value>Read courses data.</value>
  </data>

  <data name="translate_rbac_permission_description_courses:read">
    <value>Will be able to read courses data.</value>
  </data>

  <data name="translate_rbac_permission_name_courses:write">
    <value>Write courses data.</value>
  </data>

  <data name="translate_rbac_permission_description_courses:write">
    <value>Will be able to write courses data.</value>
  </data>

  <data name="translate_rbac_permission_name_courses:delete">
    <value>Delete courses data</value>
  </data>

  <data name="translate_rbac_permission_description_courses:delete">
    <value>Will be able to delete courses data.</value>
  </data>

  <data name="translate_rbac_permission_name_courses:manage_lists">
    <value>Manage courses settings lists</value>
  </data>

  <data name="translate_rbac_permission_description_courses:manage_lists">
    <value>Will be able to add, update, delete courses-specific system lists.</value>
  </data>

  <data name="translate_rbac_role_name_courses:children">
    <value>Courses management</value>
  </data>

  <data name="translate_rbac_role_description_courses:children">
    <value>Add, delete and update courses data.</value>
  </data>

  <data name="translate_rbac_permission_name_courses:employees:read">
    <value>Read employee courses data.</value>
  </data>

  <data name="translate_rbac_permission_description_courses:employees:read">
    <value>Will be able to read employee courses data.</value>
  </data>

  <data name="translate_rbac_permission_name_courses:employees:write">
    <value>Write employee courses data.</value>
  </data>

  <data name="translate_rbac_permission_description_courses:employees:write">
    <value>Will be able to write employee courses data.</value>
  </data>

  <data name="translate_rbac_permission_name_courses:employees:delete">
    <value>Delete employee courses data</value>
  </data>

  <data name="translate_rbac_permission_description_courses:employees:delete">
    <value>Will be able to delete employee courses data.</value>
  </data>
  
  <!-- Leaves -->
  <data name="translate_rbac_permission_name_leaves:read">
    <value>Read leaves data</value>
  </data>

  <data name="translate_rbac_permission_description_leaves:read">
    <value>The user will be able to read all leaves data.</value>
  </data>

  <data name="translate_rbac_permission_name_leaves:write">
    <value>Write leaves data</value>
  </data>

  <data name="translate_rbac_permission_description_leaves:write">
    <value>The user will be able to write all leaves data.</value>
  </data>

  <data name="translate_rbac_permission_name_leaves:delete">
    <value>Delete leaves data</value>
  </data>

  <data name="translate_rbac_permission_description_leaves:delete">
    <value>The user will be able to delete all leaves data.</value>
  </data>

  <data name="translate_rbac_permission_name_leaves:final_approval">
    <value>Approve leave requests</value>
  </data>

  <data name="translate_rbac_permission_description_leaves:final_approval">
    <value>Will be able to approve leave requests.</value>
  </data>

  <data name="translate_rbac_permission_name_leaves:higher_approve">
    <value>Higher approve leave requests</value>
  </data>

  <data name="translate_rbac_permission_description_leaves:higher_approve">
    <value>Will be able to high approve leave requests.</value>
  </data>

  <data name="translate_rbac_permission_name_leaves:medical_approval">
    <value>Medical committee approval</value>
  </data>

  <data name="translate_rbac_permission_description_leaves:medical_approval">
    <value>Will be able to enter the decision of the medical committee.</value>
  </data>

  <data name="translate_rbac_role_name_leaves:all_approval">
    <value>All leaves approvals</value>
  </data>

  <data name="translate_rbac_role_description_leaves:all_approval">
    <value>The user will be able to approve all leaves for all approval types.</value>
  </data>

  <data name="translate_rbac_role_name_leaves:final_approval">
    <value>Final leaves approval</value>
  </data>

  <data name="translate_rbac_role_description_leaves:final_approval">
    <value>The user will be able to finalize the approval on leaves requests.</value>
  </data>

  <data name="translate_rbac_role_name_leaves:medical_approval">
    <value>Medical leaves approval</value>
  </data>

  <data name="translate_rbac_role_description_leaves:medical_approval">
    <value>The user will be able to provide medical approvals for sick leave requests.</value>
  </data>

  <data name="translate_rbac_role_name_leaves">
    <value>Employees management</value>
  </data>

  <data name="translate_rbac_role_description_leaves">
    <value>Read, add, delete and update leaves data.</value>
  </data>
  
  <!-- Logging -->
  <data name="translate_rbac_permission_name_logging:logs:read">
    <value>Read logs</value>
  </data>

  <data name="translate_rbac_permission_description_logging:logs:read">
    <value>The user will have the ability to read all system logs.</value>
  </data>

</root>
