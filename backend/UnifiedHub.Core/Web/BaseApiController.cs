using ErrorOr;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using UnifiedHub.Core.Resources.Shared;

namespace UnifiedHub.Core.Web;

[ApiController]
public abstract class BaseApiController : ControllerBase
{
    protected string GetControllerRoute()
    {
        return RouteData.Values["controller"]!.ToString()!.ToLower();
    }

    protected ActionResult Problem(List<Error> errors)
    {
        return errors.Count is 0
            ? Problem()
            : errors.All(error => error.Type == ErrorType.Validation)
                ? ValidationProblem(errors)
                : Problem(errors[0]);
    }

    private ObjectResult Problem(Error error)
    {
        var statusCode = error.Type switch
        {
            ErrorType.Conflict => StatusCodes.Status409Conflict,
            ErrorType.Validation => StatusCodes.Status400BadRequest,
            ErrorType.Failure => StatusCodes.Status400BadRequest,
            ErrorType.NotFound => StatusCodes.Status404NotFound,
            ErrorType.Unauthorized => StatusCodes.Status403Forbidden,
            _ => StatusCodes.Status500InternalServerError,
        };

        return Problem(statusCode: statusCode, title: error.Description);
    }

    private ActionResult ValidationProblem(List<Error> errors)
    {
        var modelStateDictionary = new ModelStateDictionary();

        var l = HttpContext.RequestServices.GetRequiredService<IStringLocalizer<SharedResource>>();

        errors.ForEach(error => modelStateDictionary.AddModelError(error.Code, error.Description));
        return ValidationProblem(
            title: l["translate_one_or_more_validation_error_has_occurred"],
            modelStateDictionary: modelStateDictionary);
    }
}
