using System.Collections.Concurrent;
using Microsoft.Extensions.Caching.Memory;

namespace UnifiedHub.Core.Services.Caching;

public sealed class Cache(IMemoryCache cache) : ICache
{
    // Using nested ConcurrentDictionary for O(1) operations instead of arrays
    private readonly ConcurrentDictionary<string, ConcurrentDictionary<string, bool>> _stores = new();

    public async Task SetAsync<T>(
        string store,
        string key, T value,
        TimeSpan? expiry = null)
    {
        await Task.CompletedTask;

        var cacheKey = $"{store}:{key}";
        var options = new MemoryCacheEntryOptions();

        expiry ??= TimeSpan.FromHours(1);
        options.AbsoluteExpirationRelativeToNow = expiry;

        // Clean up tracking when cache item expires/is evicted
        options.RegisterPostEvictionCallback((_, _, _, state) =>
        {
            var (storeKey, itemKey) = ((string, string)) state!;

            if (!_stores.TryGetValue(storeKey, out var storeKeys))
            {
                return;
            }

            storeKeys.TryRemove(itemKey, out _);

            // Remove empty stores to prevent memory bloat
            if (storeKeys.IsEmpty)
            {
                _stores.TryRemove(storeKey, out _);
            }
        }, (store, key));

        cache.Set(cacheKey, value, options);

        // Track the key in our store registry
        var storeDict = _stores.GetOrAdd(store, _ => new ConcurrentDictionary<string, bool>());
        storeDict.TryAdd(key, true);
    }


    public async Task<T?> GetAsync<T>(string store, string key)
    {
        await Task.CompletedTask;
        return cache.Get<T>($"{store}:{key}");
    }

    public async Task RemoveAsync(string store, string key)
    {
        await Task.CompletedTask;

        cache.Remove($"{store}:{key}");

        // Remove from tracking
        if (_stores.TryGetValue(store, out var storeKeys))
        {
            storeKeys.TryRemove(key, out _);

            // Clean up empty stores
            if (storeKeys.IsEmpty)
            {
                _stores.TryRemove(store, out _);
            }
        }
    }

    public async Task ClearAsync(string store)
    {
        await Task.CompletedTask;

        // Remove the entire store from tracking first
        if (_stores.TryRemove(store, out var keys))
        {
            // Remove all cache entries for this store
            foreach (var key in keys.Keys)
            {
                cache.Remove($"{store}:{key}");
            }
        }
    }
}
