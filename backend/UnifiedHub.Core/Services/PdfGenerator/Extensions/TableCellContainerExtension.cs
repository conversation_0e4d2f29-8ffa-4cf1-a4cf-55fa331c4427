using QuestPDF.Elements.Table;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;

namespace UnifiedHub.Core.Services.PdfGenerator.Extensions;

public static class TableCellContainerExtension
{
    public static IContainer Data(this ITableCellContainer cell)
    {
        return cell.Background(Colors.White)
            .Border(0.5f)
            .PaddingHorizontal(5)
            .PaddingVertical(2);
    }

    public static IContainer Header(this ITableCellContainer cell)
    {
        return cell.DefaultTextStyle(x => x.Bold())
            .Background(Colors.Brown.Lighten5)
            .Border(0.5f)
            .PaddingHorizontal(5)
            .PaddingVertical(2);
    }
}
